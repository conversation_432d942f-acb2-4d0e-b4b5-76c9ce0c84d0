import React from 'react';
import './globals.css'
import type { Metadata } from 'next'
import { AuthProvider } from '../contexts/AuthContext'
import { LocaleProvider } from '../contexts/LocaleContext'
import ErrorBoundary from '../components/ErrorBoundary'

export const metadata: Metadata = {
  title: '键帽设计器 - 自定义您的键盘',
  description: '设计、分享和购买定制键帽的在线平台',
}

// 这些路径下使用自定义布局，不显示全局导航和页脚
const dashboardPaths = [
  '/dashboard',
  '/designer',
  '/materials',
  '/designs',
  '/profile',
  '/settings'
];

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body>
        <ErrorBoundary>
          <LocaleProvider>
            <AuthProvider>
              {children}
            </AuthProvider>
          </LocaleProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}
