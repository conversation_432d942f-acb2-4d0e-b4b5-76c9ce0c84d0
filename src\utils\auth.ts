// 用户类型枚举
export enum UserType {
  ADMIN = 'admin',
  USER = 'user',
  GUEST = 'guest'
}

// 检查当前用户类型
export function getCurrentUserType(): UserType {
  if (typeof window === 'undefined') return UserType.GUEST;
  
  const userType = localStorage.getItem('user_type');
  
  switch (userType) {
    case 'admin':
      return UserType.ADMIN;
    case 'user':
      return UserType.USER;
    default:
      return UserType.GUEST;
  }
}

// 检查是否为管理员
export function isAdmin(): boolean {
  return getCurrentUserType() === UserType.ADMIN;
}

// 检查是否为普通用户
export function isUser(): boolean {
  return getCurrentUserType() === UserType.USER;
}

// 检查是否已登录
export function isLoggedIn(): boolean {
  const userType = getCurrentUserType();
  return userType === UserType.ADMIN || userType === UserType.USER;
}

// 获取管理员信息
export function getAdminInfo() {
  if (!isAdmin()) return null;
  
  try {
    const adminData = localStorage.getItem('admin');
    return adminData ? JSON.parse(adminData) : null;
  } catch (error) {
    console.error('解析管理员数据失败:', error);
    return null;
  }
}

// 获取用户信息
export function getUserInfo() {
  if (!isUser()) return null;
  
  try {
    const userData = localStorage.getItem('user');
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('解析用户数据失败:', error);
    return null;
  }
}

// 清除所有登录信息
export function clearAllAuth() {
  localStorage.removeItem('admin');
  localStorage.removeItem('admin_token');
  localStorage.removeItem('user');
  localStorage.removeItem('token');
  localStorage.removeItem('user_type');
}

// 管理员登出
export function adminLogout() {
  localStorage.removeItem('admin');
  localStorage.removeItem('admin_token');
  localStorage.removeItem('user_type');
}

// 用户登出
export function userLogout() {
  localStorage.removeItem('user');
  localStorage.removeItem('token');
  localStorage.removeItem('user_type');
} 