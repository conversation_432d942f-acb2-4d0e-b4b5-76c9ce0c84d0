'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  id: string;
  username: string;
  nickname?: string;
  email: string;
  avatar?: string;
  avatarPath?: string;
}

interface AuthContextType {
  user: User | null;
  login: (account: string, password: string) => Promise<boolean>;
  register: (email: string, password: string, username: string, nickname: string, phoneNumber?: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
  updateUserProfile: (userData: Partial<User>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // 验证token是否有效
  const validateToken = async (token: string): Promise<boolean> => {
    try {
      // 检查token格式（JWT应该有两个点分隔符）
      const tokenParts = token.split('.');
      if (tokenParts.length !== 3) {
        console.warn('Token格式无效');
        return false;
      }

      // 简单验证：检查token是否过期（从JWT payload中获取exp字段）
      try {
        // 对JWT payload进行安全的base64解码
        let payloadBase64 = tokenParts[1];
        
        // 添加必要的padding
        while (payloadBase64.length % 4) {
          payloadBase64 += '=';
        }
        
        // 尝试解码base64
        let decodedPayload;
        try {
          decodedPayload = atob(payloadBase64);
        } catch (decodeError) {
          console.warn('Token base64解码失败:', decodeError);
          // 如果解码失败，但token看起来是正确的格式，暂时认为有效
          return true;
        }
        
        // 尝试解析JSON
        const payload = JSON.parse(decodedPayload);
        const currentTime = Date.now() / 1000;
        
        // 如果token即将过期（还有5分钟），仍然认为有效，但给出警告
        if (payload.exp && payload.exp > currentTime - 300) {
          if (payload.exp <= currentTime) {
            console.warn('Token已过期，但在宽限期内');
          }
          return true;
        } else {
          console.warn('Token已过期超过宽限期');
          return false;
        }
      } catch (e) {
        console.warn('Token解析失败，但不强制清除:', e);
        // 即使解析失败，也暂时保留token，可能是网络问题
        return true;
      }
    } catch (error) {
      console.error('Token验证失败:', error);
      return false;
    }
  };

  // 检查本地存储的登录状态
  useEffect(() => {
    const initializeAuth = async () => {
      console.log('初始化认证状态...');
      setIsLoading(true);
      
      try {
        const savedUser = localStorage.getItem('user');
        const savedToken = localStorage.getItem('token');
        
        console.log('从localStorage读取:', {
          hasUser: !!savedUser,
          hasToken: !!savedToken,
          tokenPreview: savedToken ? `${savedToken.substring(0, 20)}...` : 'null'
        });

        if (savedUser && savedToken) {
          // 验证token是否有效
          const isTokenValid = await validateToken(savedToken);
          
          if (isTokenValid) {
            const userData = JSON.parse(savedUser);
            console.log('恢复用户状态:', userData);
            setUser(userData);
            setIsAuthenticated(true);
          } else {
            console.warn('Token已过期或无效，清除认证状态');
            localStorage.removeItem('user');
            localStorage.removeItem('token');
            setUser(null);
            setIsAuthenticated(false);
          }
        } else {
          console.log('未找到有效的认证信息');
          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('初始化认证状态失败:', error);
        setUser(null);
        setIsAuthenticated(false);
        // 清除可能损坏的数据
        localStorage.removeItem('user');
        localStorage.removeItem('token');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (account: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // 调用后端登录API
      const response = await fetch('http://localhost:8080/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ account, password, rememberMe: false }),
      });

      console.log('登录响应状态:', response.status);
      
      // 先打印原始响应文本
      const responseText = await response.text();
      console.log('登录原始响应文本:', responseText);
      
      // 解析JSON
      let result;
      try {
        result = JSON.parse(responseText);
      } catch (e) {
        console.error('解析登录响应JSON失败:', e);
        return false;
      }
      
      console.log('登录后端返回结果:', result);
      
      if (result.code === 200) {
        // 登录成功，设置用户信息
        const userData = result.data;
        const user: User = {
          id: userData.userId,
          username: userData.username,
          nickname: userData.nickname,
          email: userData.email,
          avatar: userData.avatarPath,
          avatarPath: userData.avatarPath
        };
        setUser(user);
        setIsAuthenticated(true);
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('token', userData.accessToken);
        console.log('登录成功，已保存用户信息');
        return true;
      } else {
        console.error('登录失败:', result.message);
        setIsAuthenticated(false);
        return false;
      }
    } catch (error) {
      console.error('登录失败:', error);
      setIsAuthenticated(false);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (email: string, password: string, username: string, nickname: string, phoneNumber?: string): Promise<boolean> => {
    try {
      console.log('开始注册，参数:', { email, username, nickname, phoneNumber });
      
      // 调用后端注册API
      const response = await fetch('http://localhost:8080/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email, 
          password, 
          username,
          nickname,
          phoneNumber: phoneNumber || ''
        }),
      });

      console.log('响应状态:', response.status);
      
      // 先打印原始响应文本
      const responseText = await response.text();
      console.log('原始响应文本:', responseText);
      
      // 解析JSON
      let result;
      try {
        result = JSON.parse(responseText);
      } catch (e) {
        console.error('解析响应JSON失败:', e);
        return false;
      }
      
      console.log('后端返回结果:', result);
      console.log('result.code:', result.code);
      console.log('result.data:', result.data);
      
      if (result.code === 200) {
        // 注册成功，设置用户信息
        const userData = result.data;
        console.log('用户数据:', userData);
        
        // 构建用户信息
        const user: User = {
          id: userData.userId,
          username: userData.username,
          nickname: userData.nickname,
          email: userData.email,
          avatar: userData.avatarPath,
          avatarPath: userData.avatarPath
        };
        
        // 设置用户状态
        setUser(user);
        setIsAuthenticated(true);
        localStorage.setItem('user', JSON.stringify(user));
        
        // 如果有Token就保存，没有也继续（后端问题时的兼容处理）
        if (userData.accessToken) {
          localStorage.setItem('token', userData.accessToken);
          console.log('注册成功，已保存用户信息和Token');
        } else {
          console.warn('注册成功但未收到Token，已保存用户信息');
        }
        
        console.log('注册成功，返回true');
        return true;
      } else {
        console.error('注册失败:', result.message);
        setIsAuthenticated(false);
        return false;
      }
    } catch (error) {
      console.error('注册异常:', error);
      setIsAuthenticated(false);
      return false;
    }
  };

  const logout = () => {
    console.log('用户退出登录');
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('user');
    localStorage.removeItem('token');
  };

  // 新增方法：更新用户资料
  const updateUserProfile = (userData: Partial<User>) => {
    if (!user) return;

    const updatedUser = { ...user, ...userData };
    setUser(updatedUser);
    localStorage.setItem('user', JSON.stringify(updatedUser));
  };

  const contextValue = {
    user,
    login,
    register,
    logout,
    isLoading,
    isAuthenticated,
    updateUserProfile
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 