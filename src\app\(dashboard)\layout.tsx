'use client';

import React, { useEffect } from 'react';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  useEffect(() => {
    // 移除透明文字修复脚本，因为它导致页面闪烁
    // 改为只在必要时使用CSS修复
    console.log('Dashboard layout mounted - 已移除透明文字修复脚本');
    
    // 不再使用定时器和外部脚本
    return () => {
      console.log('Dashboard layout unmounted');
    };
  }, []);
  
  // 使用深色主题的布局
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#2a1f3e] via-[#1e1432] to-[#0f0a1a]">
      {children}
    </div>
  );
} 