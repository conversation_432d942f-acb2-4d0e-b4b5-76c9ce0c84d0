'use client';

import React, { useState, useEffect } from 'react';

interface AddressPickerProps {
  value: {
    province: string;
    city: string;
    district: string;
  };
  onChange: (address: { province: string; city: string; district: string }) => void;
  className?: string;
}

// 简化的地址数据
const ADDRESS_DATA: Record<string, Record<string, string[]>> = {
  '北京市': {
    '北京市': ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区']
  },
  '上海市': {
    '上海市': ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '闵行区', '宝山区', '嘉定区', '浦东新区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区']
  },
  '广东省': {
    '广州市': ['荔湾区', '越秀区', '海珠区', '天河区', '白云区', '黄埔区', '番禺区', '花都区', '南沙区', '从化区', '增城区'],
    '深圳市': ['罗湖区', '福田区', '南山区', '宝安区', '龙岗区', '盐田区', '龙华区', '坪山区', '光明区', '大鹏新区'],
    '珠海市': ['香洲区', '斗门区', '金湾区']
  },
  '江苏省': {
    '南京市': ['玄武区', '秦淮区', '建邺区', '鼓楼区', '浦口区', '栖霞区', '雨花台区', '江宁区', '六合区', '溧水区', '高淳区'],
    '苏州市': ['虎丘区', '吴中区', '相城区', '姑苏区', '吴江区', '常熟市', '张家港市', '昆山市', '太仓市']
  },
  '浙江省': {
    '杭州市': ['上城区', '下城区', '江干区', '拱墅区', '西湖区', '滨江区', '萧山区', '余杭区', '富阳区', '临安区'],
    '宁波市': ['海曙区', '江北区', '北仑区', '镇海区', '鄞州区', '奉化区', '余姚市', '慈溪市', '象山县', '宁海县']
  }
};

const AddressPicker: React.FC<AddressPickerProps> = ({ value, onChange, className = '' }) => {
  const [provinces] = useState(Object.keys(ADDRESS_DATA));
  const [cities, setCities] = useState<string[]>([]);
  const [districts, setDistricts] = useState<string[]>([]);

  // 当省份改变时更新城市列表
  useEffect(() => {
    if (value.province && ADDRESS_DATA[value.province as keyof typeof ADDRESS_DATA]) {
      const provinceData = ADDRESS_DATA[value.province as keyof typeof ADDRESS_DATA];
      setCities(Object.keys(provinceData));
      
      // 清空城市和区县
      if (!provinceData[value.city as keyof typeof provinceData]) {
        onChange({
          province: value.province,
          city: '',
          district: ''
        });
      }
    } else {
      setCities([]);
      setDistricts([]);
    }
  }, [value.province]);

  // 当城市改变时更新区县列表
  useEffect(() => {
    if (value.province && value.city && ADDRESS_DATA[value.province as keyof typeof ADDRESS_DATA]) {
      const provinceData = ADDRESS_DATA[value.province as keyof typeof ADDRESS_DATA];
      const cityData = provinceData[value.city as keyof typeof provinceData];
      if (cityData) {
        setDistricts(cityData);
        
        // 如果当前区县不在新列表中，清空区县
        if (value.district && !cityData.includes(value.district)) {
          onChange({
            province: value.province,
            city: value.city,
            district: ''
          });
        }
      } else {
        setDistricts([]);
      }
    } else {
      setDistricts([]);
    }
  }, [value.city]);

  const handleProvinceChange = (province: string) => {
    onChange({
      province,
      city: '',
      district: ''
    });
  };

  const handleCityChange = (city: string) => {
    onChange({
      province: value.province,
      city,
      district: ''
    });
  };

  const handleDistrictChange = (district: string) => {
    onChange({
      province: value.province,
      city: value.city,
      district
    });
  };

  return (
    <div className={`flex space-x-2 ${className}`}>
      {/* 省份选择 */}
      <select
        value={value.province}
        onChange={(e) => handleProvinceChange(e.target.value)}
        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
      >
        <option value="">选择省份</option>
        {provinces.map(province => (
          <option key={province} value={province}>{province}</option>
        ))}
      </select>

      {/* 城市选择 */}
      <select
        value={value.city}
        onChange={(e) => handleCityChange(e.target.value)}
        disabled={!value.province}
        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed text-gray-800"
      >
        <option value="">选择城市</option>
        {cities.map(city => (
          <option key={city} value={city}>{city}</option>
        ))}
      </select>

      {/* 区县选择 */}
      <select
        value={value.district}
        onChange={(e) => handleDistrictChange(e.target.value)}
        disabled={!value.city}
        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed text-gray-800"
      >
        <option value="">选择区县</option>
        {districts.map(district => (
          <option key={district} value={district}>{district}</option>
        ))}
      </select>
    </div>
  );
};

export default AddressPicker; 