'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';

// 模拟待支付订单数据
const mockPendingOrders = [
  {
    id: 'ORD004',
    orderNumber: '2024031604',
    date: '2024-03-16',
    status: '待支付',
    statusColor: 'text-orange-600 bg-orange-100',
    paymentDeadline: '2024-03-16 18:30:00',
    items: [
      {
        id: 1,
        name: 'Wooting 80HE 磁轴键盘',
        image: '🎯',
        price: 2199,
        originalPrice: 2499,
        quantity: 1,
        category: 'Wooting',
        style: '电竞游戏',
        discount: 12
      }
    ],
    totalAmount: 2199,
    paymentMethods: ['支付宝', '微信支付', '银行卡']
  },
  {
    id: 'ORD005',
    orderNumber: '2024031605',
    date: '2024-03-16',
    status: '待支付',
    statusColor: 'text-orange-600 bg-orange-100',
    paymentDeadline: '2024-03-16 20:45:00',
    items: [
      {
        id: 2,
        name: 'Carbon 大侠 R3 PBT键帽套装',
        image: '🎯',
        price: 399,
        originalPrice: 499,
        quantity: 1,
        category: 'PBT',
        style: '复古经典',
        discount: 20
      },
      {
        id: 3,
        name: 'GMK CYL Hi Viz 高可视二色',
        image: '🎯',
        price: 1179,
        originalPrice: 1299,
        quantity: 1,
        category: 'GMK',
        style: '高对比',
        discount: 9
      }
    ],
    totalAmount: 1578,
    paymentMethods: ['支付宝', '微信支付', '银行卡']
  }
];

export default function PendingOrdersPage() {
  const [timeLeft, setTimeLeft] = useState<{[key: string]: string}>({});

  // 计算倒计时
  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date().getTime();
      const newTimeLeft: {[key: string]: string} = {};

      mockPendingOrders.forEach(order => {
        const deadline = new Date(order.paymentDeadline).getTime();
        const difference = deadline - now;

        if (difference > 0) {
          const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((difference % (1000 * 60)) / 1000);
          newTimeLeft[order.id] = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        } else {
          newTimeLeft[order.id] = '已过期';
        }
      });

      setTimeLeft(newTimeLeft);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-8">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                  L
                </div>
                <span className="text-xl font-bold text-gray-900">灵狐键创</span>
              </Link>
              <div className="hidden md:flex items-center space-x-6">
                <Link href="/shop" className="text-gray-600 hover:text-gray-900 transition-colors">
                  商城
                </Link>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="bg-gradient-to-r from-purple-600 to-cyan-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* 面包屑导航 */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Link href="/shop" className="hover:text-gray-700">商城</Link>
            <span>›</span>
            <Link href="/orders/pending" className="text-purple-600 font-medium">待支付订单</Link>
          </div>
        </div>
      </div>

      {/* 订单标签页 */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <Link href="/orders/paid" className="py-4 text-gray-500 hover:text-gray-700 transition-colors">
              ✅ 已支付订单
            </Link>
            <Link href="/orders/pending" className="py-4 border-b-2 border-orange-600 text-orange-600 font-medium">
              ⏳ 待支付订单
            </Link>
            <Link href="/cart" className="py-4 text-gray-500 hover:text-gray-700 transition-colors">
              🛒 购物车
            </Link>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">待支付订单</h1>
          <p className="text-gray-600 mt-2">请尽快完成支付，逾期订单将自动取消</p>
        </div>

        {mockPendingOrders.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">⏰</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">暂无待支付订单</h3>
            <p className="text-gray-500 mb-6">您没有需要支付的订单</p>
            <Link href="/shop" className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
              去购物
            </Link>
          </div>
        ) : (
          <div className="space-y-6">
            {mockPendingOrders.map((order) => (
              <div key={order.id} className="bg-white rounded-lg shadow-md overflow-hidden border-l-4 border-orange-500">
                {/* 订单头部 */}
                <div className="px-6 py-4 bg-orange-50 border-b flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div>
                      <p className="font-medium text-gray-900">订单号: {order.orderNumber}</p>
                      <p className="text-sm text-gray-500">下单时间: {order.date}</p>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${order.statusColor}`}>
                      {order.status}
                    </span>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-lg text-red-600">¥{order.totalAmount}</p>
                    <p className="text-sm text-gray-500">共{order.items.length}件商品</p>
                  </div>
                </div>

                {/* 支付倒计时 */}
                <div className="px-6 py-3 bg-red-50 border-b">
                  <div className="flex items-center justify-center space-x-2">
                    <span className="text-red-600 font-medium">⏰ 剩余支付时间:</span>
                    <span className="text-red-700 font-bold text-lg">
                      {timeLeft[order.id] || '计算中...'}
                    </span>
                    <span className="text-red-600 text-sm">
                      (逾期将自动取消订单)
                    </span>
                  </div>
                </div>

                {/* 商品列表 */}
                <div className="px-6 py-4">
                  <div className="space-y-4">
                    {order.items.map((item) => (
                      <div key={item.id} className="flex items-center space-x-4">
                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center text-2xl">
                          {item.image}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900">{item.name}</h3>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                              {item.category}
                            </span>
                            <span className="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded">
                              {item.style}
                            </span>
                            {item.discount && (
                              <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded">
                                -{item.discount}%
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-gray-900">¥{item.price}</span>
                            {item.originalPrice && item.originalPrice > item.price && (
                              <span className="text-sm text-gray-400 line-through">¥{item.originalPrice}</span>
                            )}
                          </div>
                          <p className="text-sm text-gray-500">x{item.quantity}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 支付方式选择 */}
                <div className="px-6 py-4 bg-gray-50 border-t border-b">
                  <div className="flex items-center space-x-4">
                    <span className="text-gray-700 font-medium">支付方式:</span>
                    <div className="flex space-x-3">
                      {order.paymentMethods.map((method) => (
                        <button
                          key={method}
                          className="px-4 py-2 border border-gray-300 rounded-lg hover:border-purple-500 hover:text-purple-600 transition-colors"
                        >
                          {method === '支付宝' && '💙'}
                          {method === '微信支付' && '💚'}
                          {method === '银行卡' && '💳'}
                          {method}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="px-6 py-4 border-t bg-gray-50">
                  <div className="flex items-center justify-end space-x-3">
                    <button className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors">
                      取消订单
                    </button>
                    <button className="px-4 py-2 text-purple-600 border border-purple-300 rounded-lg hover:bg-purple-50 transition-colors">
                      修改订单
                    </button>
                    <button className="px-6 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-lg hover:shadow-lg transition-shadow font-medium">
                      立即支付 ¥{order.totalAmount}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 