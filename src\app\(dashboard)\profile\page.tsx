'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '../../../contexts/AuthContext';

export default function ProfilePage() {
  const { updateUserProfile } = useAuth();

  const [user, setUser] = useState({
    username: '',
    nickname: '',
    email: '',
    avatar: 'U',
    avatarPath: '',
    userTitle: '初级设计师',
    joinDate: '2024-01-01',
    worksCount: 0,
    shopStatus: '未开通',
    hasShop: false,
    bio: '',
    designAdvantage: '',
    followingCount: 0,
    followersCount: 0
  });

  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    username: '',
    nickname: '',
    email: ''
  });

  // 头像上传相关状态
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [showAvatarModal, setShowAvatarModal] = useState(false);

  // 获取用户信息
  useEffect(() => {
    const loadUserProfile = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) return;

        const userData = localStorage.getItem('user');
        if (!userData) return;

        const userInfo = JSON.parse(userData);

        // 获取真实的用户资料（调用当前用户资料API）
        const response = await fetch(`http://localhost:8080/api/users/profile`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const result = await response.json();
          if (result.code === 200) {
            const profile = result.data;
            
            // 获取真实的店铺状态
            const shopResponse = await fetch('http://localhost:8080/api/shop/my-shop', {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });

            let shopStatus = '未开通';
            let hasShop = false;
            if (shopResponse.ok) {
              const shopResult = await shopResponse.json();
              if (shopResult.code === 200 && shopResult.data) {
                const shop = shopResult.data;
                hasShop = true;
                switch (shop.status) {
                  case 'PENDING':
                    shopStatus = '审核中';
                    break;
                  case 'APPROVED':
                    shopStatus = '已开通';
                    break;
                  case 'REJECTED':
                    shopStatus = '审核未通过';
                    break;
                  default:
                    shopStatus = '未知状态';
                }
              }
            }

            const fullUser = {
              username: profile.username || '创意设计师',
              nickname: profile.nickname || profile.username || '创意设计师',
              email: userInfo.email || '未绑定邮箱',
              avatar: (profile.nickname || profile.username) ? (profile.nickname || profile.username).charAt(0).toUpperCase() : 'U',
              avatarPath: profile.avatarPath || '',
              userTitle: profile.userTitle || '初级设计师',
              joinDate: profile.createTime ? new Date(profile.createTime).toISOString().split('T')[0] : (userInfo.createTime ? new Date(userInfo.createTime).toISOString().split('T')[0] : '2024-01-01'),
              worksCount: profile.postCount || 0,
              shopStatus: shopStatus,
              hasShop: hasShop,
              bio: profile.bio || '',
              designAdvantage: profile.designAdvantage || '',
              followingCount: profile.followingCount || 0,
              followersCount: profile.followersCount || 0
            };
            
            // 更新当前组件状态
            setUser(fullUser);
            setEditForm({
              username: fullUser.username,
              nickname: fullUser.nickname,
              email: fullUser.email
            });
            
            // 每次加载时，同步更新localStorage中的用户信息
            const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
            const updatedUser = {
              ...currentUser,
              username: profile.username,
              nickname: profile.nickname,
              avatarPath: profile.avatarPath,
              userTitle: profile.userTitle
            };
            localStorage.setItem('user', JSON.stringify(updatedUser));
            
            // 更新全局用户状态
            updateUserProfile({
              username: profile.username,
              nickname: profile.nickname,
              avatarPath: profile.avatarPath
            });
          }
        }
      } catch (error) {
        console.error('获取用户资料失败:', error);
        // 使用本地存储的基本信息作为备选
        const userData = localStorage.getItem('user');
        if (userData) {
          const userInfo = JSON.parse(userData);
          const fullUser = {
            username: userInfo.username || '创意设计师',
            nickname: userInfo.nickname || userInfo.username || '创意设计师',
            email: userInfo.email || '未绑定邮箱',
            avatar: (userInfo.nickname || userInfo.username) ? (userInfo.nickname || userInfo.username).charAt(0).toUpperCase() : 'U',
            avatarPath: userInfo.avatarPath || '',
            userTitle: userInfo.userTitle || '初级设计师',
            joinDate: userInfo.createTime ? new Date(userInfo.createTime).toISOString().split('T')[0] : '2024-01-01',
            worksCount: 0,
            shopStatus: '未开通',
            hasShop: false,
            bio: userInfo.bio || '',
            designAdvantage: userInfo.designAdvantage || '',
            followingCount: 0,
            followersCount: 0
          };
          setUser(fullUser);
          setEditForm({
            username: fullUser.username,
            nickname: fullUser.nickname,
            email: fullUser.email
          });
        }
      }
    };

    if (typeof window !== 'undefined') {
      loadUserProfile();
    }
  }, []);

  const handleSave = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      // 调用后端API保存用户信息
      const response = await fetch('http://localhost:8080/api/users/update-basic-info', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          username: editForm.username,
          nickname: editForm.nickname,
          bio: user.bio || '',
          designAdvantage: user.designAdvantage || ''
        })
      });

      if (!response.ok) {
        throw new Error('保存失败，请重试');
      }

      const result = await response.json();
      if (result.code !== 200) {
        throw new Error(result.message || '保存失败，请重试');
      }

      // 更新localStorage中的用户信息
      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
      const updatedUser = {
        ...currentUser,
        username: editForm.username,
        nickname: editForm.nickname,
        email: editForm.email,
        bio: user.bio || '',
        designAdvantage: user.designAdvantage || ''
      };
      localStorage.setItem('user', JSON.stringify(updatedUser));
      
      // 更新状态
      setUser(prev => ({
        ...prev,
        username: editForm.username,
        nickname: editForm.nickname,
        email: editForm.email,
        avatar: (editForm.nickname || editForm.username).charAt(0).toUpperCase(),
        bio: user.bio || '',
        designAdvantage: user.designAdvantage || ''
      }));
      
      // 更新全局用户状态
      updateUserProfile({
        username: editForm.username,
        nickname: editForm.nickname
      });
      
      setIsEditing(false);
      alert('保存成功！');
    } catch (error) {
      console.error('保存用户信息失败:', error);
      alert(error instanceof Error ? error.message : '保存失败，请重试');
    }
  };

  // 处理头像上传
  const handleAvatarUpload = async (file: File) => {
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      alert('请选择图片文件');
      return;
    }

    // 验证文件大小（5MB）
    if (file.size > 5 * 1024 * 1024) {
      alert('图片大小不能超过5MB');
      return;
    }

    setIsUploadingAvatar(true);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('http://localhost:8080/api/users/upload-avatar', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          const avatarPath = result.data.avatarPath;
          const avatarUrl = result.data.avatarUrl;

          // 更新用户状态
          setUser(prev => ({
            ...prev,
            avatarPath: avatarPath
          }));

          // 更新localStorage
          const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
          const updatedUser = {
            ...currentUser,
            avatarPath: avatarPath
          };
          localStorage.setItem('user', JSON.stringify(updatedUser));

          setShowAvatarModal(false);
          alert('头像上传成功！');
        } else {
          alert(result.message || '头像上传失败');
        }
      } else {
        alert('头像上传失败，请重试');
      }
    } catch (error) {
      console.error('头像上传失败:', error);
      alert('头像上传失败，请重试');
    } finally {
      setIsUploadingAvatar(false);
    }
  };

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleAvatarUpload(file);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航面包屑 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <nav className="flex items-center space-x-2 text-sm">
          <Link href="/dashboard" className="text-gray-500 hover:text-gray-700">
            仪表盘
          </Link>
          <span className="text-gray-400">&gt;</span>
          <span className="text-gray-900">个人信息</span>
        </nav>
      </div>

      <div className="max-w-4xl mx-auto py-8 px-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          {/* 头部 */}
          <div className="bg-gradient-to-r from-cyan-500 to-blue-600 px-8 py-12">
            <div className="flex items-center space-x-6">
              {/* 头像区域 */}
              <div className="relative group">
                <div className="w-24 h-24 rounded-full overflow-hidden bg-white/20 backdrop-blur-sm flex items-center justify-center">
                  {user.avatarPath ? (
                    <img 
                      src={`http://localhost:8080${user.avatarPath}`} 
                      alt="用户头像" 
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        // 如果图片加载失败，显示默认头像
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.parentElement?.querySelector('.fallback-avatar') as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                    />
                  ) : null}
                  <div 
                    className={`w-full h-full text-white font-bold text-3xl flex items-center justify-center fallback-avatar ${user.avatarPath ? 'hidden' : ''}`}
                  >
                    {user.avatar}
                  </div>
                </div>
                
                {/* 头像上传按钮 */}
                <button
                  onClick={() => setShowAvatarModal(true)}
                  className="absolute inset-0 w-24 h-24 rounded-full bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                >
                  <span className="text-white text-sm">更换头像</span>
                </button>
              </div>
              
              <div className="text-white">
                <h1 className="text-3xl font-bold mb-2">{user.nickname || user.username}</h1>
                <p className="text-blue-100 mb-3">{user.email}</p>
                <div className="flex items-center space-x-4">
                  <span className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-sm">
                    {user.userTitle}
                  </span>
                  <span className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-sm">
                    {user.worksCount} 个作品
                  </span>
                  <span className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-sm">
                    店铺{user.shopStatus}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* 内容区域 */}
          <div className="p-8">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900">基本信息</h2>
              <button
                onClick={() => setIsEditing(!isEditing)}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                {isEditing ? '取消编辑' : '编辑信息'}
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 用户名 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  用户名
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editForm.username}
                    onChange={(e) => setEditForm(prev => ({ ...prev, username: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                ) : (
                  <div className="px-4 py-3 bg-gray-50 rounded-lg text-gray-900">
                    {user.username}
                  </div>
                )}
              </div>

              {/* 昵称 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  昵称
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editForm.nickname}
                    onChange={(e) => setEditForm(prev => ({ ...prev, nickname: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                ) : (
                  <div className="px-4 py-3 bg-gray-50 rounded-lg text-gray-900">
                    {user.nickname || user.username}
                  </div>
                )}
              </div>

              {/* 邮箱 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  邮箱地址
                </label>
                {isEditing ? (
                  <input
                    type="email"
                    value={editForm.email}
                    onChange={(e) => setEditForm(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                ) : (
                  <div className="px-4 py-3 bg-gray-50 rounded-lg text-gray-900">
                    {user.email}
                  </div>
                )}
              </div>

              {/* 用户头衔 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  用户头衔
                </label>
                <div className="px-4 py-3 bg-gray-50 rounded-lg text-gray-900">
                  {user.userTitle}
                </div>
              </div>

              {/* 加入日期 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  加入日期
                </label>
                <div className="px-4 py-3 bg-gray-50 rounded-lg text-gray-900">
                  {user.joinDate}
                </div>
              </div>
            </div>

            {isEditing && (
              <div className="mt-8 flex justify-end space-x-4">
                <button
                  onClick={() => setIsEditing(false)}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleSave}
                  className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  保存修改
                </button>
              </div>
            )}
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-8">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">我的作品</p>
                <p className="text-2xl font-bold text-gray-900">{user.worksCount}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-2xl">🎨</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">我的关注</p>
                <p className="text-2xl font-bold text-gray-900">{user.followingCount || 0}</p>
              </div>
              <div className="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center">
                <span className="text-2xl">👥</span>
              </div>
            </div>
            <div className="mt-2">
              <Link 
                href="/following"
                className="text-sm text-pink-600 hover:text-pink-700 transition-colors"
              >
                查看我的关注 →
              </Link>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">粉丝数</p>
                <p className="text-2xl font-bold text-gray-900">{user.followersCount || 0}</p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <span className="text-2xl">⭐</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">店铺状态</p>
                <p className="text-lg font-bold text-gray-900">{user.shopStatus}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-2xl">🏪</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 头像上传模态框 */}
      {showAvatarModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-96 max-w-[90vw]">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-gray-900">更换头像</h3>
              <button
                onClick={() => setShowAvatarModal(false)}
                className="text-gray-400 hover:text-gray-600 text-2xl"
              >
                ×
              </button>
            </div>

            <div className="space-y-4">
              {/* 当前头像预览 */}
              <div className="text-center">
                <div className="w-32 h-32 mx-auto rounded-full overflow-hidden bg-gray-200 flex items-center justify-center mb-4">
                  {user.avatarPath ? (
                    <img 
                      src={`http://localhost:8080${user.avatarPath}`} 
                      alt="当前头像" 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <span className="text-4xl font-bold text-gray-500">{user.avatar}</span>
                  )}
                </div>
                <p className="text-sm text-gray-600">当前头像</p>
              </div>

              {/* 上传按钮 */}
              <div className="text-center">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="avatar-upload"
                  disabled={isUploadingAvatar}
                />
                <label
                  htmlFor="avatar-upload"
                  className={`inline-flex items-center px-6 py-3 rounded-lg font-medium cursor-pointer transition-colors ${
                    isUploadingAvatar
                      ? 'bg-gray-400 text-white cursor-not-allowed'
                      : 'bg-blue-500 hover:bg-blue-600 text-white'
                  }`}
                >
                  {isUploadingAvatar ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      上传中...
                    </>
                  ) : (
                    <>
                      📷 选择图片
                    </>
                  )}
                </label>
              </div>

              {/* 提示信息 */}
              <div className="text-xs text-gray-500 text-center space-y-1">
                <p>支持 JPG、PNG、GIF、WEBP 格式</p>
                <p>文件大小不超过 5MB</p>
                <p>建议尺寸：200x200 像素</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}