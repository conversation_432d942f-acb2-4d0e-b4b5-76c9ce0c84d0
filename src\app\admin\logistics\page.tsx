'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdmin } from '../../../contexts/AdminContext';
import Link from 'next/link';

export default function AdminLogistics() {
  const router = useRouter();
  const { admin, isLoading: adminLoading } = useAdmin();

  useEffect(() => {
    if (!adminLoading) {
      if (!admin) {
        router.push('/admin/login');
      }
    }
  }, [admin, adminLoading, router]);

  if (adminLoading) {
    return <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
    </div>;
  }

  if (!admin) {
    return null; // 会被useEffect重定向到登录页
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <nav className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-600 rounded-lg flex items-center justify-center text-white font-bold text-sm mr-3">
                管
              </Link>
              <h1 className="text-xl font-bold text-white">物流管理</h1>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        <aside className="w-64 bg-gray-800 min-h-screen pt-4">
          <nav className="px-4 space-y-2">
            <Link href="/admin/dashboard" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📊</span>
              仪表盘
            </Link>
            <Link href="/admin/users" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">👥</span>
              用户管理
            </Link>

            <Link href="/admin/products" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📦</span>
              商品管理
            </Link>
            <Link href="/admin/orders" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🛒</span>
              订单管理
            </Link>
            <Link href="/admin/customer-service" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">💬</span>
              客服中心
            </Link>
            <Link href="/admin/stores" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🏪</span>
              店铺管理
            </Link>
            <Link href="/admin/logistics" className="flex items-center px-4 py-2 text-white bg-red-600 rounded-lg">
              <span className="mr-3">🚚</span>
              物流管理
            </Link>
            <Link href="/admin/settings" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">⚙️</span>
              系统设置
            </Link>
          </nav>
        </aside>

        <main className="flex-1 p-6">
          <div className="bg-gray-800 rounded-lg p-8 border border-gray-700 text-center">
            <div className="text-6xl mb-4">🚚</div>
            <h2 className="text-2xl font-bold text-white mb-4">物流管理</h2>
            <p className="text-gray-400 mb-6">管理物流信息、追踪包裹状态、处理配送问题</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <div className="bg-gray-700 rounded-lg p-6">
                <div className="text-blue-400 text-3xl mb-3">📋</div>
                <h3 className="text-white font-bold mb-2">待发货</h3>
                <div className="text-2xl font-bold text-blue-400">8</div>
                <p className="text-gray-400 text-sm">等待安排发货</p>
              </div>
              
              <div className="bg-gray-700 rounded-lg p-6">
                <div className="text-orange-400 text-3xl mb-3">🚛</div>
                <h3 className="text-white font-bold mb-2">运输中</h3>
                <div className="text-2xl font-bold text-orange-400">15</div>
                <p className="text-gray-400 text-sm">正在配送</p>
              </div>
              
              <div className="bg-gray-700 rounded-lg p-6">
                <div className="text-red-400 text-3xl mb-3">⚠️</div>
                <h3 className="text-white font-bold mb-2">配送异常</h3>
                <div className="text-2xl font-bold text-red-400">2</div>
                <p className="text-gray-400 text-sm">需要处理</p>
              </div>
            </div>

            <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-700 rounded-lg p-6 text-left">
                <h3 className="text-white font-bold mb-4">快递公司管理</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">顺丰速运</span>
                    <span className="text-green-400">正常</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">中通快递</span>
                    <span className="text-green-400">正常</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">圆通速递</span>
                    <span className="text-yellow-400">延迟</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-700 rounded-lg p-6 text-left">
                <h3 className="text-white font-bold mb-4">物流统计</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">今日发货</span>
                    <span className="text-white">12 单</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">平均配送时间</span>
                    <span className="text-white">2.5 天</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">配送成功率</span>
                    <span className="text-green-400">98.5%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
} 
    </div>
  );
} 