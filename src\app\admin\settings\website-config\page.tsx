'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdmin } from '../../../../contexts/AdminContext';
import Link from 'next/link';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

interface WebsiteSettings {
  id: number;
  configKey: string;
  configValue: string;
  configType: string;
  category: string;
  description: string;
  isActive: boolean;
}

interface SettingsVersion {
  id: number;
  versionName: string;
  isPublished: boolean;
  isDraft: boolean;
  publishedAt: string;
  createdAt: string;
}

// 产品类型定义
interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  icon?: string;
  gradient?: string;
  imageUrl?: string;
  videoUrl?: string;
}

// 功能特点类型定义
interface Feature {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
}

export default function WebsiteConfigPage() {
  const router = useRouter();
  const { admin, isLoading: adminLoading } = useAdmin();
  const [settings, setSettings] = useState<Record<string, WebsiteSettings[]>>({});
  const [versions, setVersions] = useState<SettingsVersion[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('hero');
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [versionName, setVersionName] = useState('');
  const [previewMode, setPreviewMode] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const [tabs, setTabs] = useState<string[]>([]);
  const [version, setVersion] = useState('');
  const [versionHistory, setVersionHistory] = useState<any[]>([]);
  const [showHistory, setShowHistory] = useState(false);

  // 分类配置
  const categories = [
    { key: 'hero', name: '首页横幅', icon: '🎯' },
    { key: 'products', name: '产品展示', icon: '📦' },
    { key: 'features', name: '功能特点', icon: '⚡' },
    { key: 'styles', name: '设计风格', icon: '🎨' },
    { key: 'cta', name: 'CTA区域', icon: '🚀' },
    { key: 'footer', name: '页脚信息', icon: '📄' },
    { key: 'site', name: '网站信息', icon: '🌐' }
  ];
  
  // 产品管理相关状态
  const [productConfigs, setProductConfigs] = useState<Product[]>([]);
  const [showProductEditor, setShowProductEditor] = useState(false);
  const [currentProduct, setCurrentProduct] = useState<Product | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [uploadingVideo, setUploadingVideo] = useState(false);
  const [uploadingPromoVideo, setUploadingPromoVideo] = useState(false);
  
  // 功能特点相关状态
  const [featureConfigs, setFeatureConfigs] = useState<Feature[]>([]);
  const [showFeatureEditor, setShowFeatureEditor] = useState(false);
  const [currentFeature, setCurrentFeature] = useState<Feature | null>(null);
  const [promotionalVideoUrl, setPromotionalVideoUrl] = useState('');

  useEffect(() => {
    if (!adminLoading) {
      if (admin) {
        loadSettings();
        loadVersions();
      } else {
        router.push('/admin/login');
      }
    }
  }, [admin, adminLoading, router]);

  const loadSettings = async () => {
    try {
      setLoading(true);
      console.log('开始加载网站配置...');
      
      // 添加管理员token到请求头
      const token = localStorage.getItem('admin_token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      const response = await fetch('http://localhost:8080/api/admin/website-settings', {
        headers
      });
      
      console.log('API响应状态:', response.status, response.statusText);
      
      if (!response.ok) {
        throw new Error(`HTTP错误 ${response.status}: ${response.statusText}`);
      }
      
      const responseText = await response.text();
      console.log('API原始响应文本长度:', responseText.length);
      console.log('API原始响应前500字符:', responseText.substring(0, 500));
      
      // 解析JSON
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        console.error('JSON解析失败:', e);
        console.error('尝试解析的文本:', responseText);
        throw new Error('服务器返回的数据格式错误');
      }
      
      console.log('API解析后数据结构:', typeof data);
      console.log('API解析后数据的keys:', Object.keys(data));
      console.log('data.success:', data.success);
      console.log('data.message:', data.message);
      console.log('data.code:', data.code);
      console.log('data.data存在:', !!data.data);
      console.log('data.data类型:', typeof data.data);
      
      // 更宽松的成功检查 - 支持不同的后端响应格式
      const isSuccess = data.success === true || data.success === 'true' || data.code === 200;
      
      if (!isSuccess) {
        console.error('API返回失败标志:', { success: data.success, code: data.code, message: data.message });
        throw new Error(data.message || '获取配置失败');
      }
      
      // 检查数据结构 - 更宽松的检查
      if (!data.data) {
        console.error('data.data不存在或为空:', data.data);
        throw new Error('API返回的数据中没有data字段');
      }
      
      if (typeof data.data !== 'object') {
        console.error('data.data不是对象类型:', typeof data.data, data.data);
        throw new Error('配置数据格式错误');
      }
      
      const configData = data.data;
      const configKeys = Object.keys(configData);
      console.log('配置数据键:', configKeys);
      console.log('配置数据键数量:', configKeys.length);
      
      if (configKeys.length === 0) {
        console.warn('配置数据为空对象');
        alert('配置数据为空，请检查数据库中是否有数据');
        return;
      }
      
      // 验证数据结构
      let totalConfigItems = 0;
      configKeys.forEach(category => {
        const categoryData = configData[category];
        if (Array.isArray(categoryData)) {
          totalConfigItems += categoryData.length;
          console.log(`分类 ${category}: ${categoryData.length} 个配置项`);
        } else {
          console.warn(`分类 ${category} 的数据不是数组:`, typeof categoryData);
        }
      });
      
      console.log('总配置项数量:', totalConfigItems);
      
      // 设置配置数据
      setSettings(configData);
      console.log('✅ 设置配置数据完成，分类数量:', configKeys.length);
      
      // 设置默认的activeTab为第一个有数据的分类
      const firstCategory = configKeys[0];
      if (firstCategory) {
        setActiveTab(firstCategory);
        console.log('✅ 设置默认分类:', firstCategory);
      }
      
      // 初始化表单数据
      const initialFormData: Record<string, string> = {};
      Object.values(configData).flat().forEach((setting: any) => {
        if (setting && setting.configKey) {
          initialFormData[setting.configKey] = setting.configValue || '';
        }
      });
      
      setFormData(initialFormData);
      console.log('✅ 表单数据初始化完成，配置项数量:', Object.keys(initialFormData).length);
      
    } catch (error) {
      console.error('❌ 加载配置失败:', error);
      console.error('错误详情:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      alert('加载配置失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setLoading(false);
    }
  };

  const loadVersions = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      const response = await fetch('http://localhost:8080/api/admin/website-settings/versions', {
        headers
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.code === 200) {
          setVersions(data.data);
        }
      }
    } catch (error) {
      console.error('加载版本失败:', error);
    }
  };

  const handleInputChange = (configKey: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [configKey]: value
    }));
  };

  const handleSave = async (publishImmediately = false) => {
    setSaving(true);
    try {
      console.log('开始保存配置...', { publishImmediately, formDataKeys: Object.keys(formData).length });
      
      const token = localStorage.getItem('admin_token');
      console.log('使用Token:', token ? token.substring(0, 20) + '...' : '无');
      
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      const requestData = {
        configMap: formData,
        versionName: versionName || `版本_${new Date().toLocaleString()}`,
        publishImmediately
      };
      
      console.log('请求数据:', {
        configMapSize: Object.keys(requestData.configMap).length,
        versionName: requestData.versionName,
        publishImmediately: requestData.publishImmediately
      });

      const response = await fetch('http://localhost:8080/api/admin/website-settings', {
        method: 'POST',
        headers,
        body: JSON.stringify(requestData)
      });
      
      console.log('保存API响应状态:', response.status, response.statusText);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('保存API返回错误:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('保存API响应数据:', data);
      
      // 后端使用 code 字段，200表示成功
      if (data.code === 200) {
        const successMessage = publishImmediately ? '配置已保存并发布！首页将立即更新' : '配置已保存为草稿！要让首页显示新内容，请点击"保存并发布"';
        alert(successMessage);
        console.log('✅ 保存成功:', successMessage);
        
        // 重新加载版本列表
        await loadVersions();
        setVersionName('');
        
        // 如果是发布，也重新加载配置数据以确保同步
        if (publishImmediately) {
          await loadSettings();
        }
      } else {
        const errorMessage = data.message || '保存失败，未知错误';
        console.error('❌ 保存失败:', errorMessage);
        alert('保存失败: ' + errorMessage);
      }
    } catch (error) {
      console.error('❌ 保存过程中发生错误:', error);
      console.error('错误详情:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      
      const errorMessage = error instanceof Error ? error.message : '网络或服务器错误';
      alert('保存失败: ' + errorMessage);
    } finally {
      setSaving(false);
      console.log('保存操作完成');
    }
  };

  const handlePublishVersion = async (versionId: number) => {
    try {
      setLoading(true);
      setMessage({ type: '', text: '' });
      
      const publishResponse = await fetch(`http://localhost:8080/api/admin/website-settings/versions/${versionId}/publish`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      });

      const data = await publishResponse.json();
      
      if (data.code === 200) {
        alert('版本已发布！');
        loadVersions();
      } else {
        alert('发布失败: ' + data.message);
      }
    } catch (error) {
      console.error('发布失败:', error);
      alert('发布失败');
    } finally {
      setLoading(false);
    }
  };

  const openPreview = () => {
    // 创建一个包含当前修改的配置的临时预览URL
    const previewData = {
      configMap: formData,
      timestamp: new Date().toISOString()
    };
    
    // 将预览数据存储到sessionStorage中
    sessionStorage.setItem('admin_preview_config', JSON.stringify(previewData));
    
    // 打开带有预览参数的新页面
    const previewUrl = `/?preview=admin&timestamp=${Date.now()}`;
    window.open(previewUrl, '_blank');
  };

  const previewPublishedConfig = () => {
    // 打开当前发布的配置
    window.open('/', '_blank');
  };

  // 加载配置数据时解析产品配置
  useEffect(() => {
    if (settings.products) {
      const productsDataSetting = settings.products.find(s => s.configKey === 'products_data');
      if (productsDataSetting && formData['products_data']) {
        try {
          const products = JSON.parse(formData['products_data']);
          setProductConfigs(products);
        } catch (e) {
          console.error('Failed to parse products data:', e);
          setProductConfigs([]);
        }
      }
    }
    
    // 加载功能特点配置
    if (settings.features) {
      const featuresDataSetting = settings.features.find(s => s.configKey === 'features_data');
      if (featuresDataSetting && formData['features_data']) {
        try {
          const features = JSON.parse(formData['features_data']);
          setFeatureConfigs(features.map((feature: any) => ({
            ...feature,
            id: feature.id || uuidv4() // 确保每个特点都有ID
          })));
        } catch (e) {
          console.error('Failed to parse features data:', e);
          setFeatureConfigs([]);
        }
      }
    }
  }, [settings.products, settings.features, formData]);
  
  // 添加新产品
  const addNewProduct = () => {
    const newProduct: Product = {
      id: uuidv4(),
      name: '新产品',
      description: '产品描述',
      price: 399,
      originalPrice: 499,
      icon: '🎯',
      gradient: 'from-blue-500/20 to-cyan-500/20'
    };
    
    setCurrentProduct(newProduct);
    setShowProductEditor(true);
  };
  
  // 编辑产品
  const editProduct = (product: Product) => {
    setCurrentProduct({...product});
    setShowProductEditor(true);
  };
  
  // 删除产品
  const deleteProduct = (productId: string) => {
    if (confirm('确定要删除这个产品吗？')) {
      const updatedProducts = productConfigs.filter(p => p.id !== productId);
      setProductConfigs(updatedProducts);
      updateProductsData(updatedProducts);
    }
  };
  
  // 保存产品编辑
  const saveProductEdit = () => {
    if (!currentProduct) return;
    
    if (!currentProduct.name || !currentProduct.description || !currentProduct.price) {
      alert('请填写必填项：产品名称、描述和价格');
      return;
    }
    
    let updatedProducts: Product[];
    
    if (productConfigs.some(p => p.id === currentProduct.id)) {
      // 更新现有产品
      updatedProducts = productConfigs.map(p => 
        p.id === currentProduct.id ? currentProduct : p
      );
    } else {
      // 添加新产品
      updatedProducts = [...productConfigs, currentProduct];
    }
    
    setProductConfigs(updatedProducts);
    updateProductsData(updatedProducts);
    setShowProductEditor(false);
  };
  
  // 更新产品数据到配置
  const updateProductsData = (products: Product[]) => {
    const productsJson = JSON.stringify(products);
    setFormData(prev => ({
      ...prev,
      'products_data': productsJson
    }));
  };
  
  // 更新功能特点数据到配置
  const updateFeaturesData = (features: Feature[]) => {
    const featuresJson = JSON.stringify(features);
    setFormData(prev => ({
      ...prev,
      'features_data': featuresJson
    }));
  };
  
  // 上传产品图片
  const uploadProductImage = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0 || !currentProduct) return;
    
    const file = e.target.files[0];
    const formData = new FormData();
    formData.append('file', file);
    
    setUploadingImage(true);
    
    try {
      // 上传到后端服务器
      const response = await fetch('http://localhost:8080/api/upload/product-image', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        },
        body: formData
      });
      
      const data = await response.json();
      
      if (data.code === 200) {
        setCurrentProduct({
          ...currentProduct,
          imageUrl: data.data
        });
      } else {
        throw new Error(data.message || '上传失败');
      }
    } catch (error) {
      console.error('上传图片失败:', error);
      alert('上传图片失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setUploadingImage(false);
    }
  };
  
  // 上传产品视频
  const uploadProductVideo = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0 || !currentProduct) return;
    
    const file = e.target.files[0];
    const formData = new FormData();
    formData.append('file', file);
    
    setUploadingVideo(true);
    
    try {
      // 上传到后端服务器
      const response = await fetch('http://localhost:8080/api/upload/product-video', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        },
        body: formData
      });
      
      const data = await response.json();
      
      if (data.code === 200) {
        setCurrentProduct({
          ...currentProduct,
          videoUrl: data.data
        });
      } else {
        throw new Error(data.message || '上传失败');
      }
    } catch (error) {
      console.error('上传视频失败:', error);
      alert('上传视频失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setUploadingVideo(false);
    }
  };

  // 添加新功能特点
  const addNewFeature = () => {
    const newFeature: Feature = {
      id: uuidv4(),
      title: '新功能特点',
      description: '功能描述',
      icon: '🚀',
      color: 'from-blue-400 to-cyan-400'
    };
    
    setCurrentFeature(newFeature);
    setShowFeatureEditor(true);
  };
  
  // 编辑功能特点
  const editFeature = (feature: Feature) => {
    setCurrentFeature({...feature});
    setShowFeatureEditor(true);
  };
  
  // 删除功能特点
  const deleteFeature = (featureId: string) => {
    if (confirm('确定要删除这个功能特点吗？')) {
      const updatedFeatures = featureConfigs.filter(f => f.id !== featureId);
      setFeatureConfigs(updatedFeatures);
      updateFeaturesData(updatedFeatures);
    }
  };
  
  // 保存功能特点编辑
  const saveFeatureEdit = () => {
    if (!currentFeature) return;
    
    if (!currentFeature.title || !currentFeature.description) {
      alert('请填写必填项：标题和描述');
      return;
    }
    
    let updatedFeatures: Feature[];
    
    if (featureConfigs.some(f => f.id === currentFeature.id)) {
      // 更新现有功能特点
      updatedFeatures = featureConfigs.map(f => 
        f.id === currentFeature.id ? currentFeature : f
      );
    } else {
      // 添加新功能特点
      updatedFeatures = [...featureConfigs, currentFeature];
    }
    
    setFeatureConfigs(updatedFeatures);
    updateFeaturesData(updatedFeatures);
    setShowFeatureEditor(false);
  };

  // 保存配置
  const saveSettings = async () => {
    setLoading(true);
    setMessage({ type: '', text: '' });
    
    try {
      // 确保产品数据被正确保存
      if (activeTab === 'products') {
        updateProductsData(productConfigs);
      }
      
      // 确保功能特点数据被正确保存
      if (activeTab === 'features') {
        updateFeaturesData(featureConfigs);
      }
      
      const payload = {
        configMap: formData,
        versionName: versionName || `版本-${new Date().toLocaleString('zh-CN')}`
      };
      
      const response = await fetch('http://localhost:8080/api/admin/website-settings/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        },
        body: JSON.stringify(payload)
      });
      
      const data = await response.json();
      
      if (data.code === 200) {
        setMessage({ type: 'success', text: '配置保存成功' });
        setVersionName('');
      } else {
        setMessage({ type: 'error', text: data.message || '保存失败' });
      }
    } catch (error) {
      console.error('保存配置失败:', error);
      setMessage({ type: 'error', text: '保存配置失败，请重试' });
    } finally {
      setLoading(false);
    }
  };
  
  // 发布配置
  const publishSettings = async () => {
    if (!confirm('确定要发布当前配置到前台吗？发布后将立即对所有用户可见。')) {
      return;
    }
    
    setLoading(true);
    setMessage({ type: '', text: '' });
    
    try {
      // 确保产品数据被正确保存
      if (activeTab === 'products') {
        updateProductsData(productConfigs);
      }
      
      // 确保功能特点数据被正确保存
      if (activeTab === 'features') {
        updateFeaturesData(featureConfigs);
      }
      
      // 先保存当前配置
      const savePayload = {
        configMap: formData,
        versionName: `发布版本-${new Date().toLocaleString('zh-CN')}`
      };
      
      const saveResponse = await fetch('http://localhost:8080/api/admin/website-settings/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        },
        body: JSON.stringify(savePayload)
      });
      
      const saveData = await saveResponse.json();
      
      if (saveData.code !== 200) {
        throw new Error(saveData.message || '保存配置失败');
      }
      
      // 发布配置
      const versionName = encodeURIComponent(savePayload.versionName);
      const publishResponse = await fetch(`http://localhost:8080/api/admin/website-settings/versions?versionName=${versionName}&publishImmediately=true`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      });
      
      const publishData = await publishResponse.json();
      
      if (publishData.code === 200) {
        setMessage({ type: 'success', text: '配置已成功发布到前台' });
      } else {
        setMessage({ type: 'error', text: publishData.message || '发布失败' });
      }
    } catch (error) {
      console.error('发布配置失败:', error);
      setMessage({ type: 'error', text: '发布配置失败，请重试' });
    } finally {
      setLoading(false);
    }
  };
  
  // 预览配置
  const previewSettings = () => {
    // 确保产品数据被正确保存
    if (activeTab === 'products') {
      updateProductsData(productConfigs);
    }
    
    // 将当前配置保存到会话存储中
    sessionStorage.setItem('admin_preview_config', JSON.stringify({
      configMap: formData,
      timestamp: new Date().getTime()
    }));
    
    // 打开预览窗口
    window.open('/?preview=admin', '_blank');
  };

  // 加载网站配置时获取宣传视频URL
  useEffect(() => {
    if (formData && formData.promotional_video_url) {
      setPromotionalVideoUrl(formData.promotional_video_url);
    }
  }, [formData]);

  // 上传宣传视频
  const uploadPromotionalVideo = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;
    
    const file = e.target.files[0];
    const formData = new FormData();
    formData.append('file', file);
    
    setUploadingPromoVideo(true);
    
    try {
      // 上传到后端服务器
      const response = await fetch('http://localhost:8080/api/upload/promotional-video', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        },
        body: formData
      });
      
      const data = await response.json();
      
      if (data.code === 200) {
        setPromotionalVideoUrl(data.data);
        handleInputChange('promotional_video_url', data.data);
        console.log('宣传视频上传成功，URL:', data.data);
      } else {
        throw new Error(data.message || '上传失败');
      }
    } catch (error) {
      console.error('上传宣传视频失败:', error);
      alert('上传视频失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setUploadingPromoVideo(false);
    }
  };

  if (adminLoading) {
    return <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
    </div>;
  }

  if (!admin) {
    return null; // 会被useEffect重定向到登录页
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* 导航栏 */}
      <nav className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin/settings" className="text-gray-400 hover:text-white mr-4">
                ← 返回设置
              </Link>
              <h1 className="text-xl font-bold text-white">网站配置管理</h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={openPreview}
                className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded transition-colors"
                disabled={Object.keys(formData).length === 0}
                title={Object.keys(formData).length === 0 ? "请先修改一些配置项" : "预览当前修改的内容"}
              >
                预览草稿
              </button>
              <button
                onClick={previewPublishedConfig}
                className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors"
              >
                预览发布版
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* 侧边栏 */}
        <aside className="w-80 bg-gray-800 min-h-screen">
          <div className="p-4">
            <h2 className="text-lg font-bold text-white mb-4">配置分类</h2>
            <nav className="space-y-2">
              {categories.map((category) => (
                <button
                  key={category.key}
                  onClick={() => setActiveTab(category.key)}
                  className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                    activeTab === category.key
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-300 hover:text-white hover:bg-gray-700'
                  }`}
                >
                  <span className="mr-3 text-xl">{category.icon}</span>
                  <span>{category.name}</span>
                  <span className="ml-auto text-sm text-gray-400">
                    {settings[category.key]?.length || 0}
                  </span>
                </button>
              ))}
            </nav>

            {/* 版本管理 */}
            <div className="mt-8">
              <h3 className="text-lg font-bold text-white mb-4">版本管理</h3>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {versions.map((version) => (
                  <div
                    key={version.id}
                    className={`p-3 rounded-lg border ${
                      version.isPublished
                        ? 'bg-green-900 border-green-600'
                        : 'bg-gray-700 border-gray-600'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-white text-sm font-medium">
                        {version.versionName}
                      </span>
                      {version.isPublished && (
                        <span className="text-green-400 text-xs">已发布</span>
                      )}
                    </div>
                    <div className="text-gray-400 text-xs mt-1">
                      {new Date(version.createdAt).toLocaleString()}
                    </div>
                    {!version.isPublished && (
                      <button
                        onClick={() => handlePublishVersion(version.id)}
                        className="mt-2 text-blue-400 hover:text-blue-300 text-xs"
                      >
                        发布此版本
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </aside>

        {/* 主内容区 */}
        <main className="flex-1 p-6">
          <div className="bg-gray-800 rounded-lg border border-gray-700">
            <div className="p-6 border-b border-gray-700">
              <h2 className="text-xl font-bold text-white mb-2">
                {categories.find(c => c.key === activeTab)?.name} 配置
              </h2>
              <p className="text-gray-400">
                编辑 {categories.find(c => c.key === activeTab)?.name} 区域的内容
              </p>
              
              {/* 消息提示 */}
              {message.text && (
                <div className={`mt-4 p-4 rounded-lg ${message.type === 'success' ? 'bg-green-600/20 border border-green-600/30 text-green-400' : 'bg-red-600/20 border border-red-600/30 text-red-400'}`}>
                  {message.text}
                </div>
              )}
            </div>

            <div className="p-6">
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                  <div className="text-gray-400">加载配置中...</div>
                </div>
              ) : settings[activeTab] && settings[activeTab].length > 0 ? (
                <div className="space-y-6">
                  {activeTab === 'products' && (
                    <div className="bg-gray-800 rounded-lg p-6 mb-4">
                      <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                        <span className="mr-2">🎬</span>
                        宣传视频设置
                      </h3>
                      <div className="grid grid-cols-1 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            宣传视频标题
                          </label>
                          <input
                            type="text"
                            value={formData.promo_video_title || '键帽宣传视频'}
                            onChange={(e) => handleInputChange('promo_video_title', e.target.value)}
                            className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white"
                            placeholder="请输入宣传视频标题"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            宣传视频描述
                          </label>
                          <textarea
                            value={formData.promo_video_description || '发现键帽设计的无限可能'}
                            onChange={(e) => handleInputChange('promo_video_description', e.target.value)}
                            className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white"
                            placeholder="请输入宣传视频描述"
                            rows={3}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">宣传视频</label>
                          <div className="space-y-4">
                            {/* 视频预览 */}
                            {promotionalVideoUrl ? (
                              <div className="relative rounded-lg overflow-hidden aspect-video bg-gray-700 border border-gray-600">
                                <video
                                  src={
                                    promotionalVideoUrl.startsWith('http')
                                      ? promotionalVideoUrl
                                      : `http://localhost:8080${promotionalVideoUrl}`
                                  }
                                  className="w-full h-full object-contain"
                                  controls
                                />
                                <button
                                  onClick={() => {
                                    setPromotionalVideoUrl('');
                                    handleInputChange('promotional_video_url', '');
                                  }}
                                  className="absolute top-2 right-2 bg-red-600 rounded-full w-6 h-6 flex items-center justify-center text-white text-xs"
                                  title="删除视频"
                                >
                                  ✕
                                </button>
                              </div>
                            ) : (
                              <div className="aspect-video rounded-lg bg-gray-700 border border-gray-600 flex items-center justify-center">
                                <div className="text-center text-gray-400">
                                  <div className="text-4xl mb-2">🎬</div>
                                  <p>暂无宣传视频</p>
                                </div>
                              </div>
                            )}
                            
                            {/* 上传按钮 */}
                            <div>
                              <label className="inline-block px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded cursor-pointer transition-colors">
                                {uploadingPromoVideo ? (
                                  <span className="flex items-center">
                                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    上传中...
                                  </span>
                                ) : (
                                  <span className="flex items-center">
                                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    上传宣传视频
                                  </span>
                                )}
                                <input
                                  type="file"
                                  className="hidden"
                                  accept="video/*"
                                  onChange={uploadPromotionalVideo}
                                  disabled={uploadingPromoVideo}
                                />
                              </label>
                              <p className="text-xs text-gray-400 mt-1">推荐尺寸: 1920×1080, MP4格式, 大小不超过100MB</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === 'products' && (
                    <div className="mb-8">
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="text-xl font-bold text-white">产品展示配置</h3>
                        <button
                          onClick={addNewProduct}
                          className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                        >
                          添加新产品
                        </button>
                      </div>

                      {/* 产品列表 */}
                      <div className="space-y-4 mb-6">
                        {productConfigs.map((product) => (
                          <div 
                            key={product.id} 
                            className="bg-gray-800 border border-gray-700 rounded-lg p-4"
                          >
                            <div className="flex flex-col md:flex-row gap-4">
                              {/* 左侧：图片/视频/图标预览 */}
                              <div className="w-full md:w-1/3">
                                {product.videoUrl ? (
                                  <div className="relative rounded-lg overflow-hidden h-48 bg-gray-900">
                                    <video 
                                      src={product.videoUrl} 
                                      className="w-full h-full object-cover"
                                      controls
                                    />
                                    <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                                      视频
                                    </div>
                                  </div>
                                ) : product.imageUrl ? (
                                  <div className="relative rounded-lg overflow-hidden h-48 bg-gray-900">
                                    <img 
                                      src={product.imageUrl} 
                                      alt={product.name} 
                                      className="w-full h-full object-contain"
                                    />
                                    <div className="absolute top-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded">
                                      图片
                                    </div>
                                  </div>
                                ) : (
                                  <div className="flex items-center justify-center h-48 bg-gray-900 rounded-lg">
                                    <div className="text-8xl">{product.icon}</div>
                                  </div>
                                )}
                              </div>
                              
                              {/* 右侧：产品信息 */}
                              <div className="flex-1">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <h4 className="text-xl text-white font-medium">{product.name}</h4>
                                    <p className="text-gray-400 mt-1">{product.description}</p>
                                    <div className="mt-2 flex items-center">
                                      <span className="text-cyan-400 font-bold text-xl">¥{product.price}</span>
                                      {product.originalPrice && (
                                        <span className="text-gray-500 line-through ml-2">¥{product.originalPrice}</span>
                                      )}
                                    </div>
                                    <div className="mt-2 flex flex-wrap gap-2">
                                      {product.gradient && (
                                        <span className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded">
                                          渐变: {product.gradient}
                                        </span>
                                      )}
                                      {product.icon && (
                                        <span className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded flex items-center">
                                          图标: <span className="ml-1 text-lg">{product.icon}</span>
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                  <div className="flex space-x-2">
                                    <button
                                      onClick={() => editProduct(product)}
                                      className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
                                    >
                                      编辑
                                    </button>
                                    <button
                                      onClick={() => deleteProduct(product.id)}
                                      className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
                                    >
                                      删除
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}

                        {productConfigs.length === 0 && (
                          <div className="text-center py-16 bg-gray-800 border border-gray-700 rounded-lg">
                            <div className="text-6xl mb-4">📦</div>
                            <p className="text-gray-400 text-xl mb-2">暂无产品配置</p>
                            <p className="text-gray-500 mb-6">添加产品来自定义首页产品展示区域</p>
                            <button
                              onClick={addNewProduct}
                              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-lg"
                            >
                              添加第一个产品
                            </button>
                          </div>
                        )}
                      </div>

                      <div className="border-t border-gray-700 pt-4">
                        <p className="text-gray-400 text-sm">
                          提示: 产品配置将自动保存为JSON格式到products_data配置项中
                        </p>
                      </div>
                    </div>
                  )}

                  {activeTab === 'features' && (
                    <div className="mb-8">
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="text-xl font-bold text-white">功能特点配置</h3>
                        <button
                          onClick={addNewFeature}
                          className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                        >
                          添加新功能特点
                        </button>
                      </div>

                      {/* 功能特点列表 */}
                      <div className="space-y-4 mb-6">
                        {featureConfigs.map((feature) => (
                          <div 
                            key={feature.id} 
                            className="bg-gray-800 border border-gray-700 rounded-lg p-4"
                          >
                            <div className="flex flex-col md:flex-row gap-4">
                              {/* 左侧：图标预览 */}
                              <div className="w-full md:w-1/3">
                                <div className="flex items-center justify-center h-48 bg-gray-900 rounded-lg">
                                  <div className="text-8xl">{feature.icon}</div>
                                </div>
                              </div>
                              
                              {/* 右侧：功能特点信息 */}
                              <div className="flex-1">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <h4 className="text-xl text-white font-medium">{feature.title}</h4>
                                    <p className="text-gray-400 mt-1">{feature.description}</p>
                                    <div className="mt-2 flex items-center">
                                      <span className="text-cyan-400 font-bold text-xl">颜色: {feature.color}</span>
                                    </div>
                                  </div>
                                  <div className="flex space-x-2">
                                    <button
                                      onClick={() => editFeature(feature)}
                                      className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
                                    >
                                      编辑
                                    </button>
                                    <button
                                      onClick={() => deleteFeature(feature.id)}
                                      className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
                                    >
                                      删除
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}

                        {featureConfigs.length === 0 && (
                          <div className="text-center py-16 bg-gray-800 border border-gray-700 rounded-lg">
                            <div className="text-6xl mb-4">⚡</div>
                            <p className="text-gray-400 text-xl mb-2">暂无功能特点配置</p>
                            <p className="text-gray-500 mb-6">添加功能特点来自定义首页功能特点展示区域</p>
                            <button
                              onClick={addNewFeature}
                              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-lg"
                            >
                              添加第一个功能特点
                            </button>
                          </div>
                        )}
                      </div>

                      <div className="border-t border-gray-700 pt-4">
                        <p className="text-gray-400 text-sm">
                          提示: 功能特点配置将自动保存为JSON格式到features_data配置项中
                        </p>
                      </div>
                    </div>
                  )}

                  {settings[activeTab].map((setting) => {
                    // 如果是产品数据的JSON配置项，隐藏它，因为我们已经提供了可视化编辑界面
                    if ((setting.configKey === 'products_data' && activeTab === 'products') ||
                        (setting.configKey === 'features_data' && activeTab === 'features')) {
                      return null;
                    }
                    
                    return (
                    <div key={setting.configKey} className="space-y-2">
                      <label className="block text-sm font-medium text-gray-300">
                        {setting.description || setting.configKey}
                      </label>
                      
                      {setting.configType === 'json' ? (
                        <textarea
                          value={formData[setting.configKey] || ''}
                          onChange={(e) => handleInputChange(setting.configKey, e.target.value)}
                          rows={10}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                          placeholder="JSON格式数据"
                        />
                      ) : (
                        <input
                          type="text"
                          value={formData[setting.configKey] || ''}
                          onChange={(e) => handleInputChange(setting.configKey, e.target.value)}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-blue-500 focus:border-blue-500"
                          placeholder={setting.description}
                        />
                      )}
                      
                      <p className="text-xs text-gray-500">
                        配置键: {setting.configKey} | 类型: {setting.configType}
                      </p>
                    </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">请选择左侧的配置分类</div>
              )}
            </div>
          </div>

          {/* 底部操作按钮 */}
          {activeTab && (
            <div className="p-6 border-t border-gray-700 flex flex-col sm:flex-row justify-between items-center gap-4">
              <div className="flex items-center w-full sm:w-auto">
                <input
                  type="text"
                  value={versionName}
                  onChange={(e) => setVersionName(e.target.value)}
                  placeholder="版本名称 (可选)"
                  className="w-full sm:w-auto px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div className="flex flex-wrap gap-3 justify-center sm:justify-end w-full sm:w-auto">
                  <button 
                  onClick={saveSettings}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center"
                  disabled={loading}
                >
                  {loading ? (
                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                    </svg>
                  )}
                  保存配置
                </button>
                
                <button
                  onClick={previewSettings}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center"
                  disabled={loading}
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  预览效果
                </button>
                
                <button
                  onClick={publishSettings}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center"
                  disabled={loading}
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"></path>
                  </svg>
                  发布到前台
                  </button>
              </div>
            </div>
          )}
        </main>
      </div>

      {/* 产品编辑弹窗 */}
      {showProductEditor && currentProduct && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold text-white">
                  {currentProduct.id ? '编辑产品' : '添加产品'}
                </h3>
                <button 
                  onClick={() => setShowProductEditor(false)}
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-6">
                {/* 基本信息 */}
                <div>
                  <h4 className="text-lg font-medium text-white mb-3 flex items-center">
                    <span className="mr-2">基本信息</span>
                    <span className="text-xs text-gray-400 font-normal">产品的基本展示信息</span>
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">产品名称 <span className="text-red-500">*</span></label>
                      <input
                        type="text"
                        value={currentProduct.name}
                        onChange={(e) => setCurrentProduct({...currentProduct, name: e.target.value})}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                        placeholder="例如：键帽小新联名款"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">图标 (Emoji)</label>
                      <input
                        type="text"
                        value={currentProduct.icon || ''}
                        onChange={(e) => setCurrentProduct({...currentProduct, icon: e.target.value})}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                        placeholder="例如：🎯 🌸 💎 🔥"
                      />
                      <p className="text-xs text-gray-500 mt-1">可以使用Windows+. 或 Ctrl+Cmd+Space 打开表情选择器</p>
                    </div>
                  </div>
                </div>

                {/* 描述 */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">产品描述 <span className="text-red-500">*</span></label>
                  <textarea
                    value={currentProduct.description}
                    onChange={(e) => setCurrentProduct({...currentProduct, description: e.target.value})}
                    rows={3}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                    placeholder="简短描述产品的特点和卖点"
                  />
                </div>

                {/* 价格 */}
                <div>
                  <h4 className="text-lg font-medium text-white mb-3 flex items-center">
                    <span className="mr-2">价格信息</span>
                    <span className="text-xs text-gray-400 font-normal">设置产品价格和折扣</span>
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">价格 (¥) <span className="text-red-500">*</span></label>
                      <input
                        type="number"
                        value={currentProduct.price}
                        onChange={(e) => setCurrentProduct({...currentProduct, price: parseInt(e.target.value)})}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                        placeholder="例如：399"
                        min="0"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">原价 (¥)</label>
                      <input
                        type="number"
                        value={currentProduct.originalPrice || ''}
                        onChange={(e) => setCurrentProduct({...currentProduct, originalPrice: parseInt(e.target.value)})}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                        placeholder="例如：499"
                        min="0"
                      />
                      <p className="text-xs text-gray-500 mt-1">设置原价可以显示折扣效果</p>
                    </div>
                  </div>
                </div>

                {/* 样式设置 */}
                <div>
                  <h4 className="text-lg font-medium text-white mb-3 flex items-center">
                    <span className="mr-2">样式设置</span>
                    <span className="text-xs text-gray-400 font-normal">自定义产品的显示样式</span>
                  </h4>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">渐变色样式</label>
                    <input
                      type="text"
                      value={currentProduct.gradient || ''}
                      onChange={(e) => setCurrentProduct({...currentProduct, gradient: e.target.value})}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                      placeholder="例如: from-blue-500/20 to-cyan-500/20"
                    />
                    <p className="text-xs text-gray-500 mt-1">使用Tailwind CSS渐变色格式，可选值：
                      <span className="text-blue-400">from-blue-500/20 to-cyan-500/20</span>,
                      <span className="text-pink-400">from-pink-500/20 to-purple-500/20</span>,
                      <span className="text-orange-400">from-orange-500/20 to-red-500/20</span>
                    </p>
                  </div>
                </div>
                
                {/* 媒体上传 */}
                <div>
                  <h4 className="text-lg font-medium text-white mb-3 flex items-center">
                    <span className="mr-2">媒体文件</span>
                    <span className="text-xs text-gray-400 font-normal">上传产品图片或视频</span>
                  </h4>
                  
                  {/* 预览区域 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    {/* 图片预览 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">产品图片</label>
                      <div className="relative bg-gray-900 rounded-lg overflow-hidden h-40 flex items-center justify-center border border-gray-700">
                        {currentProduct.imageUrl ? (
                          <>
                            <img 
                              src={currentProduct.imageUrl} 
                              alt="产品图片" 
                              className="w-full h-full object-contain"
                            />
                            <button
                              onClick={() => setCurrentProduct({...currentProduct, imageUrl: ''})}
                              className="absolute top-2 right-2 bg-red-600 rounded-full w-6 h-6 flex items-center justify-center text-white text-xs"
                              title="删除图片"
                            >
                              ✕
                            </button>
                          </>
                        ) : (
                          <div className="text-gray-500 text-center">
                            <div className="text-3xl mb-2">🖼️</div>
                            <div className="text-sm">暂无图片</div>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* 视频预览 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">产品视频</label>
                      <div className="relative bg-gray-900 rounded-lg overflow-hidden h-40 flex items-center justify-center border border-gray-700">
                        {currentProduct.videoUrl ? (
                          <>
                            <video 
                              src={currentProduct.videoUrl} 
                              className="w-full h-full object-cover"
                              controls
                            />
                            <button
                              onClick={() => setCurrentProduct({...currentProduct, videoUrl: ''})}
                              className="absolute top-2 right-2 bg-red-600 rounded-full w-6 h-6 flex items-center justify-center text-white text-xs"
                              title="删除视频"
                            >
                              ✕
                            </button>
                          </>
                        ) : (
                          <div className="text-gray-500 text-center">
                            <div className="text-3xl mb-2">🎬</div>
                            <div className="text-sm">暂无视频</div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* 上传按钮 */}
                  <div className="flex flex-wrap gap-4">
                    <div>
                      <label className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded cursor-pointer transition-colors inline-flex items-center">
                        {uploadingImage ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            上传中...
                          </>
                        ) : (
                          <>
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            上传图片
                          </>
                        )}
                        <input
                          type="file"
                          accept="image/*"
                          onChange={uploadProductImage}
                          className="hidden"
                          disabled={uploadingImage}
                        />
                      </label>
                      <p className="text-xs text-gray-500 mt-1">推荐尺寸: 800×600px，JPG或PNG格式</p>
                    </div>
                    
                    <div>
                      <label className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded cursor-pointer transition-colors inline-flex items-center">
                        {uploadingVideo ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            上传中...
                          </>
                        ) : (
                          <>
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                            上传视频
                          </>
                        )}
                        <input
                          type="file"
                          accept="video/*"
                          onChange={uploadProductVideo}
                          className="hidden"
                          disabled={uploadingVideo}
                        />
                      </label>
                      <p className="text-xs text-gray-500 mt-1">推荐尺寸: 1280×720px，MP4格式，大小小于20MB</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-8 flex justify-end space-x-4">
                <button
                  onClick={() => setShowProductEditor(false)}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={saveProductEdit}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors flex items-center"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  保存产品
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 功能特点编辑弹窗 */}
      {showFeatureEditor && currentFeature && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold text-white">
                  {currentFeature.id ? '编辑功能特点' : '添加功能特点'}
                </h3>
                <button 
                  onClick={() => setShowFeatureEditor(false)}
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-6">
                {/* 基本信息 */}
                <div>
                  <h4 className="text-lg font-medium text-white mb-3 flex items-center">
                    <span className="mr-2">基本信息</span>
                    <span className="text-xs text-gray-400 font-normal">功能特点的基本展示信息</span>
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">标题 <span className="text-red-500">*</span></label>
                      <input
                        type="text"
                        value={currentFeature.title}
                        onChange={(e) => setCurrentFeature({...currentFeature, title: e.target.value})}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                        placeholder="例如：AI智能设计"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">图标 (Emoji)</label>
                      <input
                        type="text"
                        value={currentFeature.icon}
                        onChange={(e) => setCurrentFeature({...currentFeature, icon: e.target.value})}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                        placeholder="例如：🚀 🤖 💡 ⚡"
                      />
                      <p className="text-xs text-gray-500 mt-1">可以使用Windows+. 或 Ctrl+Cmd+Space 打开表情选择器</p>
                    </div>
                  </div>
                </div>

                {/* 描述 */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">描述 <span className="text-red-500">*</span></label>
                  <textarea
                    value={currentFeature.description}
                    onChange={(e) => setCurrentFeature({...currentFeature, description: e.target.value})}
                    rows={3}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                    placeholder="简短描述功能特点"
                  />
                </div>

                {/* 样式设置 */}
                <div>
                  <h4 className="text-lg font-medium text-white mb-3 flex items-center">
                    <span className="mr-2">样式设置</span>
                    <span className="text-xs text-gray-400 font-normal">自定义功能特点的显示样式</span>
                  </h4>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">颜色样式</label>
                    <input
                      type="text"
                      value={currentFeature.color}
                      onChange={(e) => setCurrentFeature({...currentFeature, color: e.target.value})}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                      placeholder="例如: from-blue-400 to-cyan-400"
                    />
                    <p className="text-xs text-gray-500 mt-1">使用Tailwind CSS渐变色格式，可选值：
                      <span className="text-blue-400">from-blue-400 to-cyan-400</span>,
                      <span className="text-pink-400">from-pink-400 to-purple-400</span>,
                      <span className="text-orange-400">from-orange-400 to-red-400</span>,
                      <span className="text-green-400">from-green-400 to-teal-400</span>
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-8 flex justify-end space-x-4">
                <button
                  onClick={() => setShowFeatureEditor(false)}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={saveFeatureEdit}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors flex items-center"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  保存功能特点
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 