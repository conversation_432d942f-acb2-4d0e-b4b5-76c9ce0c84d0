@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-rgb: 139, 69, 255;
  --purple-primary: 139, 69, 255;
  --purple-secondary: 168, 85, 247;
  --purple-accent: 196, 181, 253;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-rgb: 88, 28, 135;
    --purple-primary: 88, 28, 135;
    --purple-secondary: 124, 58, 237;
    --purple-accent: 147, 51, 234;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" !important;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* 强制所有元素使用系统字体和正常颜色 */
*, *::before, *::after {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" !important;
}

.dashboard-return-button,
.dashboard-return-button span,
.community-dark-theme h3, 
.community-dark-theme h4,
.community-dark-theme .text-orange-400,
.community-dark-card button,
.share-work-button, 
.share-work-icon, 
.share-work-text,
.community-tab-button,
.community-tab-button span,
.bg-gradient-to-br.from-white h3.text-yellow-600,
.bg-gradient-to-br.from-white .bg-white .font-bold.text-gray-900,
.bg-gradient-to-br.from-white .bg-white .text-gray-500 {
  opacity: 1 !important;
}

/* 确保创作仪表盘与深色风格一致 */
.dashboard-button {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #7c3aed;
  --tw-gradient-to: #2563eb;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  color: white;
  font-weight: 600;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.dashboard-button:hover {
  --tw-gradient-from: #6d28d9;
  --tw-gradient-to: #1d4ed8;
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* 防止返回仪表盘按钮闪烁 */
.dashboard-return-button {
  will-change: auto !important;
  backface-visibility: hidden;
  transform: translateZ(0);
  -webkit-font-smoothing: subpixel-antialiased;
  transition: background-color 0.2s ease-in-out !important;
  animation: none !important;
  opacity: 1 !important;
}

.dashboard-return-button span {
  opacity: 1 !important;
  animation: none !important;
}

/* 社区页面特定样式 */
.community-dark-theme h3, 
.community-dark-theme h4,
.community-dark-theme .text-orange-400 {
  opacity: 1 !important;
  visibility: visible !important;
}

.community-dark-card h3 {
  color: white !important;
  -webkit-text-fill-color: white !important;
}

.community-dark-card p {
  color: #d1d5db !important;
  -webkit-text-fill-color: #d1d5db !important;
}

/* 热门话题按钮样式 */
.community-dark-card button {
  opacity: 1 !important;
  visibility: visible !important;
}

.community-dark-card button[style*="backgroundColor: #ffffff"] span,
.community-dark-card button[style*="background-color: #ffffff"] span {
  color: #1f2937 !important;
  -webkit-text-fill-color: #1f2937 !important;
  opacity: 1 !important;
  font-weight: 800 !important;
  visibility: visible !important;
}

/* 分享你的作品按钮特殊样式 */
.share-work-button, .share-work-icon, .share-work-text {
  opacity: 1 !important;
  visibility: visible !important;
}

.share-work-text {
  color: #7c3aed !important;
  -webkit-text-fill-color: #7c3aed !important;
  font-weight: bold !important;
}

/* 用户推荐区域样式 */
.bg-gradient-to-br.from-white h3.text-yellow-600 {
  color: #d97706 !important;
  -webkit-text-fill-color: #d97706 !important;
  opacity: 1 !important;
  visibility: visible !important;
  text-shadow: 0px 1px 2px rgba(0,0,0,0.1);
}

.bg-gradient-to-br.from-white .bg-white .font-bold.text-gray-900 {
  color: #111827 !important;
  -webkit-text-fill-color: #111827 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.bg-gradient-to-br.from-white .bg-white .text-gray-500 {
  color: #6b7280 !important;
  -webkit-text-fill-color: #6b7280 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* 修复帖子标题和内容 */
.community-dark-card .text-gray-900 {
  color: white !important;
  -webkit-text-fill-color: white !important;
}

.community-dark-card .text-gray-600 {
  color: #d1d5db !important;
  -webkit-text-fill-color: #d1d5db !important;
}

/* 修复导航标签文字 */
.community-tab-button {
  opacity: 1 !important;
  visibility: visible !important;
}

.community-tab-button span {
  opacity: 1 !important;
  visibility: visible !important;
}

.community-tab-button.bg-gradient-to-r span {
  color: white !important;
  -webkit-text-fill-color: white !important;
}

.community-tab-button:not(.bg-gradient-to-r) span {
  color: #d1d5db !important;
  -webkit-text-fill-color: #d1d5db !important;
}

/* 动画类 */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradient-text {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

@keyframes gradient-text-reverse {
  0%, 100% {
    background-size: 200% 200%;
    background-position: right center;
  }
  50% {
    background-size: 200% 200%;
    background-position: left center;
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out;
}

.animate-slide-in-up {
  animation: slide-in-up 0.8s ease-out;
}

.animate-gradient-text {
  animation: gradient-text 3s ease-in-out infinite;
}

.animate-gradient-text-reverse {
  animation: gradient-text-reverse 3s ease-in-out infinite;
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-600 {
  animation-delay: 600ms;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* 滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
}

.scrollbar-track-gray-100::-webkit-scrollbar-track {
  background-color: #f3f4f6;
}

/* 文本截断 */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* 添加平滑动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 动画类 */
.animate-in {
  animation-fill-mode: both;
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in-from-bottom {
  animation: slideInFromBottom 0.3s ease-out;
}

.slide-in-from-top {
  animation: slideInFromTop 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* 持续时间变体 */
.duration-200 {
  animation-duration: 200ms;
}

.duration-300 {
  animation-duration: 300ms;
}

.duration-500 {
  animation-duration: 500ms;
}

/* 平滑过渡 */
* {
  transition: all 0.2s ease-in-out;
}

/* 流星动画 */
@keyframes meteor {
  0% {
    transform: translateX(-100px) translateY(-100px) rotate(45deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(100vw) translateY(100vh) rotate(45deg);
    opacity: 0;
  }
}

@keyframes meteor-reverse {
  0% {
    transform: translateX(100vw) translateY(-100px) rotate(-45deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(-100px) translateY(100vh) rotate(-45deg);
    opacity: 0;
  }
}

.meteor {
  position: absolute;
  width: 2px;
  height: 2px;
  background: linear-gradient(45deg, #ffffff, #60a5fa, #a78bfa, #ec4899);
  border-radius: 50%;
  box-shadow: 0 0 10px #ffffff, 0 0 20px #60a5fa, 0 0 30px #a78bfa;
  animation: meteor 3s linear infinite;
}

.meteor::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100px;
  height: 1px;
  background: linear-gradient(90deg, transparent, #ffffff, transparent);
  transform: translate(-50%, -50%) rotate(45deg);
  opacity: 0.7;
}

.meteor-reverse {
  animation: meteor-reverse 4s linear infinite;
}

.meteor-reverse::before {
  transform: translate(-50%, -50%) rotate(-45deg);
}

/* 防止过度动画 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 终极修复 - 覆盖所有可能的干扰 */
span.text-transparent.bg-clip-text,
span[class*="bg-gradient-"][class*="bg-clip-text"],
span[class*="text-transparent"][class*="bg-clip-text"] {
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  color: transparent !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: inline !important;
  font-weight: inherit !important;
}

/* 修复素材库和上传页面的字体透明问题 */
.materials-page button,
.materials-page a,
.materials-page select,
.materials-upload-page button,
.materials-upload-page a,
.materials-upload-page select {
  -webkit-text-fill-color: currentColor !important;
  color: revert !important;
}

.materials-page button span,
.materials-page a span,
.materials-upload-page button span,
.materials-upload-page a span {
  -webkit-text-fill-color: currentColor !important;
  color: inherit !important;
}

/* 修复素材上传页面的输入框字体透明问题 */
.materials-upload-page input,
.materials-upload-page textarea,
.materials-upload-page select {
  -webkit-text-fill-color: initial !important;
  color: initial !important;
}

/* 修复设置页面的输入框字体透明问题 */
.settings-page input,
.settings-page textarea,
.settings-page select {
  -webkit-text-fill-color: initial !important;
  color: initial !important;
}

/* 修复所有模态框内的输入框和文本域文字颜色 */
.fixed.inset-0 .bg-white input,
.fixed.inset-0 .bg-white textarea {
  color: #111827 !important; /* text-gray-900 */
  -webkit-text-fill-color: #111827 !important;
  background-image: none !important;
}

/* 修复设置页面输入框问题 */
.settings-page input,
.settings-page select {
  -webkit-text-fill-color: initial !important;
}

/* 修复所有弹窗中输入框文字透明问题 */
.fixed.inset-0 .bg-white input,
.fixed.inset-0 .bg-white textarea {
    color: #1f2937 !important; /* Tailwind's gray-800 */
    -webkit-text-fill-color: #1f2937 !important;
}

.fixed.inset-0 .bg-white input::placeholder,
.fixed.inset-0 .bg-white textarea::placeholder {
    color: #6b7280 !important; /* Tailwind's gray-500 */
    -webkit-text-fill-color: #6b7280 !important;
}

/* 修复社区弹窗中分区选择按钮的文字颜色 */
.fixed.inset-0 .bg-white .grid button.border-gray-200 .font-medium {
    color: #1f2937 !important; /* Tailwind's gray-800 */
    -webkit-text-fill-color: #1f2937 !important;
}

/* 修复帖子详情页评论输入框和返回按钮的文字颜色 */

/* 1. 修复评论输入框的输入文字颜色 */
.bg-white .border-pink-500 textarea {
    color: #1f2937 !important; /* Tailwind's gray-800 */
    -webkit-text-fill-color: #1f2937 !important;
}

/* 2. 修复帖子详情页返回按钮的文字颜色 */
.bg-gray-100.hover\\:bg-gray-200 {
    color: #1f2937 !important; /* Tailwind's gray-800 */
    -webkit-text-fill-color: #1f2937 !important;
}

/* 修复购物车页面文字透明问题 */
/* 1. 修复购物车结算面板右侧数值的文字颜色 */
.cart-page .bg-white span:not([class*="text-"]),
.cart-page .bg-white span.text-undefined {
    color: #111827 !important; /* Tailwind's gray-900 */
    -webkit-text-fill-color: #111827 !important;
}

/* 2. 修复购物车数量控制按钮的文字颜色 */
.cart-page .border-gray-300 {
    color: #374151 !important; /* Tailwind's gray-700 */
    -webkit-text-fill-color: #374151 !important;
}

/* 3. 确保购物车所有文字都可见 */
.cart-page span,
.cart-page button,
.cart-page p,
.cart-page h1,
.cart-page h2,
.cart-page h3 {
    opacity: 1 !important;
    visibility: visible !important;
}

/* 修复确认订单页面文字透明问题 */
/* 1. 修复确认订单右侧摘要数值的文字颜色 */
.cart-checkout-page .bg-white span:not([class*="text-"]),
.cart-checkout-page .bg-white span.text-undefined {
    color: #111827 !important; /* Tailwind's gray-900 */
    -webkit-text-fill-color: #111827 !important;
}

/* 2. 修复确认订单所有输入框和按钮的文字颜色 */
.cart-checkout-page input,
.cart-checkout-page textarea,
.cart-checkout-page button {
    color: inherit !important;
    -webkit-text-fill-color: inherit !important;
}

/* 3. 确保确认订单页面所有文字都可见 */
.cart-checkout-page span,
.cart-checkout-page p,
.cart-checkout-page h1,
.cart-checkout-page h2,
.cart-checkout-page h3,
.cart-checkout-page h4 {
    opacity: 1 !important;
    visibility: visible !important;
}

/* 购物车页面透明文字修复 */
.cart-page input,
.cart-page textarea,
.cart-page button,
.cart-page select,
.cart-page .text-gray-800,
.cart-page .text-gray-900,
.cart-page .text-gray-700 {
  color: #374151 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.cart-page select option {
  color: #374151 !important;
  background-color: white !important;
}

/* 确认订单页面透明文字修复 */
.cart-checkout-page input,
.cart-checkout-page textarea,
.cart-checkout-page button,
.cart-checkout-page select,
.cart-checkout-page .text-gray-800,
.cart-checkout-page .text-gray-900,
.cart-checkout-page .text-gray-700 {
  color: #374151 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.cart-checkout-page select option {
  color: #374151 !important;
  background-color: white !important;
}

/* 全局下拉选择框文字修复 */
select {
  color: #374151 !important;
}

select option {
  color: #374151 !important;
  background-color: white !important;
}


/* 1. 修复购物车结算面板右侧数值的文字颜色 */
.cart-page .bg-white span:not([class*="text-"]),
.cart-page .bg-white span.text-undefined {
    color: #111827 !important; /* Tailwind's gray-900 */
    -webkit-text-fill-color: #111827 !important;
}

/* 2. 修复购物车数量控制按钮的文字颜色 */
.cart-page .border-gray-300 {
    color: #374151 !important; /* Tailwind's gray-700 */
    -webkit-text-fill-color: #374151 !important;
}

/* 3. 确保购物车所有文字都可见 */
.cart-page span,
.cart-page button,
.cart-page p,
.cart-page h1,
.cart-page h2,
.cart-page h3 {
    opacity: 1 !important;
    visibility: visible !important;
}

/* 修复确认订单页面文字透明问题 */
/* 1. 修复确认订单右侧摘要数值的文字颜色 */
.cart-checkout-page .bg-white span:not([class*="text-"]),
.cart-checkout-page .bg-white span.text-undefined {
    color: #111827 !important; /* Tailwind's gray-900 */
    -webkit-text-fill-color: #111827 !important;
}

/* 2. 修复确认订单所有输入框和按钮的文字颜色 */
.cart-checkout-page input,
.cart-checkout-page textarea,
.cart-checkout-page button {
    color: inherit !important;
    -webkit-text-fill-color: inherit !important;
}

/* 3. 确保确认订单页面所有文字都可见 */
.cart-checkout-page span,
.cart-checkout-page p,
.cart-checkout-page h1,
.cart-checkout-page h2,
.cart-checkout-page h3,
.cart-checkout-page h4 {
    opacity: 1 !important;
    visibility: visible !important;
}

/* 购物车页面透明文字修复 */
.cart-page input,
.cart-page textarea,
.cart-page button,
.cart-page select,
.cart-page .text-gray-800,
.cart-page .text-gray-900,
.cart-page .text-gray-700 {
  color: #374151 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.cart-page select option {
  color: #374151 !important;
  background-color: white !important;
}

/* 确认订单页面透明文字修复 */
.cart-checkout-page input,
.cart-checkout-page textarea,
.cart-checkout-page button,
.cart-checkout-page select,
.cart-checkout-page .text-gray-800,
.cart-checkout-page .text-gray-900,
.cart-checkout-page .text-gray-700 {
  color: #374151 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.cart-checkout-page select option {
  color: #374151 !important;
  background-color: white !important;
}

/* 全局下拉选择框文字修复 */
select {
  color: #374151 !important;
}

select option {
  color: #374151 !important;
  background-color: white !important;
}

/* 为2D视图添加深色背景，让白色线框键帽清晰可见 */
.keycap-2d-view {
  background-color: #2d3748 !important;
}


