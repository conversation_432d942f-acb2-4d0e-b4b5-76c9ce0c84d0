import { useState } from 'react';
import Image from 'next/image';

interface ImageWithFallbackProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fallbackSrc?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
  transparent?: boolean;
  enlargeFactor?: number;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  priority?: boolean;
  loadingStrategy?: 'eager' | 'lazy';
}

/**
 * 带有占位图功能的图片组件
 * 当图片加载失败时会显示占位图
 */
export default function ImageWithFallback({
  src,
  alt,
  width,
  height,
  className = 'w-full h-full object-cover',
  fallbackSrc = '/images/placeholders/image-placeholder.png',
  style = {},
  onClick,
  transparent = false,
  enlargeFactor = 1,
  objectFit = 'contain',
  priority = false,
  loadingStrategy = 'eager',
}: ImageWithFallbackProps) {
  const [imgSrc, setImgSrc] = useState(src);
  const [error, setError] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  const handleError = () => {
    console.log(`❌ 图片加载失败: ${src}`);
    console.log(`🔄 切换到备用图片: ${fallbackSrc}`);
    if (!error) {
      setImgSrc(fallbackSrc);
      setError(true);
    }
  };

  const handleLoad = () => {
    console.log(`✅ 图片加载成功: ${imgSrc}`);
    setIsLoaded(true);
  };

  const enlargedWidth = width ? width * enlargeFactor : undefined;
  const enlargedHeight = height ? height * enlargeFactor : undefined;

  // 自动选择合适的object-fit
  const objectFitClass = 
    className.includes('object-cover') ? 'object-cover' : 
    className.includes('object-contain') ? 'object-contain' : 
    `object-${objectFit}`;

  // 合并样式
  const enhancedStyle = {
    cursor: onClick ? 'pointer' : 'default',
    backgroundColor: transparent ? 'transparent' : (style.backgroundColor || '#374151'),
    transform: enlargeFactor > 1 ? `scale(${enlargeFactor})` : undefined,
    opacity: 1, // 强制设置为1，移除加载状态的透明度变化
    transition: 'opacity 0.3s ease-in-out',
    objectFit,
    display: 'block',
    ...style
  };

  return (
    <img
      src={imgSrc}
      alt={alt}
      width={enlargedWidth}
      height={enlargedHeight}
      className={`${className} ${transparent ? 'bg-transparent' : ''} ${objectFitClass}`}
      style={enhancedStyle}
      onClick={onClick}
      onError={handleError}
      onLoad={handleLoad}
      loading={priority ? 'eager' : loadingStrategy}
    />
  );
} 