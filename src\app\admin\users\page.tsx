'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdmin } from '../../../contexts/AdminContext';
import Link from 'next/link';

// 用户数据类型定义
interface UserData {
  userId: string;
  username: string;
  nickname?: string;
  email: string;
  phoneNumber?: string;
  status: string;
  createTime: string;
  loginCount: number;
  isBanned?: boolean;
  isMuted?: boolean;
  banReason?: string;
  banEndTime?: string;
  muteEndTime?: string;
}

export default function AdminUsers() {
  const router = useRouter();
  const { admin, isLoading: adminLoading } = useAdmin();
  const [users, setUsers] = useState<UserData[]>([]);
  const [statistics, setStatistics] = useState({
    totalUsers: 0,
    onlineUsers: 0,
    onlineRate: 0,
    bannedUsers: 0,
    mutedUsers: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionType, setActionType] = useState('');
  const [actionReason, setActionReason] = useState('');
  const [actionDuration, setActionDuration] = useState(24);
  const [newPassword, setNewPassword] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!adminLoading) {
      if (admin) {
        loadStatistics();
        loadUsers();
      } else {
        router.push('/admin/login');
      }
    }
  }, [admin, adminLoading, router]);

  // 加载统计信息
  const loadStatistics = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/admin/users/statistics');
      const result = await response.json();
      if (result.code === 200) {
        setStatistics(result.data);
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  // 加载用户列表
  const loadUsers = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: '0',
        size: '20',
        sortBy: 'createTime',
        sortDir: 'desc'
      });
      
      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter !== 'all') params.append('status', statusFilter);

      const response = await fetch(`http://localhost:8080/api/admin/users/list?${params}`);
      const result = await response.json();
      
      if (result.code === 200) {
        setUsers(result.data.users);
      }
    } catch (error) {
      console.error('加载用户列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 执行用户操作
  const handleUserAction = async (userId: string, action: string, reason?: string, duration?: number) => {
    if (!admin) return;

    const actions: Record<string, string> = {
      ban: '/api/admin/users/ban',
      unban: '/api/admin/users/unban',
      mute: '/api/admin/users/mute',
      'reset-password': '/api/admin/users/reset-password',
      'force-logout': '/api/admin/users/force-logout',
      delete: '/api/admin/users/delete'
    };

    try {
      setLoading(true);
      const requestData: any = {
        userId,
                 adminId: admin.username || 'admin',
        reason: reason || '管理员操作'
      };

      if (action === 'mute' && duration) {
        requestData.durationHours = duration;
        requestData.muteType = 'ALL';
      } else if (action === 'ban' && duration) {
        requestData.durationHours = duration;
      } else if (action === 'reset-password') {
        if (!newPassword || newPassword.trim().length < 6) {
          alert('新密码不能为空且长度不能少于6位');
          setLoading(false);
          return;
        }
        requestData.newPassword = newPassword.trim();
      }

      const response = await fetch(`http://localhost:8080${actions[action]}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();
      
      if (result.code === 200) {
        alert(result.message || '操作成功');
        loadUsers(); // 重新加载用户列表
        loadStatistics(); // 重新加载统计信息
      } else {
        alert(result.message || '操作失败');
      }
    } catch (error) {
      console.error('操作失败:', error);
      alert('操作失败，请稍后重试');
    } finally {
      setLoading(false);
      setShowActionModal(false);
      setActionReason('');
      setNewPassword('');
    }
  };

  // 过滤用户
  const filteredUsers = users.filter(user => {
    const matchesSearch = searchTerm === '' || 
      user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.nickname?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // 显示操作确认弹窗
  const showActionConfirm = (user: UserData, action: string) => {
    setSelectedUser(user);
    setActionType(action);
    setActionReason('');
    setActionDuration(24);
    setNewPassword('');
    setShowActionModal(true);
  };

  // 格式化状态显示
  const getStatusDisplay = (user: UserData) => {
    if (user.isBanned) return { text: '已封禁', class: 'bg-red-500/20 text-red-400' };
    if (user.isMuted) return { text: '已禁言', class: 'bg-yellow-500/20 text-yellow-400' };
    if (user.status === 'online') return { text: '在线', class: 'bg-green-500/20 text-green-400' };
    return { text: '离线', class: 'bg-gray-500/20 text-gray-400' };
  };

  if (adminLoading) {
    return <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
    </div>;
  }

  if (!admin) {
    return null; // 会被useEffect重定向到登录页
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* 顶部导航栏 */}
      <nav className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-600 rounded-lg flex items-center justify-center text-white font-bold text-sm mr-3">
                管
              </Link>
              <h1 className="text-xl font-bold text-white">用户管理</h1>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* 侧边栏 */}
        <aside className="w-64 bg-gray-800 min-h-screen pt-4">
          <nav className="px-4 space-y-2">
            <Link href="/admin/dashboard" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📊</span>
              仪表盘
            </Link>
            <Link href="/admin/users" className="flex items-center px-4 py-2 text-white bg-red-600 rounded-lg">
              <span className="mr-3">👥</span>
              用户管理
            </Link>

            <Link href="/admin/products" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📦</span>
              商品管理
            </Link>
            <Link href="/admin/orders" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🛒</span>
              订单管理
            </Link>
            <Link href="/admin/customer-service" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">💬</span>
              客服中心
            </Link>
            <Link href="/admin/stores" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🏪</span>
              店铺管理
            </Link>
          </nav>
        </aside>

        {/* 主内容区 */}
        <main className="flex-1 p-8">
          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">总用户数</p>
                  <p className="text-2xl font-bold text-white">{statistics.totalUsers}</p>
                </div>
                <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-blue-400 text-2xl">👥</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">在线用户</p>
                  <p className="text-2xl font-bold text-white">{statistics.onlineUsers}</p>
                </div>
                <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-green-400 text-2xl">🟢</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">被封禁用户</p>
                  <p className="text-2xl font-bold text-white">{statistics.bannedUsers}</p>
                </div>
                <div className="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-red-400 text-2xl">🚫</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">在线率</p>
                  <p className="text-2xl font-bold text-white">{statistics.onlineRate}%</p>
                </div>
                <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-purple-400 text-2xl">📊</span>
                </div>
              </div>
            </div>
          </div>

          {/* 搜索和过滤 */}
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="搜索用户名、邮箱或昵称..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                <option value="all">全部状态</option>
                <option value="online">在线</option>
                <option value="offline">离线</option>
                <option value="banned">已封禁</option>
                <option value="muted">已禁言</option>
              </select>
              <button
                onClick={loadUsers}
                className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                搜索
              </button>
            </div>
          </div>

          {/* 用户列表 */}
          <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">用户</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">联系方式</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">状态</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">注册时间</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">登录次数</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {loading ? (
                    <tr>
                      <td colSpan={6} className="px-6 py-8 text-center text-gray-400">
                        <div className="flex justify-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500"></div>
                        </div>
                      </td>
                    </tr>
                  ) : filteredUsers.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="px-6 py-8 text-center text-gray-400">
                        暂无用户数据
                      </td>
                    </tr>
                  ) : (
                    filteredUsers.map((user) => {
                      const status = getStatusDisplay(user);
                      return (
                        <tr key={user.userId} className="hover:bg-gray-700/50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3">
                                {user.username?.charAt(0).toUpperCase() || 'U'}
                              </div>
                              <div>
                                <div className="text-white font-medium">{user.username}</div>
                                <div className="text-gray-400 text-sm">{user.nickname || '未设置昵称'}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-white">{user.email}</div>
                            <div className="text-gray-400 text-sm">{user.phoneNumber || '未绑定手机'}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 rounded text-xs font-medium ${status.class}`}>
                              {status.text}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-gray-300">
                            {new Date(user.createTime).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-gray-300">
                            {user.loginCount}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex space-x-2">
                              <button
                                onClick={() => {
                                  setSelectedUser(user);
                                  setShowUserModal(true);
                                }}
                                className="text-blue-400 hover:text-blue-300 text-sm"
                              >
                                详情
                              </button>
                              
                              {!user.isBanned ? (
                                <button
                                  onClick={() => showActionConfirm(user, 'ban')}
                                  className="text-red-400 hover:text-red-300 text-sm"
                                >
                                  封禁
                                </button>
                              ) : (
                                <button
                                  onClick={() => showActionConfirm(user, 'unban')}
                                  className="text-green-400 hover:text-green-300 text-sm"
                                >
                                  解封
                                </button>
                              )}
                              
                              {!user.isMuted && (
                                <button
                                  onClick={() => showActionConfirm(user, 'mute')}
                                  className="text-yellow-400 hover:text-yellow-300 text-sm"
                                >
                                  禁言
                                </button>
                              )}
                              
                              <button
                                onClick={() => showActionConfirm(user, 'reset-password')}
                                className="text-purple-400 hover:text-purple-300 text-sm"
                              >
                                重置密码
                              </button>
                              
                              <button
                                onClick={() => showActionConfirm(user, 'force-logout')}
                                className="text-orange-400 hover:text-orange-300 text-sm"
                              >
                                强制下线
                              </button>
                              
                              <button
                                onClick={() => showActionConfirm(user, 'delete')}
                                className="text-red-500 hover:text-red-400 text-sm"
                              >
                                注销账号
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </main>
      </div>

      {/* 用户详情模态框 */}
      {showUserModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">用户详情</h3>
              <button
                onClick={() => setShowUserModal(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                  {selectedUser.username?.charAt(0).toUpperCase() || 'U'}
                </div>
                <div>
                  <h4 className="text-white font-medium">{selectedUser.username}</h4>
                  <p className="text-gray-400 text-sm">{selectedUser.nickname || '未设置昵称'}</p>
                </div>
              </div>
              <div className="space-y-2">
                <div>
                  <span className="text-gray-400">邮箱：</span>
                  <span className="text-white">{selectedUser.email}</span>
                </div>
                <div>
                  <span className="text-gray-400">手机：</span>
                  <span className="text-white">{selectedUser.phoneNumber || '未绑定'}</span>
                </div>
                <div>
                  <span className="text-gray-400">状态：</span>
                  <span className={`${getStatusDisplay(selectedUser).class.includes('green') ? 'text-green-400' : 
                    getStatusDisplay(selectedUser).class.includes('red') ? 'text-red-400' : 
                    getStatusDisplay(selectedUser).class.includes('yellow') ? 'text-yellow-400' : 'text-gray-400'}`}>
                    {getStatusDisplay(selectedUser).text}
                  </span>
                </div>
                <div>
                  <span className="text-gray-400">注册时间：</span>
                  <span className="text-white">{new Date(selectedUser.createTime).toLocaleString()}</span>
                </div>
                <div>
                  <span className="text-gray-400">登录次数：</span>
                  <span className="text-white">{selectedUser.loginCount}</span>
                </div>
                {selectedUser.isBanned && (
                  <div>
                    <span className="text-gray-400">封禁原因：</span>
                    <span className="text-red-400">{selectedUser.banReason || '未知'}</span>
                  </div>
                )}
                {selectedUser.isMuted && (
                  <div>
                    <span className="text-gray-400">禁言到期：</span>
                    <span className="text-yellow-400">
                      {selectedUser.muteEndTime ? new Date(selectedUser.muteEndTime).toLocaleString() : '永久'}
                    </span>
                  </div>
                )}
              </div>
            </div>
            <div className="mt-6 flex justify-end">
              <button
                onClick={() => setShowUserModal(false)}
                className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 操作确认模态框 */}
      {showActionModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">
                {actionType === 'ban' && '封禁用户'}
                {actionType === 'unban' && '解封用户'}
                {actionType === 'mute' && '禁言用户'}
                {actionType === 'reset-password' && '重置密码'}
                {actionType === 'force-logout' && '强制下线'}
                {actionType === 'delete' && '注销账号'}
              </h3>
              <button
                onClick={() => setShowActionModal(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            
            <div className="mb-4">
              <p className="text-gray-300 mb-2">目标用户：{selectedUser.username}</p>
              
              {(actionType === 'ban' || actionType === 'mute') && (
                <div className="mb-4">
                  <label className="block text-gray-300 mb-2">时长（小时）：</label>
                  <select
                    value={actionDuration}
                    onChange={(e) => setActionDuration(Number(e.target.value))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                  >
                    <option value={1}>1小时</option>
                    <option value={24}>1天</option>
                    <option value={168}>7天</option>
                    <option value={720}>30天</option>
                    {actionType === 'ban' && <option value={0}>永久</option>}
                  </select>
                </div>
              )}
              
              {actionType === 'reset-password' && (
                <div className="mb-4">
                  <label className="block text-gray-300 mb-2">新密码：</label>
                  <input
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    placeholder="请输入新密码（至少6位）"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400"
                    autoComplete="new-password"
                  />
                  <p className="text-gray-400 text-sm mt-1">密码长度不能少于6位</p>
                </div>
              )}
              
              <div>
                <label className="block text-gray-300 mb-2">操作原因：</label>
                <textarea
                  value={actionReason}
                  onChange={(e) => setActionReason(e.target.value)}
                  placeholder="请输入操作原因..."
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 resize-none"
                  rows={3}
                />
              </div>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowActionModal(false)}
                className="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors"
              >
                取消
              </button>
              <button
                onClick={() => handleUserAction(
                  selectedUser.userId, 
                  actionType, 
                  actionReason || '管理员操作', 
                  actionDuration
                )}
                disabled={loading}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                {loading ? '处理中...' : '确认'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 
    </div>
  );
} 