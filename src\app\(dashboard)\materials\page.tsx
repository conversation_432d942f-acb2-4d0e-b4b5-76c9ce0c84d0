'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';

interface Material {
  materialId: number;
  title: string;
  description: string;
  imageUrl: string;
  width: number;
  height: number;
  fileSize: number;
  fileFormat: string;
  category: string;
  keyboardLayout?: string;
  keyPositions?: number;
  downloadCount: number;
  isPublic: boolean;
  tags: string;
  createTime: string;
  isOwner: boolean;
}

export default function MaterialsPage() {
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFormat, setSelectedFormat] = useState('all');
  const [sortBy, setSortBy] = useState('newest');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewMaterial, setPreviewMaterial] = useState<Material | null>(null);
  const pageSize = 20;

  // 侧边栏菜单项 - 统一深色主题风格
  const menu = [
    { 
      name: '仪表盘', 
      href: '/dashboard', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
        </svg>
      ), 
      active: false, 
      gradient: 'from-blue-500/20 to-cyan-500/20',
      borderColor: 'border-blue-500/30'
    },
    { 
      name: '设计器', 
      href: '/designer', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z" />
        </svg>
      ), 
      gradient: 'from-purple-500/20 to-pink-500/20',
      borderColor: 'border-purple-500/30'
    },
    { 
      name: '我的设计', 
      href: '/designs', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ), 
      gradient: 'from-emerald-500/20 to-teal-500/20',
      borderColor: 'border-emerald-500/30'
    },
    { 
      name: '我的素材', 
      href: '/materials/my', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 5H6a2 2 0 00-2 2v6a2 2 0 002 2h2m0-10h8a2 2 0 012 2v6a2 2 0 01-2 2h-8m0-10v10" />
        </svg>
      ), 
      gradient: 'from-orange-500/20 to-red-500/20',
      borderColor: 'border-orange-500/30'
    },
    { 
      name: '素材库', 
      href: '/materials', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ), 
      active: true, 
      gradient: 'from-indigo-500/20 to-purple-500/20',
      borderColor: 'border-indigo-500/30'
    },
  ];

  const tabs = [
    { id: 'all', name: '全部', icon: '🗂️' },
  ];

  const formats = [
    { id: 'all', name: '全部格式' },
    { id: 'PNG', name: 'PNG' },
    { id: 'JPG', name: 'JPG' },
    { id: 'SVG', name: 'SVG' }
  ];

  // 获取当前用户信息
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        setCurrentUser({ userId: payload.userId });
      } catch (error) {
        console.error('解析token失败:', error);
      }
    }
  }, []);

  // 获取公共素材列表
  const fetchPublicMaterials = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // 构建查询参数
      const params = new URLSearchParams({
        page: currentPage.toString(),
        size: pageSize.toString()
      });

      if (activeTab !== 'all') {
        params.append('category', activeTab);
      }

      if (searchQuery) {
        params.append('keyword', searchQuery);
      }

      const response = await fetch(`http://localhost:8080/api/materials/public?${params}`, {
        headers
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          setMaterials(result.data.content || []);
          setTotalPages(result.data.totalPages || 0);
          setTotalElements(result.data.totalElements || 0);
        } else {
          console.error('获取素材失败:', result.message);
          setMaterials([]);
        }
      } else {
        console.error('请求失败:', response.statusText);
        setMaterials([]);
      }
    } catch (error) {
      console.error('获取公共素材时出错:', error);
      setMaterials([]);
    } finally {
      setLoading(false);
    }
  };

  // 页面加载和筛选条件变化时获取数据
  useEffect(() => {
    fetchPublicMaterials();
  }, [activeTab, searchQuery, currentPage]);

  // 获取图片完整URL
  const getImageUrl = (imageUrl: string) => {
    if (!imageUrl) return '/images/placeholders/image-placeholder.png';
    
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    
    if (imageUrl.startsWith('/')) {
      return `http://localhost:8080${imageUrl}`;
    }
    
    return `http://localhost:8080/uploads/${imageUrl}`;
  };

  // 添加保存到我的素材功能
  const saveToMyMaterials = async (material: Material) => {
    if (!currentUser) {
      alert('请先登录');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8080/api/materials/${material.materialId}/save`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          alert('素材已保存到您的个人素材库！');
        } else {
          alert(result.message || '保存失败');
        }
      } else {
        alert('保存失败，请重试');
      }
    } catch (error) {
      console.error('保存素材失败:', error);
      alert('保存失败，请重试');
    }
  };

  // 过滤和排序素材（前端额外筛选）
  const getFilteredMaterials = () => {
    let filtered = [...materials];

    // 格式过滤
    if (selectedFormat !== 'all') {
      filtered = filtered.filter(material => material.fileFormat === selectedFormat);
    }

    // 排序
    if (sortBy === 'newest') {
      filtered.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());
    } else if (sortBy === 'popular') {
      filtered.sort((a, b) => b.downloadCount - a.downloadCount);
    }

    return filtered;
  };

  // 打开预览模态框
  const openPreviewModal = (material: Material) => {
    setPreviewMaterial(material);
    setShowPreviewModal(true);
  };

  // 关闭预览模态框
  const closePreviewModal = () => {
    setShowPreviewModal(false);
    setPreviewMaterial(null);
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // 搜索处理
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(0); // 重置到第一页
    fetchPublicMaterials();
  };

  const renderMaterialCard = (material: Material) => {
    return (
      <div key={material.materialId} className="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group">
        <div className="relative h-48 bg-gray-100 flex items-center justify-center">
          <img 
            src={getImageUrl(material.imageUrl)} 
            alt={material.title}
            className="w-full h-full object-cover cursor-pointer"
            onClick={() => openPreviewModal(material)}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = '/images/placeholders/image-placeholder.png';
            }}
          />

          {/* 悬停操作按钮 */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="flex space-x-2">
              <button 
                onClick={() => openPreviewModal(material)}
                className="bg-white text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                👁️ 预览
              </button>
              <button 
                 onClick={() => saveToMyMaterials(material)}
                 className="bg-green-500 text-white px-3 py-2 rounded-lg hover:bg-green-600 transition-colors"
              >
                 💾 保存
              </button>
            </div>
          </div>

          <div className="absolute top-3 left-3 bg-black/30 backdrop-blur-sm rounded-full px-3 py-1">
            <span className="text-white text-xs font-medium">{material.category}</span>
          </div>

          <div className="absolute top-3 right-3 bg-green-500/80 backdrop-blur-sm rounded-full px-2 py-1">
            <span className="text-white text-xs font-medium">{material.fileFormat}</span>
          </div>

          <div className="absolute bottom-3 left-3 right-3 flex items-center justify-end">
            <div className="bg-black/30 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
              <span className="text-white text-xs">📥</span>
              <span className="text-white text-xs font-medium">{material.downloadCount}</span>
            </div>
          </div>
        </div>
        
        <div className="p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-indigo-600 transition-colors">
            {material.title}
          </h3>
          
          {material.description && (
            <p className="text-sm text-gray-600 mb-3 line-clamp-2">{material.description}</p>
          )}

          <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mb-4">
            <div>尺寸: {material.width}×{material.height}</div>
            <div>格式: {material.fileFormat}</div>
            <div>大小: {formatFileSize(material.fileSize)}</div>
            <div>下载: {material.downloadCount}次</div>
          </div>

          {material.tags && (
            <div className="flex flex-wrap gap-1 mb-3">
              {material.tags.split(',').map((tag, index) => (
                <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                  {tag.trim()}
                </span>
              ))}
            </div>
          )}

          <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
            <span>📅 {formatDate(material.createTime)}</span>
            <span className="text-green-600">公开</span>
          </div>

          <div className="flex space-x-2">
            <button 
              onClick={() => saveToMyMaterials(material)}
              className="flex-1 py-2 px-4 rounded-lg transition-colors font-medium bg-indigo-600 text-white hover:bg-indigo-700"
            >
              💾 保存到我的素材
            </button>
            <button 
              onClick={() => {
                window.open(getImageUrl(material.imageUrl), '_blank');
              }}
              className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              📥 下载
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900">
      {/* 现代化侧边栏 - 深色主题 */}
      <aside className={`${sidebarOpen ? 'w-72' : 'w-20'} bg-gray-800/95 backdrop-blur-xl border-r border-gray-700/50 transition-all duration-300 ease-in-out fixed left-0 top-0 h-screen overflow-y-auto scrollbar-thin shadow-xl z-50`}>
        <div className="p-6 flex justify-between items-center">
          {sidebarOpen && (
            <Link href="/" className="flex items-center space-x-3 hover-float">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center text-white font-bold text-xs shadow-md">
                Keycap
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-medium text-white">
                  Keycap
                </span>
                <span className="text-xs text-gray-400 font-normal -mt-1">
                  Keycap Design Platform
                </span>
              </div>
            </Link>
          )}
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 rounded-lg bg-gray-700/30 text-gray-300 hover:text-white hover:bg-gray-600/40 transition-all duration-200"
          >
            {sidebarOpen ? '◀' : '▶'}
          </button>
        </div>
        
        <nav className="mt-8 px-4">
          <ul className="space-y-3">
            {menu.map((item) => (
              <li key={item.name}>
                <Link 
                  href={item.href}
                  className={`group flex items-center ${sidebarOpen ? 'px-4' : 'px-3'} py-3 rounded-xl transition-all duration-300 border backdrop-blur-sm ${
                    item.active 
                      ? `bg-gradient-to-r ${item.gradient} text-white shadow-lg border-white/10 hover:shadow-xl` 
                      : 'text-gray-400 hover:text-white hover:bg-gray-700/20 border-transparent hover:border-gray-600/30'
                  }`}
                >
                  <div className={`flex items-center justify-center w-6 h-6 ${
                    item.active ? 'text-white' : 'text-gray-400 group-hover:text-white'
                  } transition-colors duration-300`}>
                    {item.icon}
                  </div>
                  {sidebarOpen && (
                    <>
                      <span className="ml-3 font-medium tracking-wide">{item.name}</span>
                      {item.active && (
                        <div className="ml-auto">
                          <div className="w-2 h-2 rounded-full bg-white/80 shadow-sm"></div>
                        </div>
                      )}
                    </>
                  )}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </aside>

      {/* 主内容区 */}
      <div className={`flex-1 overflow-auto ${sidebarOpen ? 'ml-72' : 'ml-20'} transition-all duration-300 ease-in-out`}>
        <main className="min-h-screen">
        {/* 顶部标题栏 */}
        <div className="bg-gradient-to-r from-indigo-400 to-purple-500 text-white p-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">公共素材库</h1>
              <p className="text-indigo-100">探索和管理的设计素材资源 🎨</p>
            </div>
            <Link 
              href="/materials/upload?type=public"
              className="bg-white text-indigo-600 px-6 py-3 rounded-xl font-medium hover:bg-indigo-50 transition-colors flex items-center space-x-2"
            >
              <span>⬆️</span>
              <span>上传到公共库</span>
                    </Link>
                  </div>
              </div>
              
        {/* 筛选和搜索栏 */}
        <div className="bg-gray-800/90 backdrop-blur-md border-b border-gray-700/40 p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* 搜索框 */}
            <div className="flex-1 max-w-md">
              <form onSubmit={handleSearch} className="relative">
                <input
                  type="text"
                  placeholder="搜索素材、标签..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-transparent backdrop-blur-sm"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </form>
            </div>

            {/* 筛选器 */}
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
              <select
                value={selectedFormat}
                onChange={(e) => setSelectedFormat(e.target.value)}
                className="min-w-[140px] px-4 py-3 bg-gray-700/70 border border-gray-600/50 rounded-lg text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent backdrop-blur-sm appearance-none cursor-pointer"
                style={{
                  backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                  backgroundPosition: 'right 0.5rem center',
                  backgroundRepeat: 'no-repeat',
                  backgroundSize: '1.5em 1.5em',
                  paddingRight: '2.5rem'
                }}
              >
                {formats.map(format => (
                  <option key={format.id} value={format.id} className="bg-gray-800 text-white">
                    {format.name}
                  </option>
                ))}
              </select>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="min-w-[140px] px-4 py-3 bg-gray-700/70 border border-gray-600/50 rounded-lg text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent backdrop-blur-sm appearance-none cursor-pointer"
                style={{
                  backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                  backgroundPosition: 'right 0.5rem center',
                  backgroundRepeat: 'no-repeat',
                  backgroundSize: '1.5em 1.5em',
                  paddingRight: '2.5rem'
                }}
              >
                <option value="newest" className="bg-gray-800 text-white">最新上传</option>
                <option value="popular" className="bg-gray-800 text-white">最多下载</option>
              </select>
            </div>
          </div>

          {/* 分类标签 */}
          <div className="flex space-x-2 mt-6 overflow-x-auto">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id);
                  setCurrentPage(0); // 重置到第一页
                }}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-indigo-500/20 text-indigo-300 border border-indigo-400/30 shadow-lg'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700/30 border border-transparent hover:border-gray-600/30'
                }`}
              >
                <span className="text-sm">{tab.icon}</span>
                <span className="font-medium">{tab.name}</span>
              </button>
            ))}
          </div>
        </div>

                {/* 素材统计 */}
        <div className="px-6 py-4 bg-gray-800/70 backdrop-blur-md border-b border-gray-700/40">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-300">
              共找到 <span className="font-semibold text-white">{totalElements}</span> 个素材
              {activeTab !== 'all' && (
                <span> · 分类: <span className="font-semibold text-indigo-300">{tabs.find(t => t.id === activeTab)?.name}</span></span>
              )}
            </div>
            {totalPages > 1 && (
              <div className="text-sm text-gray-400">
                第 {currentPage + 1} 页，共 {totalPages} 页
              </div>
            )}
          </div>
        </div>

        {/* 素材网格 */}
        <div className="p-6 bg-gray-900/30 backdrop-blur-sm min-h-screen">
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="text-gray-400">正在加载素材...</div>
            </div>
          ) : getFilteredMaterials().length === 0 ? (
            <div className="text-center py-16">
              <div className="text-6xl mb-4 opacity-50">📂</div>
              <h3 className="text-xl font-semibold text-white mb-2">暂无素材</h3>
              <p className="text-gray-400 mb-6">
                {searchQuery ? '未找到匹配的素材，请尝试其他关键词' : '还没有人上传素材，成为第一个贡献者吧！'}
              </p>
              <Link 
                href="/materials/upload?type=public"
                className="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors inline-block font-medium"
              >
                上传到公共库
              </Link>
            </div>
            ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                {getFilteredMaterials().map(renderMaterialCard)}
              </div>

              {/* 分页控件 */}
              {totalPages > 1 && (
                <div className="flex justify-center space-x-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                    disabled={currentPage === 0}
                    className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    上一页
                  </button>
                  
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = Math.max(0, Math.min(totalPages - 5, currentPage - 2)) + i;
                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-4 py-2 border rounded-lg ${
                          currentPage === pageNum
                            ? 'bg-indigo-600 text-white border-indigo-600'
                            : 'border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum + 1}
                      </button>
                    );
                  })}
                  
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
                    disabled={currentPage === totalPages - 1}
                    className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    下一页
                  </button>
                </div>
              )}
            </>
            )}
          </div>
        </main>

      {/* 预览模态框 */}
      {showPreviewModal && previewMaterial && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-gray-900">{previewMaterial.title}</h3>
              <button
                onClick={closePreviewModal}
                className="text-gray-400 hover:text-gray-600 transition-colors text-3xl"
              >
                ×
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* 左侧：大图预览 */}
              <div className="lg:col-span-2">
                <div className="relative w-full bg-gray-100 rounded-lg overflow-hidden">
                  <img 
                    src={getImageUrl(previewMaterial.imageUrl)} 
                    alt={previewMaterial.title}
                    className="w-full h-auto max-h-[60vh] object-contain"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/images/placeholders/image-placeholder.png';
                    }}
                  />
                </div>
              </div>

              {/* 右侧：详细信息 */}
              <div className="space-y-6">
                {/* 基本信息 */}
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-3">基本信息</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">尺寸:</span>
                      <span className="font-medium">{previewMaterial.width}×{previewMaterial.height}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">格式:</span>
                      <span className="font-medium">{previewMaterial.fileFormat}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">大小:</span>
                      <span className="font-medium">{formatFileSize(previewMaterial.fileSize)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">分类:</span>
                      <span className="font-medium">{previewMaterial.category}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">状态:</span>
                      <span className="font-medium text-green-600">公开</span>
                    </div>
                  </div>
                </div>

                {/* 描述 */}
                {previewMaterial.description && (
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-3">描述</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">{previewMaterial.description}</p>
                  </div>
                )}

                {/* 标签 */}
                {previewMaterial.tags && (
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-3">标签</h4>
                    <div className="flex flex-wrap gap-2">
                      {previewMaterial.tags.split(',').map((tag, index) => (
                        <span key={index} className="text-xs bg-gray-100 text-gray-600 px-3 py-1 rounded-full">
                          {tag.trim()}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* 统计信息 */}
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-3">统计信息</h4>
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{previewMaterial.downloadCount}</div>
                    <div className="text-xs text-gray-600">下载次数</div>
                  </div>
                </div>

                {/* 时间信息 */}
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-3">时间信息</h4>
                  <div className="text-sm text-gray-600">
                    <div>创建时间: {formatDate(previewMaterial.createTime)}</div>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="space-y-3">
                  <button 
                    onClick={() => saveToMyMaterials(previewMaterial)}
                    className="w-full py-3 rounded-lg transition-colors font-medium bg-indigo-600 text-white hover:bg-indigo-700"
                  >
                    💾 保存到我的素材
                  </button>
                  <button 
                    onClick={() => {
                      window.open(getImageUrl(previewMaterial.imageUrl), '_blank');
                    }}
                    className="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors font-medium"
                  >
                    📥 下载原图
                  </button>
                </div>
              </div>
            </div>
          </div>
      </div>
      )}
      </div>
    </div>
  );
}