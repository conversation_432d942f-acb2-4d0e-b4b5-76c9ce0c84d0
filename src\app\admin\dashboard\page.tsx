'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAdmin } from '../../../contexts/AdminContext';

// API配置
const API_BASE_URL = 'http://localhost:8080/api';

// 接口类型定义
interface DashboardStats {
  totalUsers: number;
  onlineUsers: number;
  totalOrders: number;
  totalRevenue: number;
  recentOrders: RecentOrder[];
  recentRefunds: RecentRefund[];
  shopApplications: ShopApplication[];
}

interface RecentOrder {
  orderId: string;
  productName: string;
  status: string;
  statusText: string;
  amount: number;
  createTime: string;
  username: string;
  nickname: string;
}

interface RecentRefund {
  orderId: string;
  productName: string;
  reason: string;
  amount: number;
  refundTime: string;
}

interface ShopApplication {
  shopId: string;
  shopName: string;
  status: string;
  statusText: string;
  applyTime: string;
  applicantName: string;
}

export default function AdminDashboard() {
  const { admin, isLoading: adminLoading, logout } = useAdmin();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    if (!adminLoading) {
      if (admin) {
        loadDashboardStats();
      } else {
        router.push('/admin/login');
      }
    }
  }, [admin, adminLoading, router]);

  const loadDashboardStats = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin_token');
      
      if (!token) {
        setError('未找到管理员token');
        return;
      }

      const response = await fetch(`${API_BASE_URL}/admin/dashboard/stats`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success || result.code === 200) {
        setStats(result.data);
        setError(null);
      } else {
        setError(result.message || '获取统计数据失败');
      }
    } catch (error) {
      console.error('加载仪表盘统计数据失败:', error);
      setError('加载统计数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    if (confirm('确定要退出管理后台吗？')) {
      logout();
      router.push('/admin/login');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
      case 'DELIVERED':
      case 'APPROVED':
        return 'bg-green-600 text-green-100';
      case 'PENDING_PAYMENT':
      case 'PENDING':
        return 'bg-yellow-600 text-yellow-100';
      case 'PAID':
      case 'SHIPPED':
        return 'bg-blue-600 text-blue-100';
      case 'CANCELLED':
      case 'REFUNDED':
      case 'REJECTED':
        return 'bg-red-600 text-red-100';
      default:
        return 'bg-gray-600 text-gray-100';
    }
  };

  if (adminLoading || loading) {
    return (
      <div className="min-h-screen bg-[#1a1b26] flex items-center justify-center">
        <div className="text-white text-xl">加载中...</div>
      </div>
    );
  }

  if (!admin) {
    return null; // 会被useEffect重定向到登录页
  }

  return (
    <div className="min-h-screen bg-[#1a1b26]">
      {/* 管理员导航栏 */}
      <nav className="bg-gray-900 border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-xl flex items-center justify-center text-white font-bold text-lg">
              🔐
            </div>
            <div>
              <h1 className="text-white text-xl font-bold">管理后台</h1>
              <p className="text-gray-400 text-sm">Administrator Dashboard</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-white font-medium">{admin?.username || 'Admin'}</p>
              <p className="text-gray-400 text-sm">{admin?.role || 'super_admin'}</p>
            </div>
            <button
              onClick={handleLogout}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              退出登录
            </button>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* 侧边栏 */}
        <aside className="w-64 bg-gray-900 min-h-screen border-r border-gray-700">
          <div className="p-6">
            <nav className="space-y-2">
              <Link
                href="/admin/dashboard"
                className="flex items-center space-x-3 text-white bg-gray-800 rounded-lg px-3 py-2 transition-colors"
              >
                <span>📊</span>
                <span>仪表盘</span>
              </Link>
              <Link
                href="/admin/users"
                className="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg px-3 py-2 transition-colors"
              >
                <span>👥</span>
                <span>用户管理</span>
              </Link>
              <Link
                href="/admin/products"
                className="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg px-3 py-2 transition-colors"
              >
                <span>🛍️</span>
                <span>商品管理</span>
              </Link>
              <Link
                href="/admin/orders"
                className="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg px-3 py-2 transition-colors"
              >
                <span>📦</span>
                <span>订单管理</span>
              </Link>
              <Link
                href="/admin/customer-service"
                className="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg px-3 py-2 transition-colors"
              >
                <span>💬</span>
                <span>客服中心</span>
              </Link>
              <Link
                href="/admin/stores"
                className="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg px-3 py-2 transition-colors"
              >
                <span>🏪</span>
                <span>店铺管理</span>
              </Link>
              <Link
                href="/admin/logistics"
                className="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg px-3 py-2 transition-colors"
              >
                <span>🚚</span>
                <span>物流管理</span>
              </Link>
              <Link
                href="/admin/fund-management"
                className="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg px-3 py-2 transition-colors"
              >
                <span>💰</span>
                <span>资金管理</span>
              </Link>
              <Link
                href="/admin/settings"
                className="flex items-center space-x-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg px-3 py-2 transition-colors"
              >
                <span>⚙️</span>
                <span>系统设置</span>
              </Link>
            </nav>
          </div>
        </aside>

        {/* 主内容区域 */}
        <main className="flex-1 p-8">
          {/* 欢迎信息 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">
              欢迎回来，{admin?.username || 'admin'} 👋
            </h1>
            <p className="text-gray-400">
              这里是灵狐键创管理后台，您可以管理系统的各个方面
            </p>
          </div>

          {error && (
            <div className="mb-6 p-4 bg-red-900 border border-red-700 rounded-lg">
              <p className="text-red-200">{error}</p>
              <button
                onClick={loadDashboardStats}
                className="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                重试
              </button>
            </div>
          )}

          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">总用户数</p>
                  <p className="text-3xl font-bold">{stats?.totalUsers?.toLocaleString() || '0'}</p>
                </div>
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">👥</span>
                </div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-green-600 to-green-700 rounded-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">在线用户</p>
                  <p className="text-3xl font-bold">{stats?.onlineUsers?.toLocaleString() || '0'}</p>
                </div>
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">🟢</span>
                </div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm">总订单</p>
                  <p className="text-3xl font-bold">{stats?.totalOrders?.toLocaleString() || '0'}</p>
                </div>
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">📦</span>
                </div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-orange-600 to-orange-700 rounded-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm">总收入</p>
                  <p className="text-3xl font-bold">{stats?.totalRevenue ? formatCurrency(stats.totalRevenue) : '¥0'}</p>
                </div>
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">💰</span>
                </div>
              </div>
            </div>
          </div>

          {/* 功能快捷入口 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 最近订单 */}
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-700">
              <h3 className="text-white text-lg font-semibold mb-4">最近订单</h3>
              <div className="space-y-3">
                {stats?.recentOrders?.length ? (
                  stats.recentOrders.map((order, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                  <div>
                        <p className="text-white font-medium">{order.orderId}</p>
                        <p className="text-gray-400 text-sm">{order.productName}</p>
                        <p className="text-blue-400 text-sm">
                          用户：{order.username}
                          {order.nickname && ` (${order.nickname})`}
                        </p>
                        <p className="text-gray-500 text-xs">{formatDate(order.createTime)}</p>
                  </div>
                      <div className="text-right">
                        <span className={`px-2 py-1 text-xs rounded ${getStatusColor(order.status)}`}>
                          {order.statusText}
                        </span>
                        <p className="text-white text-sm mt-1">{formatCurrency(order.amount)}</p>
                </div>
                  </div>
                  ))
                ) : (
                  <p className="text-gray-400 text-center py-4">暂无订单数据</p>
                )}
              </div>
              <Link 
                href="/admin/orders"
                className="block mt-4 text-center py-2 bg-gray-800 text-gray-300 hover:text-white rounded-lg transition-colors"
              >
                查看所有订单 →
              </Link>
            </div>

            {/* 最近退款 */}
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-700">
              <h3 className="text-white text-lg font-semibold mb-4">最近退款</h3>
              <div className="space-y-3">
                {stats?.recentRefunds?.length ? (
                  stats.recentRefunds.map((refund, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                      <div>
                        <p className="text-white font-medium">{refund.orderId}</p>
                        <p className="text-gray-400 text-sm">{refund.productName}</p>
                        <p className="text-gray-500 text-xs">{refund.reason}</p>
                        <p className="text-gray-500 text-xs">{formatDate(refund.refundTime)}</p>
                      </div>
                      <div className="text-right">
                        <span className="px-2 py-1 bg-red-600 text-red-100 text-xs rounded">已退款</span>
                        <p className="text-white text-sm mt-1">{formatCurrency(refund.amount)}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-400 text-center py-4">暂无退款数据</p>
                )}
              </div>
              <Link 
                href="/admin/orders?status=refunded"
                className="block mt-4 text-center py-2 bg-gray-800 text-gray-300 hover:text-white rounded-lg transition-colors"
              >
                查看所有退款 →
              </Link>
            </div>

            {/* 店铺申请 */}
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-700">
              <h3 className="text-white text-lg font-semibold mb-4">店铺申请</h3>
              <div className="space-y-3">
                {stats?.shopApplications?.length ? (
                  stats.shopApplications.map((shop, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                  <div>
                        <p className="text-white font-medium">{shop.shopName}</p>
                        <p className="text-gray-400 text-sm">申请人：{shop.applicantName}</p>
                        <p className="text-gray-500 text-xs">申请时间：{formatDate(shop.applyTime)}</p>
                  </div>
                      <span className={`px-2 py-1 text-xs rounded ${getStatusColor(shop.status)}`}>
                        {shop.statusText}
                      </span>
                </div>
                  ))
                ) : (
                  <p className="text-gray-400 text-center py-4">暂无申请数据</p>
                )}
              </div>
              <Link 
                href="/admin/stores"
                className="block mt-4 text-center py-2 bg-gray-800 text-gray-300 hover:text-white rounded-lg transition-colors"
              >
                管理店铺申请 →
              </Link>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
} 