'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useTranslations } from '../contexts/LocaleContext';
import LanguageSwitcher from '../components/LanguageSwitcher';

export default function Home() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [websiteConfig, setWebsiteConfig] = useState<any>(null);
  const [configLoading, setConfigLoading] = useState(true);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [previewConfig, setPreviewConfig] = useState<any>(null);
  const [promotionalVideo, setPromotionalVideo] = useState('');
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [visibleSections, setVisibleSections] = useState<Set<string>>(new Set(['welcome']));
  const [hotProducts, setHotProducts] = useState<any[]>([]);
  const [featuredContent, setFeaturedContent] = useState<any[]>([]);
  const [loadingProducts, setLoadingProducts] = useState(true);
  const [configProducts, setConfigProducts] = useState<any[]>([]); // 后台配置的产品
  const { user, logout, isLoading } = useAuth();
  const router = useRouter();
  const t = useTranslations();

  const handleDesignerClick = (e: React.MouseEvent) => {
    if (!user) {
      e.preventDefault();
      alert(t('auth.pleaseLoginToUseDesigner'));
      router.push('/login');
    }
  };

  // 检测预览模式
  const checkPreviewMode = () => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const isPreview = urlParams.get('preview') === 'admin';
      
      if (isPreview) {
        const previewData = sessionStorage.getItem('admin_preview_config');
        if (previewData) {
          try {
            const parsed = JSON.parse(previewData);
            setPreviewConfig(parsed);
            setIsPreviewMode(true);
            console.log('✅ 预览模式激活，配置数量:', Object.keys(parsed.configMap || {}).length);
          } catch (error) {
            console.error('❌ 解析预览配置失败:', error);
          }
        }
      }
    }
  };

  // 获取网站配置
  const loadWebsiteConfig = async () => {
    try {
      const url = `http://localhost:8080/api/website-config/published?t=${Date.now()}&cache=false`;
      const response = await fetch(url, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
      const data = await response.json();
      
      if (data.code === 200 && data.data) {
        let finalConfig = data.data;
        
        // 如果是预览模式，使用预览配置覆盖
        if (isPreviewMode && previewConfig?.configMap) {
          const mergedConfig = { ...data.data, ...previewConfig.configMap };
          finalConfig = mergedConfig;
          console.log('🔍 预览模式：使用合并后的配置');
        }
        
        setWebsiteConfig(finalConfig);
        console.log('✅ 网站配置加载完成:', finalConfig);
        
        // 详细调试信息
        if (finalConfig.settings) {
          console.log('📋 配置数据结构 - 分类数量:', Object.keys(finalConfig.settings).length);
          console.log('📋 配置分类:', Object.keys(finalConfig.settings));
          for (const category in finalConfig.settings) {
            console.log(`📂 分类 ${category}:`, Object.keys(finalConfig.settings[category]).length, '个配置项');
            if (category === 'products') {
              console.log(`🛍️ 产品配置示例:`, {
                product_1_name: finalConfig.settings[category].product_1_name,
                product_1_price: finalConfig.settings[category].product_1_price
              });
            }
            if (category === 'hero') {
              console.log(`🏠 首页配置:`, finalConfig.settings[category]);
            }
          }
        } else {
          console.log('📋 配置数据为扁平结构');
        }
        
        // 设置宣传视频
        let videoUrl = '';
        if (finalConfig.settings) {
          // 处理嵌套结构，查找所有分类中的视频URL
          for (const category in finalConfig.settings) {
            const categorySettings = finalConfig.settings[category];
            if (categorySettings.promotional_video_url) {
              videoUrl = categorySettings.promotional_video_url;
              break;
            }
            if (categorySettings.promo_video_url) {
              videoUrl = categorySettings.promo_video_url;
              break;
            }
          }
        } else {
          // 直接的扁平结构
          videoUrl = finalConfig.promotional_video_url || finalConfig.promo_video_url;
        }
        
        if (videoUrl) {
          const fullVideoUrl = videoUrl.startsWith('http') ? videoUrl : `http://localhost:8080${videoUrl}`;
          setPromotionalVideo(fullVideoUrl);
          console.log('✅ 宣传视频设置完成:', fullVideoUrl);
        } else {
          console.log('⚠️ 未找到宣传视频配置');
        }
        
        // 解析后台配置的产品
        console.log('🔄 开始解析配置产品...');
        parseConfigProducts(finalConfig);
        
        console.log('✅ 配置加载和解析完成');
        
        // 输出页脚配置信息（在状态更新后通过useEffect输出）
        
      } else {
        console.warn('⚠️ 获取网站配置失败:', data);
      }
    } catch (error) {
      console.error('❌ 获取网站配置失败:', error);
    } finally {
      setConfigLoading(false);
    }
  };

  // 解析后台配置的产品
  const parseConfigProducts = (config: any) => {
    try {
      const products = [];
      
      // 展平配置数据
      let flatConfig: { [key: string]: string } = {};
      if (config.settings) {
        // 处理嵌套结构
        for (const category in config.settings) {
          const categorySettings = config.settings[category];
          for (const configKey in categorySettings) {
            flatConfig[configKey] = categorySettings[configKey];
          }
        }
      } else {
        // 直接的扁平结构
        flatConfig = config;
      }
      
      console.log('🔍 展平后的配置数据示例:', {
        products_data: flatConfig.products_data ? '有产品数据JSON' : '无',
        promo_video_title: flatConfig.promo_video_title,
        hero_title1: flatConfig.hero_title1
      });
      
      // 优先解析 products_data JSON 格式（管理员后台使用的格式）
      if (flatConfig.products_data) {
        try {
          const productsData = JSON.parse(flatConfig.products_data);
          console.log('🔍 解析products_data JSON:', productsData);
          
          if (Array.isArray(productsData)) {
            productsData.forEach((productConfig, index) => {
              const product = {
                id: productConfig.id || `config-${index + 1}`,
                name: productConfig.name,
                description: productConfig.description || '高品质键帽产品',
                price: productConfig.price || 199,
                originalPrice: productConfig.originalPrice || Math.floor((productConfig.price || 199) * 1.25),
                gradient: productConfig.gradient || 'from-blue-500/20 to-cyan-500/20',
                discount: productConfig.originalPrice ? 
                  Math.round((1 - (productConfig.price || 199) / productConfig.originalPrice) * 100) : 20,
                rating: 4.8,
                sales: Math.floor(Math.random() * 500) + 100,
                images: productConfig.imageUrl ? [
                  productConfig.imageUrl.startsWith('http') ? productConfig.imageUrl : `http://localhost:8080${productConfig.imageUrl}`
                ] : ['/images/placeholders/image-placeholder.png'],
                tags: ['精选', '热销'],
                isFromConfig: true
              };
              products.push(product);
              console.log(`✅ 解析产品 ${index + 1}:`, product.name, '价格:', product.price);
            });
          }
        } catch (error) {
          console.error('❌ 解析products_data JSON失败:', error);
        }
      }
      
      // 如果没有products_data，尝试老格式（product_1_name等）作为兜底
      if (products.length === 0) {
        console.log('🔄 尝试解析老格式产品配置...');
        for (let i = 1; i <= 10; i++) {
          const nameKey = `product_${i}_name`;
          const descKey = `product_${i}_description`;
          const priceKey = `product_${i}_price`;
          const originalPriceKey = `product_${i}_original_price`;
          const gradientKey = `product_${i}_gradient`;
          
          if (flatConfig[nameKey]) {
            const product = {
              id: `config-${i}`,
              name: flatConfig[nameKey],
              description: flatConfig[descKey] || '高品质键帽产品',
              price: parseInt(flatConfig[priceKey]) || 199,
              originalPrice: parseInt(flatConfig[originalPriceKey]) || Math.floor((parseInt(flatConfig[priceKey]) || 199) * 1.25),
              gradient: flatConfig[gradientKey] || 'from-blue-500/20 to-cyan-500/20',
              discount: Math.round((1 - (parseInt(flatConfig[priceKey]) || 199) / (parseInt(flatConfig[originalPriceKey]) || Math.floor((parseInt(flatConfig[priceKey]) || 199) * 1.25))) * 100),
              rating: 4.8,
              sales: Math.floor(Math.random() * 500) + 100,
              images: ['/images/placeholders/image-placeholder.png'],
              tags: ['精选', '热销'],
              isFromConfig: true
            };
            products.push(product);
            console.log(`✅ 解析老格式产品 ${i}:`, product.name, '价格:', product.price);
          }
        }
      }
      
      console.log('✅ 解析后台配置产品完成:', products.length, '个产品');
      if (products.length > 0) {
        console.log('📦 配置产品详情:', products.map(p => ({ name: p.name, price: p.price, id: p.id })));
        console.log('🔄 设置配置产品状态，这将触发useEffect更新热销产品');
      }
      setConfigProducts(products);
    } catch (error) {
      console.error('❌ 解析后台配置产品失败:', error);
    }
  };

  // 获取真实的热销产品数据（仅用于API产品，配置产品由useEffect直接处理）
  const loadHotProducts = async () => {
    try {
      setLoadingProducts(true);
      console.log('🔄 开始加载API热销产品（非配置产品）');
      
      // 从仪表盘API获取热销产品
      const dashboardResponse = await fetch('http://localhost:8080/api/dashboard/data', {
        cache: 'no-store'
      });
      
      if (dashboardResponse.ok) {
        const dashboardData = await dashboardResponse.json();
        
        if (dashboardData.code === 200 && dashboardData.data?.hotSales) {
          console.log('✅ 获取到仪表盘热销产品:', dashboardData.data.hotSales);
          // API产品限制为6个，避免页面过长
          setHotProducts(dashboardData.data.hotSales.slice(0, 6));
          setLoadingProducts(false);
          return;
        }
      }
      
      // 如果仪表盘API失败，尝试直接从商品API获取
      const productsResponse = await fetch('http://localhost:8080/api/products', {
        cache: 'no-store'
      });
      
      if (productsResponse.ok) {
        const productsData = await productsResponse.json();
        
        if (productsData.code === 200 && productsData.data) {
          console.log('✅ 从商品API获取到产品:', productsData.data);
          
          // 转换商品数据格式并按销量排序
          const convertedProducts = productsData.data
            .map((product: any) => ({
              id: product.id || product.productId,
              name: product.name || product.productName,
              price: product.price,
              originalPrice: Math.floor(product.price * 1.25),
              discount: 20,
              rating: 4.8,
              sales: product.salesCount || 0,
              images: parseImages(product.images),
              tags: getProductTags(product),
              description: product.description || '高品质键帽产品'
            }))
            .sort((a: any, b: any) => b.sales - a.sales)
            .slice(0, 6); // API产品限制为6个
          
          setHotProducts(convertedProducts);
          setLoadingProducts(false);
          return;
        }
      }
      
      // 如果都失败了，使用默认数据
      console.log('⚠️ 使用默认热销产品数据');
      setHotProducts(getDefaultHotProducts());
      
    } catch (error) {
      console.error('❌ 获取热销产品失败:', error);
      setHotProducts(getDefaultHotProducts());
    } finally {
      setLoadingProducts(false);
    }
  };

  // 解析商品图片
  const parseImages = (images: any) => {
    if (Array.isArray(images)) {
      return images;
    }
    if (typeof images === 'string') {
      try {
        const parsed = JSON.parse(images);
        return Array.isArray(parsed) ? parsed : ['/images/placeholders/image-placeholder.png'];
      } catch {
        return ['/images/placeholders/image-placeholder.png'];
      }
    }
    return ['/images/placeholders/image-placeholder.png'];
  };

  // 获取产品标签
  const getProductTags = (product: any) => {
    const tags = [];
    if (product.salesCount > 50) tags.push('热销');
    if (product.isNew) tags.push('新品');
    if (product.isHot) tags.push('推荐');
    return tags.length > 0 ? tags : ['精选'];
  };

  // 默认热销产品数据
  const getDefaultHotProducts = () => [
    {
      id: '1',
      name: 'RGB背光键帽套装',
      price: 159,
      originalPrice: 199,
      discount: 20,
      rating: 4.8,
      sales: 856,
      images: ['/images/placeholders/image-placeholder.png'],
      tags: ['热销', '背光'],
      description: '支持RGB背光的高品质键帽'
    },
    {
      id: '2',
      name: 'Artisan手工键帽',
      price: 399,
      originalPrice: 499,
      discount: 20,
      rating: 4.9,
      sales: 432,
      images: ['/images/placeholders/image-placeholder.png'],
      tags: ['限量', '手工'],
      description: '纯手工制作的艺术键帽'
    },
    {
      id: '3',
      name: '机械轴测试器',
      price: 89,
      originalPrice: 109,
      discount: 18,
      rating: 4.7,
      sales: 623,
      images: ['/images/placeholders/image-placeholder.png'],
      tags: ['实用', '测试'],
      description: '体验不同机械轴的手感差异'
    }
  ];

  // 获取精选视频内容（专门用于视频展示）
  const loadFeaturedContent = async () => {
    try {
      const content = [];
      
      // 精选作品区域专门用于视频展示
      if (promotionalVideo) {
        content.push({
          id: 'promo-video',
          type: 'video',
          title: websiteConfig?.promo_video_title || websiteConfig?.promotional_video_title || '灵狐键创宣传视频',
          subtitle: websiteConfig?.promo_video_description || '发现键帽设计的无限可能',
          videoUrl: promotionalVideo,
          badge: '精选视频',
          isMainVideo: true
        });
      } else {
        // 如果没有配置视频，显示提示信息
        content.push({
          id: 'no-video',
          type: 'placeholder',
          title: '暂无精选视频',
          subtitle: '管理员可在后台上传宣传视频',
          badge: '待上传',
          placeholder: true
        });
      }
      
      console.log('✅ 精选视频内容构建完成:', content);
      setFeaturedContent(content);
      
    } catch (error) {
      console.error('❌ 获取精选视频内容失败:', error);
    }
  };

  // 监听鼠标移动
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // 监听滚动事件实现滚动动画
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const sections = ['welcome', 'featured', 'products', 'about', 'contact'];
      
      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top < window.innerHeight * 0.7 && rect.bottom > 0) {
            setVisibleSections(prev => new Set([...Array.from(prev), section]));
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 注：精选视频区域不需要轮播功能

  // 数据加载
  useEffect(() => {
    console.log('🚀 首页初始化，开始加载配置...');
    checkPreviewMode();
    loadWebsiteConfig();
  }, []);

  useEffect(() => {
    if (websiteConfig) {
      console.log('🔄 触发精选内容加载，配置产品数量:', configProducts.length);
      loadFeaturedContent();
      
      // 输出页脚配置信息
      console.log('🦶 页脚配置信息:', {
        site_name: getConfigValue('site_name', '灵狐键创'),
        site_name_en: getConfigValue('site_name_en', 'Soul Fox Keycap Creation'),
        footer_description: getConfigValue('footer_description', '专业的键帽设计与销售平台'),
        footer_contact_email: getConfigValue('footer_contact_email', '<EMAIL>'),
        footer_copyright: getConfigValue('footer_copyright', '© 2025 灵狐键创'),
        footer_about_text: getConfigValue('footer_about_text', '灵狐键创致力于为键盘爱好者提供最优质的设计工具'),
        footer_social_text: getConfigValue('footer_social_text', '关注我们的社交媒体')
      });
    }
  }, [websiteConfig, promotionalVideo, configProducts]);

  // 使用独立的useEffect来处理热销产品更新
  useEffect(() => {
    console.log('🔄 configProducts状态变化:', {
      length: configProducts.length,
      products: configProducts.map(p => ({ name: p.name, price: p.price }))
    });
    
    if (configProducts.length > 0) {
      console.log('✅ 检测到配置产品，立即更新热销产品显示');
      console.log('📊 显示所有配置产品:', configProducts.length, '个');
      setHotProducts(configProducts); // 显示所有配置产品
      setLoadingProducts(false);
    }
  }, [configProducts]);

  // 当websiteConfig加载完成但没有配置产品时，加载API产品
  useEffect(() => {
    console.log('🔄 websiteConfig状态变化:', {
      hasConfig: !!websiteConfig,
      configLoading,
      configProductsLength: configProducts.length
    });
    
    if (websiteConfig && !configLoading && configProducts.length === 0) {
      console.log('✅ 配置加载完成但无配置产品，加载API产品');
      loadHotProducts();
    }
  }, [websiteConfig, configLoading, configProducts.length]);

  // 获取产品图片URL
  const getProductImageUrl = (imagePath: string) => {
    if (!imagePath) return '/images/placeholders/image-placeholder.png';
    if (imagePath.startsWith('http')) return imagePath;
    return `http://localhost:8080${imagePath}`;
  };

  // 调试当前状态
  const debugCurrentState = () => {
    console.log('🔍 当前状态调试:', {
      configProductsLength: configProducts.length,
      hotProductsLength: hotProducts.length,
      configLoading,
      hasWebsiteConfig: !!websiteConfig,
      loadingProducts
    });
    if (configProducts.length > 0) {
      console.log('📦 当前配置产品:', configProducts.map(p => ({ name: p.name, price: p.price })));
    }
    if (hotProducts.length > 0) {
      console.log('🔥 当前热销产品:', hotProducts.map(p => ({ name: p.name, price: p.price })));
    }
  };

  // 强制更新热销产品
  const forceUpdateHotProducts = () => {
    console.log('🔄 强制更新热销产品...');
    debugCurrentState();
    if (configProducts.length > 0) {
      console.log('✅ 强制使用配置产品更新热销产品');
      console.log('📊 强制显示所有配置产品:', configProducts.length, '个');
      setHotProducts(configProducts); // 显示所有配置产品
      setLoadingProducts(false);
    } else {
      console.log('⚠️ 没有配置产品，重新加载配置和热销产品');
      // 重新加载配置
      loadWebsiteConfig();
    }
  };

  // 获取配置值的辅助函数
  const getConfigValue = (key: string, defaultValue: string = '') => {
    if (!websiteConfig) return defaultValue;
    
    // 如果配置数据有嵌套的settings结构，需要展平处理
    if (websiteConfig.settings) {
      // 展平所有分类中的配置项
      const flatConfig: { [key: string]: string } = {};
      for (const category in websiteConfig.settings) {
        const categorySettings = websiteConfig.settings[category];
        for (const configKey in categorySettings) {
          flatConfig[configKey] = categorySettings[configKey];
        }
      }
      return flatConfig[key] || defaultValue;
    }
    
    // 兼容直接的扁平结构
    return websiteConfig[key] || defaultValue;
  };

  // 返回首页内容
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#2a1f3e] via-[#1e1432] to-[#0f0a1a] relative overflow-hidden scroll-smooth">
      {/* 鼠标跟随光效 */}
      <div 
        className="fixed w-96 h-96 rounded-full pointer-events-none z-0 opacity-20"
        style={{
          background: 'radial-gradient(circle, rgba(139, 92, 246, 0.3) 0%, transparent 70%)',
          left: mousePosition.x - 192,
          top: mousePosition.y - 192,
          transition: 'all 0.1s ease-out'
        }}
      />

      {/* 预览模式提示条 */}
      {isPreviewMode && (
        <div className="fixed top-0 left-0 right-0 bg-orange-500 text-white text-center py-2 z-50">
          <span className="font-medium">🔍 预览模式</span> - 这是管理员配置的预览效果
        </div>
      )}

      {/* 调试工具条（开发时使用） */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 z-50 space-x-2">
          <button
            onClick={debugCurrentState}
            className="px-3 py-1 bg-blue-600 text-white text-xs rounded shadow-lg hover:bg-blue-700"
          >
            调试状态
          </button>
          <button
            onClick={forceUpdateHotProducts}
            className="px-3 py-1 bg-green-600 text-white text-xs rounded shadow-lg hover:bg-green-700"
          >
            强制更新
          </button>
        </div>
      )}

      {/* 导航栏 */}
      <nav className={`fixed left-0 right-0 z-40 bg-[#2a1f3e]/90 backdrop-blur-lg border-b border-purple-800/40 ${isPreviewMode ? 'top-10' : 'top-0'} shadow-lg`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-8">
              <Link href="/" className="flex items-center space-x-3 group">
                <div className="px-4 py-2 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm transform group-hover:scale-110 transition-all duration-300 shadow-lg">
                  {getConfigValue('site_logo_text', 'Keycap')}
                </div>
                <div className="flex flex-col">
                  <span className="text-xl font-bold text-white transition-all duration-300">
                    {getConfigValue('site_name', '灵狐键创')}
                  </span>
                  <span className="text-xs text-gray-400 -mt-1 group-hover:text-gray-300 transition-colors duration-300">
                    {getConfigValue('site_name_en', 'Soul Fox Keycap Creation')}
                  </span>
                </div>
              </Link>
              
              {/* 导航菜单 */}
              <div className="hidden md:flex items-center space-x-6">
                <Link href="/shop" className="text-gray-300 hover:text-white transition-colors duration-300">
                  商城
                </Link>
                <Link href="/community" className="text-gray-300 hover:text-white transition-colors duration-300">
                  社区
                </Link>
                <Link href="/designer" className="text-gray-300 hover:text-white transition-colors duration-300">
                  设计器
                </Link>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <LanguageSwitcher darkMode={true} />
              {user ? (
                <>
                  <span className="text-gray-300 hidden sm:inline">欢迎, {user.username}</span>
                  <Link 
                    href="/dashboard" 
                    className="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 shadow-md transition-all duration-300"
                  >
                    仪表盘
                  </Link>
                  <button 
                    onClick={logout}
                    className="px-4 py-2 text-gray-300 hover:text-white transition-colors border border-purple-700/40 rounded-lg hover:border-purple-500/60"
                  >
                    退出
                  </button>
                </>
              ) : (
                <>
                  <Link 
                    href="/login" 
                    className="px-6 py-2.5 text-gray-300 hover:text-white transition-all duration-300 hover:bg-[#1e1432]/50 rounded-full border border-purple-700/40 hover:border-purple-500/60 hover:scale-105 backdrop-blur-sm"
                  >
                    登录
                  </Link>
                  <Link 
                    href="/register" 
                    className="px-6 py-2.5 bg-gradient-to-r from-cyan-500 to-purple-600 text-white rounded-full hover:shadow-xl hover:shadow-purple-500/25 transition-all duration-300 hover:scale-105 font-medium relative overflow-hidden group"
                  >
                    <span className="relative z-10">注册</span>
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-cyan-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* 主横幅区域 */}
      <section id="welcome" className={`relative min-h-screen flex items-center justify-center overflow-hidden ${visibleSections.has('welcome') ? 'opacity-100' : 'opacity-0'} transition-opacity duration-1000`}>
        {/* 动态渐变背景效果 */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-[#2a1f3e] via-[#1e1432] to-[#0f0a1a]"></div>
          <div className="absolute top-1/4 left-1/4 w-[600px] h-[600px] bg-cyan-400 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-blob"></div>
          <div className="absolute top-1/3 right-1/4 w-[500px] h-[500px] bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl opacity-15 animate-blob animation-delay-2000"></div>
          <div className="absolute bottom-1/4 left-1/3 w-[550px] h-[550px] bg-pink-500 rounded-full mix-blend-multiply filter blur-3xl opacity-12 animate-blob animation-delay-4000"></div>
        </div>

        <div className="relative z-10 text-center px-4 max-w-6xl mx-auto">
          <div className="animate-fade-in-up">
            <h1 className="text-6xl md:text-8xl font-bold mb-6 leading-tight">
              <span className="bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 text-transparent bg-clip-text animate-gradient-text tracking-widest">
                {getConfigValue('hero_title1', '重新定义')}
              </span>
              <br />
              <span className="bg-gradient-to-r from-pink-500 via-purple-500 to-cyan-400 text-transparent bg-clip-text animate-gradient-text-reverse tracking-widest">
                {getConfigValue('hero_title2', '键帽设计艺术')}
              </span>
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto animate-fade-in-up animation-delay-300">
              {getConfigValue('hero_subtitle', '用科技点亮创意，让每一次敲击都成为艺术表达')} ✨
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up animation-delay-600">
            <Link 
              href="/designer" 
              className="group relative inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white transition-all duration-500 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-full hover:shadow-2xl hover:shadow-purple-500/40 hover:scale-110 overflow-hidden backdrop-blur-sm"
              onClick={handleDesignerClick}
            >
              <span className="relative z-10 flex items-center">
                {getConfigValue('hero_start_button_text', '开始创作')} ✨
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-cyan-500 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </Link>
            <Link 
              href="/shop" 
              className="group inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-gray-300 transition-all duration-500 border-2 border-purple-700/50 rounded-full hover:border-purple-400 hover:text-white hover:bg-purple-500/20 hover:scale-110 backdrop-blur-lg"
            >
              {getConfigValue('hero_browse_button_text', '浏览作品')} 👀
            </Link>
          </div>
        </div>

        {/* 滚动提示 */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center animate-bounce">
          <div className="text-gray-400 text-sm mb-2">向下滚动探索更多</div>
          <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* 精选视频播放区域 */}
      <section id="featured" className={`py-20 bg-gradient-to-r from-purple-900/20 to-cyan-900/20 ${visibleSections.has('featured') ? 'animate-slide-in-up' : 'opacity-0'} transition-all duration-1000`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              <span className="bg-gradient-to-r from-cyan-400 to-purple-500 text-transparent bg-clip-text">
                精选视频
              </span>
            </h2>
            <p className="text-gray-300 text-lg">键帽设计与工艺的精彩展示</p>
          </div>

          {featuredContent.length > 0 && (
            <div className="max-w-5xl mx-auto">
              {featuredContent.map((content, index) => (
                <div key={content.id} className="relative">
                  <div className="bg-gradient-to-r from-purple-800/30 to-cyan-800/30 backdrop-blur-lg rounded-2xl overflow-hidden shadow-2xl">
                    {content.type === 'video' ? (
                      <div className="grid md:grid-cols-5 gap-0">
                        {/* 视频信息区域 */}
                        <div className="md:col-span-2 p-8 flex flex-col justify-center">
                          <div className="inline-block bg-gradient-to-r from-cyan-500 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium mb-4 w-fit">
                            {content.badge}
                          </div>
                          <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">{content.title}</h3>
                          <p className="text-gray-300 text-base md:text-lg mb-6">{content.subtitle}</p>
                          <div className="space-y-3 text-sm text-gray-400">
                            <div className="flex items-center space-x-2">
                              <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                              <span>支持高清播放</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                              <span>专业拍摄制作</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                              <span>展示制作工艺</span>
                            </div>
                          </div>
                        </div>
                        
                        {/* 视频播放区域 */}
                        <div className="md:col-span-3 relative">
                          <div className="aspect-video bg-black rounded-r-2xl overflow-hidden">
                            <video
                              src={content.videoUrl}
                              className="w-full h-full object-cover"
                              controls
                              autoPlay
                              loop
                              muted
                              preload="metadata"
                              poster="/images/placeholders/image-placeholder.png"
                            />
                          </div>
                        </div>
                      </div>
                    ) : content.type === 'placeholder' ? (
                      // 无视频时的占位符
                      <div className="text-center py-20">
                        <div className="inline-block bg-gradient-to-r from-gray-600 to-gray-700 text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
                          {content.badge}
                        </div>
                        <div className="text-6xl mb-6 opacity-50">🎬</div>
                        <h3 className="text-3xl font-bold text-white mb-4">{content.title}</h3>
                        <p className="text-gray-400 text-lg mb-8">{content.subtitle}</p>
                        <div className="max-w-md mx-auto text-sm text-gray-500 bg-gray-800/50 rounded-lg p-4">
                          <p className="mb-2">📋 管理员可以在后台：</p>
                          <ul className="text-left space-y-1">
                            <li>• 上传宣传视频文件</li>
                            <li>• 设置视频标题和描述</li>
                            <li>• 预览和发布配置</li>
                          </ul>
                        </div>
                      </div>
                    ) : null}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* 热销产品展示 */}
      <section id="products" className={`py-20 ${visibleSections.has('products') ? 'animate-slide-in-up' : 'opacity-0'} transition-all duration-1000`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              <span className="bg-gradient-to-r from-pink-400 to-purple-500 text-transparent bg-clip-text">
                热销产品
              </span>
            </h2>
            <p className="text-gray-300 text-lg">用户最喜爱的键帽产品</p>
          </div>

          {loadingProducts ? (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-400"></div>
              <span className="ml-4 text-gray-300">加载热销产品中...</span>
            </div>
          ) : (
            <div className="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {hotProducts.map((product, index) => (
                <div 
                  key={product.id} 
                  className={`group bg-gradient-to-b ${product.gradient || 'from-purple-800/20 to-cyan-800/20'} backdrop-blur-lg rounded-2xl overflow-hidden hover:shadow-2xl hover:shadow-purple-500/25 transition-all duration-500 hover:scale-105`}
                  style={{ animationDelay: `${index * 200}ms` }}
                >
                  {/* 产品图片 */}
                  <div className="relative h-64 bg-gradient-to-br from-gray-800 to-gray-900 overflow-hidden">
                    <Image
                      src={getProductImageUrl(product.images?.[0])}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      -{product.discount}%
                    </div>
                    <div className="absolute top-4 left-4 flex flex-wrap gap-1">
                      {product.tags?.map((tag: string, tagIndex: number) => (
                        <span key={tagIndex} className="bg-black/50 text-white px-2 py-1 rounded text-xs">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* 产品信息 */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors">
                      {product.name}
                    </h3>
                    <p className="text-gray-300 text-sm mb-4">{product.description}</p>
                    
                    {/* 评分和销量 */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-1">
                        {[...Array(5)].map((_, i) => (
                          <span key={i} className={`text-sm ${i < Math.floor(product.rating || 4.8) ? 'text-yellow-400' : 'text-gray-500'}`}>
                            ★
                          </span>
                        ))}
                        <span className="text-gray-400 text-sm ml-1">({product.rating || 4.8})</span>
                      </div>
                      <span className="text-gray-400 text-sm">销量 {product.sales}</span>
                    </div>

                    {/* 价格和购买按钮 */}
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-2xl font-bold text-cyan-400">¥{product.price}</span>
                        <span className="text-gray-500 text-sm line-through ml-2">¥{product.originalPrice}</span>
                      </div>
                      <Link 
                        href="/shop"
                        className="bg-gradient-to-r from-cyan-500 to-purple-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300 hover:scale-105 text-sm font-medium"
                      >
                        立即购买
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="text-center mt-12">
            <Link 
              href="/shop"
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-cyan-600 text-white px-8 py-3 rounded-full hover:shadow-xl hover:shadow-purple-500/25 transition-all duration-300 hover:scale-105 font-medium"
            >
              <span>查看更多产品</span>
              <span>→</span>
            </Link>
          </div>
        </div>
      </section>

      {/* 关于我们 */}
      <section id="about" className={`py-20 bg-gradient-to-r from-cyan-900/20 to-purple-900/20 ${visibleSections.has('about') ? 'animate-slide-in-up' : 'opacity-0'} transition-all duration-1000`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-white mb-6">
                <span className="bg-gradient-to-r from-cyan-400 to-purple-500 text-transparent bg-clip-text">
                  为什么选择我们？
                </span>
              </h2>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-full flex items-center justify-center text-2xl">
                    🎨
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white mb-2">专业设计工具</h3>
                    <p className="text-gray-300">强大的在线设计器，让你的创意无限发挥</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-2xl">
                    ⚡
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white mb-2">快速生产</h3>
                    <p className="text-gray-300">高效的生产流程，快速将设计变为现实</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-red-500 rounded-full flex items-center justify-center text-2xl">
                    🏆
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white mb-2">品质保证</h3>
                    <p className="text-gray-300">严格的质量控制，确保每个产品都达到最高标准</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="w-full h-96 bg-gradient-to-br from-purple-800/30 to-cyan-800/30 rounded-2xl backdrop-blur-lg flex items-center justify-center">
                <div className="text-8xl">⌨️</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 页脚 */}
      <footer id="contact" className={`bg-gradient-to-r from-[#1a1b26] to-[#2a1f3e] border-t border-purple-800/40 ${visibleSections.has('contact') ? 'animate-slide-in-up' : 'opacity-0'} transition-all duration-1000`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="px-2 py-1.5 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xs">
                  {getConfigValue('site_logo_text', 'Keycap').charAt(0)}
                </div>
                <div className="flex flex-col">
                  <span className="text-xl font-bold text-white">
                    {getConfigValue('site_name', '灵狐键创')}
                  </span>
                  <span className="text-xs text-gray-400 -mt-1">
                    {getConfigValue('site_name_en', 'Soul Fox Keycap Creation')}
                  </span>
                </div>
              </div>
              <p className="text-gray-400">
                {getConfigValue('footer_description', '专业的键帽设计与销售平台，让每一次敲击都成为艺术表达')}
              </p>
            </div>
            <div>
              <h3 className="font-bold text-white mb-4">🛍️ 商城</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/shop?category=GMK" className="hover:text-cyan-400 transition-colors">GMK 键帽</Link></li>
                <li><Link href="/shop?category=SA" className="hover:text-cyan-400 transition-colors">SA 键帽</Link></li>
                <li><Link href="/shop?category=Artisan" className="hover:text-cyan-400 transition-colors">手工键帽</Link></li>
                <li><Link href="/shop?category=PBT" className="hover:text-cyan-400 transition-colors">PBT 键帽</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold text-white mb-4">🎨 服务</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/designer" className="hover:text-cyan-400 transition-colors">键帽设计器</Link></li>
                <li><Link href="/community" className="hover:text-cyan-400 transition-colors">设计社区</Link></li>
                <li><Link href="/materials" className="hover:text-cyan-400 transition-colors">材质工艺</Link></li>
                <li><Link href="/help" className="hover:text-cyan-400 transition-colors">帮助中心</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold text-white mb-4">📞 联系我们</h3>
              <ul className="space-y-2 text-gray-400">
                <li>邮箱: {getConfigValue('footer_contact_email', '<EMAIL>')}</li>
                <li>微信: LingHuJianChuang</li>
                <li>QQ: 123456789</li>
                <li>工作时间: 9:00-18:00</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-purple-800/40 mt-8 pt-8 text-center text-gray-400">
            <p>{getConfigValue('footer_copyright', '© 2025 灵狐键创 保留所有权利')}</p>
            <div className="mt-2">
              <span className="text-sm">{getConfigValue('footer_about_text', '灵狐键创致力于为键盘爱好者提供最优质的设计工具和社区平台。')}</span>
            </div>
            <div className="mt-2">
              <span className="text-sm">{getConfigValue('footer_social_text', '关注我们的社交媒体')}</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
