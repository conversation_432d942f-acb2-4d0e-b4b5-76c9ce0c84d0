'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTranslations } from '@/contexts/LocaleContext';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// 添加CSS样式支持
const styles = `
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`;

interface Category {
  categoryId: number;
  categoryName: string;
  categoryCode: string;
  icon: string;
  description: string;
}

interface Topic {
  topicId: number;
  topicName: string;
  topicCode: string;
  color: string;
  postCount: number;
  isHot: boolean;
}

interface Post {
  postId: number;
  userId: number;
  username: string;
  userAvatar: string;
  categoryId: number;
  categoryName: string;
  categoryIcon: string;
  topicId: number;
  topicName: string;
  topicColor: string;
  designId: number;
  designName: string;
  designData: string;
  title: string;
  content: string;
  images: string[];
  postType: string;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  isPinned: boolean;
  isHot: boolean;
  isLiked: boolean;
  isFollowed: boolean;
  status: string;
  createTime: string;
  updateTime: string;
}

interface Designer {
  userId: string;
  username: string;
  nickname?: string;
  avatarPath: string;
  designAdvantage: string;
  bio: string;
  postCount: number;
  likeCount: number;
  followersCount: number;
  followingCount: number;
  hasShop: boolean;
  shopName: string;
  isFollowed: boolean;
}

export default function CommunityPage() {
  const t = useTranslations();
  const [activeTab, setActiveTab] = useState('latest');
  const [categories, setCategories] = useState<Category[]>([]);
  const [topics, setTopics] = useState<Topic[]>([]);
  const [posts, setPosts] = useState<Post[]>([]);
  const [designers, setDesigners] = useState<Designer[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreatePost, setShowCreatePost] = useState(false);

  // 新增：发布帖子相关状态
  const [postTitle, setPostTitle] = useState('');
  const [postContent, setPostContent] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [selectedTopicId, setSelectedTopicId] = useState<number | null>(null);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 新增：图片预览相关状态
  const [showImageModal, setShowImageModal] = useState(false);
  const [currentImages, setCurrentImages] = useState<string[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // 编辑帖子相关状态
  const [showEditPost, setShowEditPost] = useState(false);
  const [editingPost, setEditingPost] = useState<Post | null>(null);
  const [editTitle, setEditTitle] = useState('');
  const [editContent, setEditContent] = useState('');
  const [editCategoryId, setEditCategoryId] = useState<number | null>(null);
  const [editTopicId, setEditTopicId] = useState<number | null>(null);
  const [editImages, setEditImages] = useState<string[]>([]);
  const [isUpdating, setIsUpdating] = useState(false);

  // 下拉菜单状态
  const [openDropdown, setOpenDropdown] = useState<number | null>(null);

  // 当前登录用户信息
  const [currentUser, setCurrentUser] = useState<{ userId: string; username: string } | null>(null);

  // 用户信息弹窗状态
  const [showUserModal, setShowUserModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<Designer | null>(null);
  const [userModalLoading, setUserModalLoading] = useState(false);
  const [userShopProducts, setUserShopProducts] = useState<any[]>([]);
  const [shopLoading, setShopLoading] = useState(false);

  // 标签页定义
  const tabs = [
    { id: 'latest', name: t('community.latest'), icon: '🔥' },
    { id: 'hot', name: t('community.hot'), icon: '⭐' },
    { id: 'featured', name: t('community.featured'), icon: '💎' },
    { id: 'following', name: t('community.following'), icon: '❤️' },
    { id: 'designers', name: t('community.designers'), icon: '👨‍🎨' }
  ];

  // 加载数据
  useEffect(() => {
    loadData();
    
    // 添加CSS样式
    const styleId = 'community-post-styles';
    if (!document.getElementById(styleId)) {
      const styleElement = document.createElement('style');
      styleElement.id = styleId;
      styleElement.textContent = styles;
      document.head.appendChild(styleElement);
    }
    
    // 强制修复透明文字问题 - 仅限社区页面
    const fixTransparentText = () => {
      // 只在社区页面执行修复
      if (!window.location.pathname.includes('/community')) {
        return;
      }
      
      console.log('🔧 社区页面专项文字修复...');
      
      // 修复热门话题按钮
      const topicButtons = document.querySelectorAll('.community-dark-card button[style*="backgroundColor: #ffffff"]');
      topicButtons.forEach(btn => {
        if (btn instanceof HTMLElement) {
          btn.style.setProperty('color', '#1f2937', 'important');
          btn.style.setProperty('-webkit-text-fill-color', '#1f2937', 'important');
          btn.style.setProperty('opacity', '1', 'important');
          
          const spans = btn.querySelectorAll('span');
          spans.forEach(span => {
            if (span instanceof HTMLElement) {
              span.style.setProperty('color', '#1f2937', 'important');
              span.style.setProperty('-webkit-text-fill-color', '#1f2937', 'important');
              span.style.setProperty('opacity', '1', 'important');
              span.style.setProperty('font-weight', '800', 'important');
            }
          });
        }
      });
      
      // 修复社区卡片内的文字
      const communityCards = document.querySelectorAll('.community-dark-card');
      communityCards.forEach(card => {
        const grayTexts = card.querySelectorAll('.text-gray-900, .text-gray-800, .text-gray-700, .text-gray-600, .text-gray-500');
        grayTexts.forEach(text => {
          if (text instanceof HTMLElement && !text.classList.contains('bg-clip-text')) {
            text.style.setProperty('color', '#ffffff', 'important');
            text.style.setProperty('-webkit-text-fill-color', '#ffffff', 'important');
            text.style.setProperty('opacity', '1', 'important');
          }
        });
        
        const titles = card.querySelectorAll('h3, h4, .font-bold');
        titles.forEach(title => {
          if (title instanceof HTMLElement && !title.classList.contains('bg-clip-text')) {
            title.style.setProperty('color', '#ffffff', 'important');
            title.style.setProperty('-webkit-text-fill-color', '#ffffff', 'important');
            title.style.setProperty('opacity', '1', 'important');
          }
        });
      });
      
      console.log('✅ 社区页面文字修复完成');
    };
    
    // 立即执行一次
    fixTransparentText();
    
    // 定期执行修复，但频率降低以避免干扰
    const interval = setInterval(fixTransparentText, 2000);
    
    return () => clearInterval(interval);
  }, []);

  // 获取当前用户信息
  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const token = localStorage.getItem('token');
        if (token) {
          const response = await fetch('http://localhost:8080/api/users/profile', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          if (response.ok) {
            const result = await response.json();
            if (result.code === 200) {
              const user = {
                userId: result.data.userId,
                username: result.data.nickname && result.data.nickname.trim() !== '' ? result.data.nickname : result.data.username
              };
              console.log('设置当前用户:', user);
              setCurrentUser(user);
            }
          }
        } else {
          console.log('没有找到token，用户未登录');
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    };
    fetchCurrentUser();
  }, []);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown-container')) {
        setOpenDropdown(null);
      }
    };

    if (openDropdown !== null) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [openDropdown]);

  const loadData = async () => {
    try {
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // 并行加载所有数据
      const [categoriesResponse, topicsResponse, designersResponse] = await Promise.all([
        fetch('http://localhost:8080/api/community/categories', { headers }),
        fetch('http://localhost:8080/api/community/topics', { headers }),
        fetch('http://localhost:8080/api/users/recommended-designers?limit=4', { headers })
      ]);

      // 处理分区数据
      if (categoriesResponse.ok) {
        const categoriesResult = await categoriesResponse.json();
        if (categoriesResult.code === 200) {
          setCategories(categoriesResult.data);
        }
      }

      // 处理话题数据
      if (topicsResponse.ok) {
        const topicsResult = await topicsResponse.json();
        if (topicsResult.code === 200) {
          setTopics(topicsResult.data);
        }
      }

      // 处理推荐设计师数据
      if (designersResponse.ok) {
        const designersResult = await designersResponse.json();
        if (designersResult.code === 200) {
          setDesigners(designersResult.data);
        }
      }

      // 加载帖子
      await loadPosts();

    } catch (error) {
      console.error('❌ 加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadPosts = async () => {
    try {
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      let url = `http://localhost:8080/api/community/posts`;
      
      // 根据不同标签页设置不同的查询参数
      switch (activeTab) {
        case 'latest':
          url += '?type=latest&sort=createTime&order=desc';
          break;
        case 'hot':
          url += '?type=hot&sort=popularity&order=desc';
          break;
        case 'featured':
          url += '?type=featured&sort=featured&order=desc&limit=50';
          break;
        case 'following':
          url += '?type=following';
          break;
        default:
          url += '?type=latest';
      }

      const response = await fetch(url, { headers });
      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          setPosts(result.data.content || []);
        }
      }
    } catch (error) {
      console.error('加载帖子失败:', error);
    }
  };

  // 切换标签页时重新加载帖子
  useEffect(() => {
    if (!loading) {
      loadPosts();
    }
  }, [activeTab, loading]);

  // 处理帖子点赞
  const handleLikePost = async (postId: number, isLiked: boolean, currentLikeCount: number) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert(t('auth.pleaseLogin'));
        return;
      }

      const response = await fetch(`http://localhost:8080/api/community/posts/${postId}/like`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          setPosts(prevPosts => 
            prevPosts.map(post => 
              post.postId === postId 
                ? {
                    ...post,
                    isLiked: result.data,
                    likeCount: result.data ? currentLikeCount + 1 : Math.max(0, currentLikeCount - 1)
                  }
                : post
            )
          );
        } else {
          alert(result.message || '操作失败');
        }
      } else {
        alert('操作失败，请重试');
      }
    } catch (error) {
      console.error('点赞失败:', error);
      alert('网络错误，请重试');
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 30) return `${days}天前`;
    return date.toLocaleDateString();
  };

  // 新增：图片上传处理
  const handleImageUpload = async (files: FileList) => {
    if (files.length === 0) return;
    
    setIsUploading(true);
    const uploadedPaths: string[] = [];
    
    try {
      const token = localStorage.getItem('token');
      
      // 逐个上传文件
      for (let i = 0; i < files.length; i++) {
        const formData = new FormData();
        formData.append('file', files[i]);
        formData.append('uploadType', 'WORK'); // 使用WORK类型用于社区帖子图片
        
        const response = await fetch('http://localhost:8080/api/upload', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          },
          body: formData
        });
        
        if (response.ok) {
          const result = await response.json();
          if (result.code === 200) {
            // 提取文件路径
            let filePath = '';
            if (result.data.filePath) {
              filePath = result.data.filePath;
            } else if (result.data.fileName) {
              filePath = `/uploads/works/${result.data.fileName}`;
            }
            
            if (filePath) {
              uploadedPaths.push(filePath);
            }
          } else {
            alert(`图片 ${files[i].name} 上传失败：${result.message}`);
          }
        } else {
          alert(`图片 ${files[i].name} 上传失败，请重试`);
        }
      }
      
      // 将所有成功上传的图片路径添加到状态中
      if (uploadedPaths.length > 0) {
        setUploadedImages(prev => [...prev, ...uploadedPaths]);
      }
      
    } catch (error) {
      console.error('图片上传失败:', error);
      alert('图片上传失败，请重试');
    } finally {
      setIsUploading(false);
    }
  };

  // 新增：删除已上传图片
  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  // 新增：提交帖子
  const handleSubmitPost = async () => {
    if (!postTitle.trim()) {
      alert('请输入帖子标题');
      return;
    }
    
    if (!postContent.trim()) {
      alert('请输入帖子内容');
      return;
    }
    
    if (!selectedCategoryId) {
      alert('请选择分区');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const token = localStorage.getItem('token');
      const requestData = {
        title: postTitle.trim(),
        content: postContent.trim(),
        categoryId: selectedCategoryId,
        topicId: selectedTopicId,
        images: uploadedImages,
        postType: 'GENERAL'
      };
      
      const response = await fetch('http://localhost:8080/api/community/posts', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          alert('发布成功！');
          // 重置表单
          setPostTitle('');
          setPostContent('');
          setSelectedCategoryId(null);
          setSelectedTopicId(null);
          setUploadedImages([]);
          setShowCreatePost(false);
          // 刷新帖子列表
          loadPosts();
        } else {
          alert('发布失败：' + result.message);
        }
      } else {
        alert('发布失败，请重试');
      }
    } catch (error) {
      console.error('发布帖子失败:', error);
      alert('发布失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 新增：打开图片预览
  const openImageModal = (images: string[], startIndex: number = 0) => {
    setCurrentImages(images);
    setCurrentImageIndex(startIndex);
    setShowImageModal(true);
  };

  // 新增：关闭图片预览
  const closeImageModal = () => {
    setShowImageModal(false);
    setCurrentImages([]);
    setCurrentImageIndex(0);
  };

  // 新增：切换到下一张图片
  const nextImage = () => {
    setCurrentImageIndex((prev) => 
      prev >= currentImages.length - 1 ? 0 : prev + 1
    );
  };

  // 新增：切换到上一张图片
  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev <= 0 ? currentImages.length - 1 : prev - 1
    );
  };

  // 开始编辑帖子
  const handleEditPost = (post: Post) => {
    setEditingPost(post);
    setEditTitle(post.title);
    setEditContent(post.content);
    setEditCategoryId(post.categoryId);
    setEditTopicId(post.topicId);
    setEditImages(post.images || []);
    setShowEditPost(true);
    setOpenDropdown(null);
  };

  // 提交编辑帖子
  const handleUpdatePost = async () => {
    if (!editingPost) return;
    
    if (!editTitle.trim()) {
      alert('请输入帖子标题');
      return;
    }
    
    if (!editContent.trim()) {
      alert('请输入帖子内容');
      return;
    }
    
    if (!editCategoryId) {
      alert('请选择分区');
      return;
    }
    
    setIsUpdating(true);
    
    try {
      const token = localStorage.getItem('token');
      const requestData = {
        title: editTitle.trim(),
        content: editContent.trim(),
        categoryId: editCategoryId,
        topicId: editTopicId,
        images: editImages,
        postType: 'GENERAL'
      };
      
      const response = await fetch(`http://localhost:8080/api/community/posts/${editingPost.postId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          alert('编辑成功！');
          setShowEditPost(false);
          setEditingPost(null);
          // 刷新帖子列表
          loadPosts();
        } else {
          alert('编辑失败：' + result.message);
        }
      } else {
        alert('编辑失败，请重试');
      }
    } catch (error) {
      console.error('编辑帖子失败:', error);
      alert('编辑失败，请重试');
    } finally {
      setIsUpdating(false);
    }
  };

  // 删除帖子
  const handleDeletePost = async (postId: number) => {
    if (!confirm('确定要删除这个帖子吗？删除后无法恢复。')) {
      return;
    }
    
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8080/api/community/posts/${postId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          alert('删除成功！');
          setOpenDropdown(null);
          // 刷新帖子列表
          loadPosts();
        } else {
          alert('删除失败：' + result.message);
        }
      } else {
        alert('删除失败，请重试');
      }
    } catch (error) {
      console.error('删除帖子失败:', error);
      alert('删除失败，请重试');
    }
  };

  // 移除编辑图片
  const removeEditImage = (index: number) => {
    setEditImages(prev => prev.filter((_, i) => i !== index));
  };

  // 编辑模式下的图片上传
  const handleEditImageUpload = async (files: FileList) => {
    if (!files.length) return;
    
    setIsUploading(true);
    const newImages: string[] = [];
    
    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        formData.append('file', file);
        formData.append('uploadType', 'WORK'); // 使用正确的参数名
        
        const response = await fetch('http://localhost:8080/api/upload', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: formData
        });
        
        if (response.ok) {
          const result = await response.json();
          if (result.code === 200) {
            // 使用正确的字段名
            let filePath = '';
            if (result.data.filePath) {
              filePath = result.data.filePath;
            } else if (result.data.fileName) {
              filePath = `/uploads/works/${result.data.fileName}`;
            }
            
            if (filePath) {
              newImages.push(filePath);
            }
          } else {
            alert(`图片上传失败：${result.message}`);
          }
        } else {
          alert('图片上传失败，请重试');
        }
      }
      
      if (newImages.length > 0) {
        setEditImages(prev => [...prev, ...newImages]);
      }
    } catch (error) {
      console.error('上传失败:', error);
      alert('图片上传失败，请重试');
    } finally {
      setIsUploading(false);
    }
  };

  // 关注/取消关注用户
  const handleFollowUser = async (userId: string, isCurrentlyFollowed: boolean) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      console.log('开始关注操作，用户ID:', userId, '当前关注状态:', isCurrentlyFollowed);

      const response = await fetch(`http://localhost:8080/api/users/${userId}/follow`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('关注API响应状态:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('关注API响应数据:', result);
        
        if (result.code === 200) {
          const newFollowStatus = !isCurrentlyFollowed;
          
          // 更新设计师列表中的关注状态
          setDesigners(prev => prev.map(designer => 
            designer.userId === userId 
              ? { 
                  ...designer, 
                  isFollowed: newFollowStatus,
                  followersCount: newFollowStatus ? designer.followersCount + 1 : Math.max(0, designer.followersCount - 1)
                }
              : designer
          ));

          // 如果弹窗打开且是同一用户，也更新弹窗中的状态
          if (selectedUser && selectedUser.userId === userId) {
            console.log('更新弹窗中的用户关注状态:', newFollowStatus);
            setSelectedUser(prev => prev ? {
              ...prev,
              isFollowed: newFollowStatus,
              followersCount: newFollowStatus ? prev.followersCount + 1 : Math.max(0, prev.followersCount - 1)
            } : null);
          }

          const action = newFollowStatus ? '关注成功' : '取消关注成功';
          alert(action);
        } else {
          console.error('关注操作失败:', result.message);
          alert(result.message || '操作失败');
        }
      } else {
        console.error('关注API请求失败:', response.status);
        alert('网络错误，操作失败');
      }
    } catch (error) {
      console.error('关注操作异常:', error);
      alert('网络错误，操作失败');
    }
  };

  // 点击用户头像显示用户信息
  const handleShowUserInfo = async (userId: string) => {
    try {
      setUserModalLoading(true);
      setShowUserModal(true);
      
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // 获取用户详细资料，包含当前用户的关注状态
      const response = await fetch(`http://localhost:8080/api/users/${userId}/profile`, {
        headers
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          // 确保用户信息包含关注状态
          const userInfo = result.data;
          console.log('获取到的用户信息:', userInfo);
          setSelectedUser(userInfo);
          
          // 如果用户有店铺，获取店铺商品
          if (userInfo.hasShop) {
            await fetchUserShopProducts(userId);
          }
        } else {
          alert('获取用户信息失败');
          setShowUserModal(false);
        }
      } else {
        alert('网络错误，获取用户信息失败');
        setShowUserModal(false);
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      alert('网络错误，获取用户信息失败');
      setShowUserModal(false);
    } finally {
      setUserModalLoading(false);
    }
  };

  // 获取用户店铺商品
  const fetchUserShopProducts = async (userId: string) => {
    try {
      setShopLoading(true);
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // 获取用户店铺商品
      const response = await fetch(`http://localhost:8080/api/shop/products/user/${userId}?page=0&size=6`, {
        headers
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          setUserShopProducts(result.data.content || []);
        } else {
          setUserShopProducts([]);
        }
      } else {
        setUserShopProducts([]);
      }
    } catch (error) {
      console.error('获取用户店铺商品失败:', error);
      setUserShopProducts([]);
    } finally {
      setShopLoading(false);
    }
  };

  // 关闭用户信息弹窗
  const closeUserModal = () => {
    setShowUserModal(false);
    setSelectedUser(null);
    setUserShopProducts([]);
  };

  return (
    <div className="community-dark-theme min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 relative overflow-hidden">
      {/* 动态背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-radial from-gray-700/10 via-transparent to-transparent"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-cyan-500/20 rounded-full blur-xl animate-pulse-slow"></div>
        <div className="absolute top-40 right-20 w-48 h-48 bg-gradient-to-r from-indigo-500/20 to-blue-500/20 rounded-full blur-xl animate-float"></div>
        <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-r from-cyan-500/20 to-teal-600/20 rounded-full blur-xl animate-float-reverse"></div>
        <div className="absolute bottom-40 right-10 w-24 h-24 bg-gradient-to-r from-emerald-400/20 to-green-500/20 rounded-full blur-xl animate-pulse-slow"></div>
      </div>
      {/* 欢迎区域 */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600">
        <div className="absolute inset-0 bg-black/10"></div>
        
        {/* 动态流星效果 */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="meteor" style={{animationDelay: '0s', top: '10%', left: '5%'}}></div>
          <div className="meteor" style={{animationDelay: '1s', top: '30%', left: '15%'}}></div>
          <div className="meteor" style={{animationDelay: '2s', top: '50%', left: '25%'}}></div>
          <div className="meteor meteor-reverse" style={{animationDelay: '0.5s', top: '20%', right: '5%'}}></div>
          <div className="meteor meteor-reverse" style={{animationDelay: '1.5s', top: '40%', right: '15%'}}></div>
          <div className="meteor meteor-reverse" style={{animationDelay: '2.5s', top: '60%', right: '25%'}}></div>
        </div>
        
        <div className="relative max-w-[1600px] mx-auto px-6 py-20">
          {/* 语言切换器 */}
          <div className="absolute top-6 right-6">
            <LanguageSwitcher />
          </div>
          
          <div className="text-center">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
              <span className="bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 text-transparent bg-clip-text animate-gradient-text tracking-widest">
                创意社区
              </span>
              <br />
              <span className="bg-gradient-to-r from-pink-500 via-purple-500 to-cyan-400 text-transparent bg-clip-text animate-gradient-text-reverse tracking-widest">
                键帽设计艺术
              </span>
            </h1>
            <p className="text-xl text-white mb-8 max-w-2xl mx-auto" style={{color: '#ffffff !important'}}>
              与全球键帽设计师分享创意，获取灵感，一起打造键帽艺术 ✨
            </p>
            <div className="flex gap-4 justify-center flex-wrap">
              <div
                onClick={() => setShowCreatePost(true)}
                className="share-work-button bg-white flex items-center justify-center gap-2 px-8 py-3 rounded-full font-bold text-base border-2 border-purple-600 cursor-pointer shadow-md hover:shadow-lg hover:bg-gray-50 transition-all duration-200 transform hover:scale-102"
                style={{
                  minWidth: '200px',
                  height: '48px'
                }}
              >
                <span className="share-work-icon" style={{color: '#7c3aed', WebkitTextFillColor: '#7c3aed', opacity: 1}}>✨</span>
                <span className="share-work-text" style={{color: '#7c3aed', WebkitTextFillColor: '#7c3aed', fontWeight: 'bold', opacity: 1}}>分享你的作品</span>
              </div>
              <Link
                href="/dashboard"
                className="bg-white/20 backdrop-blur-sm px-8 py-3 rounded-full font-medium hover:bg-white/30 transition-all duration-200 flex items-center gap-2 border border-white/30 shadow-lg"
                style={{
                  color: '#ffffff !important',
                  textShadow: '0 1px 2px rgba(0,0,0,0.1)'
                }}
              >
                <span style={{color: '#ffffff !important'}}>🏠</span>
                <span style={{color: '#ffffff !important', fontWeight: '600'}}>{t('community.returnHome')}</span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-[1600px] mx-auto">
        {/* 筛选标签 */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex space-x-2 bg-[#2a1f3e]/80 p-1.5 rounded-full backdrop-blur-lg self-start">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`community-tab-button flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 sm:py-2.5 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 text-xs sm:text-sm ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-cyan-400 to-purple-500 text-white shadow-lg ring-2 ring-cyan-400/30'
                    : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 hover:text-white hover:shadow-lg'
                }`}
                style={{
                  boxShadow: activeTab === tab.id 
                    ? '0 10px 15px -3px rgba(34, 211, 238, 0.3), 0 4px 6px -2px rgba(34, 211, 238, 0.1)' 
                    : '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'
                }}
              >
                <span className="text-sm sm:text-base">{tab.icon}</span>
                <span className="text-xs sm:text-sm">{tab.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 主内容区域 - 使用flex布局来处理列 */}
        <div className="lg:flex lg:gap-6 lg:h-[calc(100vh-12rem)]">
          {/* 左侧：热门话题 (固定) */}
          <div className="lg:w-1/5 mb-8 lg:mb-0 lg:overflow-y-auto lg:h-full">
            <div className="community-dark-card bg-gradient-to-br from-[#2a1f3e]/90 to-[#1e1432]/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-purple-500/30 p-6 relative overflow-hidden">
              {/* 背景装饰 */}
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-50"></div>
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-full blur-3xl"></div>
              
              <div className="relative z-10">
                <div className="flex items-center justify-center space-x-3 mb-6">
                  <div className="relative">
                    <div className="w-14 h-14 bg-gradient-to-r from-red-500 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg">
                      <span className="text-2xl">🔥</span>
                </div>
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full animate-pulse"></div>
                  </div>
                  <h3 className="text-xl font-bold bg-gradient-to-r from-red-400 to-orange-400 bg-clip-text text-transparent">
                    热门话题
                </h3>
              </div>
                
                <div className="space-y-3">
                  {['复古风', '赛博朋克风', '现代简约风', '卡通动漫风', '主题创作风'].map((tag, index) => (
                    <div key={index} className="group relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <button className="relative w-full text-left p-4 rounded-xl bg-[#1e1432]/60 hover:bg-[#1e1432]/80 transition-all duration-300 flex items-center group border border-purple-800/30 hover:border-purple-500/50">
                        <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                          <span className="text-white font-bold text-sm">#</span>
              </div>
                        <span className="text-white/90 group-hover:text-white font-medium">{tag}</span>
                        <div className="ml-auto text-xs text-purple-300 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          {Math.floor(Math.random() * 100) + 10}+
            </div>
                      </button>
                    </div>
                  ))}
          </div>

                <div className="mt-6 p-4 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-xl border border-purple-500/20">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg">✨</span>
                    <span className="text-sm font-medium text-purple-300">今日最热</span>
                </div>
                  <p className="text-xs text-gray-300">#赛博朋克风 正在火热讨论中</p>
              </div>
                </div>
              </div>
          </div>

          {/* 中间：帖子流 (可滚动) */}
          <div className="lg:w-3/5 lg:overflow-y-auto lg:h-full lg:pr-2">
              <div className="space-y-4">
              {posts.map(post => (
                <div key={post.postId} className="group relative">
                  {/* 背景光效 */}
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  
                  <div className="relative community-dark-card bg-gradient-to-br from-[#2a1f3e]/90 to-[#1e1432]/90 backdrop-blur-xl rounded-2xl shadow-xl border border-purple-500/30 hover:border-purple-400/50 p-4 transition-all duration-300 hover:shadow-purple-500/10 hover:shadow-xl">
                    {/* 顶部用户信息 */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3 cursor-pointer group/user" onClick={() => handleShowUserInfo(post.userId.toString())}>
                        <div className="relative">
                          <div className="w-10 h-10 rounded-xl overflow-hidden bg-gradient-to-r from-purple-500 to-pink-500 p-0.5">
                            <div className="w-full h-full rounded-xl overflow-hidden bg-gray-800">
                            {post.userAvatar ? (
                                <img src={`http://localhost:8080${post.userAvatar}`} alt="User Avatar" className="w-full h-full object-cover" />
                              ) : (
                                <div className="w-full h-full text-white font-bold text-sm flex items-center justify-center bg-gradient-to-r from-purple-600 to-pink-600">
                                  {(post.username)[0]?.toUpperCase() || 'U'}
                            </div>
                              )}
                            </div>
                            </div>
                          <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 rounded-full border border-[#1e1432] flex items-center justify-center">
                            <div className="w-1 h-1 bg-white rounded-full"></div>
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-0.5">
                            <h4 className="font-bold text-white text-sm group-hover/user:text-purple-300 transition-colors">
                              {post.username}
                            </h4>
                              {currentUser && currentUser.userId === post.userId.toString() && (
                              <span className="px-1.5 py-0.5 bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold text-xs rounded-md">
                                创作者
                              </span>
                            )}
                            </div>
                          <p className="text-xs text-gray-400 flex items-center space-x-1">
                            <span>{formatDistanceToNow(new Date(post.createTime), { addSuffix: true, locale: zhCN })}</span>
                            <span>•</span>
                            <span className="text-purple-300">{post.categoryName || '设计创作'}</span>
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="px-2 py-1 bg-purple-500/20 text-purple-300 text-xs font-medium rounded-lg">
                          热门
                        </div>
                        <button className="text-gray-400 hover:text-white p-1.5 rounded-full hover:bg-white/10 transition-colors">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                          </svg>
                        </button>
                        </div>
                      </div>

                      {/* 帖子内容 */}
                    <div className="mb-4">
                      <h2 className="text-lg font-bold text-white mb-2 leading-snug">{post.title}</h2>
                      <p className="text-gray-300 text-sm leading-relaxed whitespace-pre-wrap">{post.content}</p>
                    </div>

                    {/* 图片网格 */}
                       {post.images && post.images.length > 0 && (
                      <div className="mb-4">
                        <div className={`grid gap-1 max-w-md ${post.images.length === 1 ? 'grid-cols-1 max-w-xs' : post.images.length === 2 ? 'grid-cols-2 max-w-sm' : 'grid-cols-3 max-w-md'}`}>
                          {post.images.map((image, index) => (
                            <div key={index} className="relative group/image">
                              <div className="aspect-square bg-gray-800 rounded-xl overflow-hidden border border-purple-500/20 hover:border-purple-400/40 transition-colors">
                                <img src={`http://localhost:8080${image}`} alt={`Post image ${index + 1}`} className="w-full h-full object-cover group-hover/image:scale-105 transition-transform duration-300" />
                                   </div>
                              <div className="absolute inset-0 bg-black/40 opacity-0 group-hover/image:opacity-100 transition-opacity duration-300 rounded-xl flex items-center justify-center">
                                <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                  </svg>
                                </div>
                              </div>
                               </div>
                             ))}
                           </div>
                         </div>
                       )}

                      {/* 话题标签 */}
                    <div className="flex flex-wrap gap-1.5 mb-4">
                      {post.topicName && (
                        <span className="px-3 py-1 bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-300 text-xs font-medium rounded-full border border-purple-500/30 hover:border-purple-400/50 transition-colors cursor-pointer">
                            #{post.topicName}
                          </span>
                      )}
                      <span className="px-3 py-1 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 text-blue-300 text-xs font-medium rounded-full border border-blue-500/30 hover:border-blue-400/50 transition-colors cursor-pointer">
                            #键帽设计
                          </span>
                        </div>

                      {/* 底部操作栏 */}
                    <div className="flex items-center justify-between pt-3 border-t border-purple-500/20">
                        <div className="flex items-center space-x-4">
                          <button 
                            onClick={() => handleLikePost(post.postId, post.isLiked, post.likeCount)}
                          className={`flex items-center space-x-1.5 transition-colors group/like ${
                            post.isLiked 
                              ? 'text-pink-400 hover:text-pink-500' 
                              : 'text-gray-400 hover:text-pink-400'
                          }`}
                        >
                          <div className={`w-7 h-7 rounded-full flex items-center justify-center transition-colors ${
                            post.isLiked 
                              ? 'bg-pink-500/20 group-hover/like:bg-pink-500/30' 
                              : 'bg-pink-500/10 group-hover/like:bg-pink-500/20'
                          }`}>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <span className="text-sm font-medium">{post.likeCount}</span>
                          </button>
                          <Link 
                          href={`/community/post/${post.postId}`}
                          className="flex items-center space-x-1.5 text-gray-400 hover:text-cyan-400 transition-colors group/comment"
                        >
                          <div className="w-7 h-7 rounded-full bg-cyan-500/10 flex items-center justify-center group-hover/comment:bg-cyan-500/20 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                              <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h7a2 2 0 002-2V9a2 2 0 00-2-2h-1z" />
                            </svg>
                          </div>
                          <span className="text-sm font-medium">{post.commentCount}</span>
                          </Link>
                        <button className="flex items-center space-x-1.5 text-gray-400 hover:text-green-400 transition-colors group/view">
                          <div className="w-7 h-7 rounded-full bg-green-500/10 flex items-center justify-center group-hover/view:bg-green-500/20 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <span className="text-sm font-medium">{post.viewCount}</span>
                        </button>
                        </div>
                      <Link href={`/community/post/${post.postId}`} className="px-4 py-1.5 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium text-sm rounded-full hover:from-purple-600 hover:to-pink-600 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25 transform hover:scale-105">
                          查看详情
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
          </div>

          {/* 右侧：推荐设计师 (固定) */}
          <div className="lg:w-1/5 lg:overflow-y-auto lg:h-full">
            <div className="community-dark-card bg-gradient-to-br from-[#2a1f3e]/90 to-[#1e1432]/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-purple-500/30 p-6 relative overflow-hidden">
              {/* 背景装饰 */}
              <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 opacity-50"></div>
              <div className="absolute top-0 left-0 w-24 h-24 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-2xl"></div>
              
              <div className="relative z-10">
                <div className="text-center mb-6">
                  <div className="relative inline-block">
                    <div className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg mb-3">
                  <span className="text-2xl">⭐</span>
                </div>
                    <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">3</span>
                    </div>
                  </div>
                  <h3 className="text-lg font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                    推荐设计师
                </h3>
              </div>
                
                <div className="space-y-4">
                  {designers.slice(0, 5).map((designer, index) => (
                    <div key={designer.userId} className="flex items-center justify-between p-3 rounded-xl bg-[#1e1432]/40 hover:bg-[#1e1432]/60 transition-all duration-300 border border-purple-800/20 hover:border-purple-500/30">
                      <div className="flex items-center space-x-3 flex-1">
                      <div className="relative">
                          <div
                          onClick={() => handleShowUserInfo(designer.userId)}
                            className="w-10 h-10 rounded-xl p-0.5 bg-gradient-to-tr from-purple-500 to-cyan-400 cursor-pointer hover:scale-110 transition-transform duration-300"
                        >
                            <div className="w-full h-full bg-[#1e1432] rounded-xl overflow-hidden">
                          {designer.avatarPath ? (
                            <img 
                              src={`http://localhost:8080${designer.avatarPath}`} 
                              alt="设计师头像" 
                              className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full text-white font-bold text-sm flex items-center justify-center bg-gradient-to-r from-purple-600 to-pink-600">
                            {(designer.nickname || designer.username)[0]?.toUpperCase() || 'U'}
                          </div>
                              )}
                          </div>
                        </div>
                          <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border border-[#1e1432]"></div>
                      </div>
                        
                        <div className="flex-1 min-w-0">
                          <h4 className="font-bold text-white text-sm truncate hover:text-purple-300 transition-colors cursor-pointer" onClick={() => handleShowUserInfo(designer.userId)}>
                          {designer.nickname || designer.username}
                          </h4>
                          <div className="flex items-center space-x-2 text-xs text-gray-400">
                            <span className="flex items-center space-x-1">
                          <span>👥</span>
                              <span>{designer.followersCount}</span>
                            </span>
                            <span>•</span>
                            <span>{designer.postCount}帖子</span>
                        </div>
                        </div>
                      </div>
                      
                      <button 
                        onClick={() => handleFollowUser(designer.userId, designer.isFollowed)}
                        className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-300 hover:scale-105 ${
                          designer.isFollowed
                            ? 'bg-transparent border border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white'
                            : 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600'
                        }`}
                      >
                        {designer.isFollowed ? '已关注' : '关注'}
                      </button>
                  </div>
                ))}
              </div>
              
                <div className="mt-6 text-center">
                  <button className="w-full py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 font-medium text-sm shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center justify-center space-x-2">
                    <span>查看更多设计师</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 新增：图片预览模态框 */}
      {showImageModal && (
        <div className="fixed inset-0 bg-black/90 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full w-full h-full flex items-center justify-center">
            {/* 关闭按钮 */}
            <button
              onClick={closeImageModal}
              className="absolute top-4 right-4 w-12 h-12 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center transition-colors z-10"
            >
              <span className="text-white text-2xl">×</span>
            </button>

            {/* 图片计数 */}
            {currentImages.length > 1 && (
              <div className="absolute top-4 left-4 bg-black/50 text-white px-4 py-2 rounded-full text-sm z-10">
                {currentImageIndex + 1} / {currentImages.length}
              </div>
            )}

            {/* 主图片 */}
            <div className="relative w-full h-full flex items-center justify-center">
              <img
                src={`http://localhost:8080${currentImages[currentImageIndex]}`}
                alt={`预览图片 ${currentImageIndex + 1}`}
                className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/images/placeholders/image-placeholder.png';
                }}
              />
            </div>

            {/* 左右切换按钮 */}
            {currentImages.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 w-16 h-16 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center transition-all"
                >
                  <span className="text-white text-3xl">‹</span>
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 w-16 h-16 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center transition-all"
                >
                  <span className="text-white text-3xl">›</span>
                </button>
              </>
            )}

            {/* 缩略图导航（如果有多张图片） */}
            {currentImages.length > 1 && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 bg-black/50 p-3 rounded-2xl max-w-full overflow-x-auto">
                {currentImages.map((imageUrl, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                      index === currentImageIndex 
                        ? 'border-white shadow-lg' 
                        : 'border-transparent opacity-60 hover:opacity-80'
                    }`}
                  >
                    <img
                      src={`http://localhost:8080${imageUrl}`}
                      alt={`缩略图 ${index + 1}`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/images/placeholders/image-placeholder.png';
                      }}
                    />
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 用户信息弹窗 */}
      {showUserModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-3xl shadow-2xl max-w-md w-full max-h-[80vh] overflow-y-auto">
            {/* 弹窗头部 */}
            <div className="relative p-6 border-b border-gray-200">
              <button
                onClick={closeUserModal}
                className="absolute top-4 right-4 w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
              >
                <span className="text-lg">×</span>
              </button>
              <div className="text-center">
                <h2 className="text-xl font-bold text-gray-900">用户信息</h2>
              </div>
            </div>

            {/* 弹窗内容 */}
            <div className="p-6">
              {userModalLoading ? (
                <div className="text-center py-8">
                  <div className="text-2xl mb-2">⏳</div>
                  <p className="text-gray-500">加载中...</p>
                </div>
              ) : selectedUser ? (
                <div className="space-y-6">
                  {/* 用户头像和基本信息 */}
                  <div className="text-center">
                    <div className="w-20 h-20 rounded-full overflow-hidden bg-gradient-to-r from-purple-400 to-pink-500 flex items-center justify-center mx-auto mb-4 shadow-lg">
                      {selectedUser.avatarPath ? (
                        <img 
                          src={`http://localhost:8080${selectedUser.avatarPath}`} 
                          alt={`${selectedUser.username}的头像`} 
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const fallback = target.parentElement?.querySelector('.fallback-avatar') as HTMLElement;
                            if (fallback) fallback.style.display = 'flex';
                          }}
                        />
                      ) : null}
                      <div className={`w-full h-full text-white font-bold text-2xl flex items-center justify-center fallback-avatar ${selectedUser.avatarPath ? 'hidden' : ''}`}>
                        {(selectedUser.nickname || selectedUser.username)[0]?.toUpperCase() || 'U'}
                      </div>
                    </div>
                    
                    <h3 className="font-bold text-gray-900 text-xl mb-2">
                      {selectedUser.nickname || selectedUser.username}
                    </h3>
                    
                    {selectedUser.bio && (
                      <p className="text-gray-600 text-sm mb-4">
                        {selectedUser.bio}
                      </p>
                    )}
                  </div>

                  {/* 统计数据 */}
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="bg-gray-50 rounded-xl p-4">
                      <div className="text-xl font-bold text-gray-900">{selectedUser.postCount}</div>
                      <div className="text-sm text-gray-500">发帖数</div>
                    </div>
                    <div className="bg-gray-50 rounded-xl p-4">
                      <div className="text-xl font-bold text-gray-900">{selectedUser.likeCount}</div>
                      <div className="text-sm text-gray-500">获赞数</div>
                    </div>
                    <div className="bg-gray-50 rounded-xl p-4">
                      <div className="text-xl font-bold text-gray-900">{selectedUser.followersCount}</div>
                      <div className="text-sm text-gray-500">粉丝</div>
                    </div>
                  </div>

                  {/* 设计优势 */}
                  {selectedUser.designAdvantage && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">设计优势</h4>
                      <p className="text-sm text-gray-600 bg-gray-50 rounded-xl p-3">
                        {selectedUser.designAdvantage}
                      </p>
                    </div>
                  )}

                  {/* 店铺信息 */}
                  {(() => {
                    const isMyProfile = currentUser && currentUser.userId === selectedUser.userId;
                    
                    if (selectedUser.hasShop) {
                      return (
                        <div className="bg-orange-50 rounded-xl p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-2">
                              <span className="text-lg">🏪</span>
                              <h4 className="font-medium text-gray-900">
                                {isMyProfile ? '我的店铺' : 'TA的店铺'}
                              </h4>
                            </div>
                            <button
                              onClick={() => {
                                const shopUrl = isMyProfile ? '/shop/my-store' : `/shop/store/${selectedUser.userId}?from=community`;
                                window.open(shopUrl, '_blank');
                              }}
                              className="text-sm text-orange-600 hover:text-orange-700 font-medium"
                            >
                              进入店铺 →
                            </button>
                          </div>
                          
                          {selectedUser.shopName && (
                            <p className="text-sm text-gray-600 mb-3">{selectedUser.shopName}</p>
                          )}
                          
                          {/* 店铺商品展示 */}
                          {shopLoading ? (
                            <div className="text-center py-4">
                              <div className="text-sm text-gray-500">加载商品中...</div>
                            </div>
                          ) : userShopProducts.length > 0 ? (
                            <div>
                              <div className="text-xs text-gray-500 mb-2">店铺商品 ({userShopProducts.length})</div>
                              <div className="grid grid-cols-3 gap-2">
                                {userShopProducts.slice(0, 6).map((product, index) => (
                                  <div 
                                    key={product.productId || index}
                                    className="bg-white rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer"
                                                                      onClick={() => {
                                    window.open(`/shop/product/${product.productId || product.id}`, '_blank');
                                  }}
                                  >
                                    <div className="aspect-square bg-gray-100 rounded-md mb-1 overflow-hidden">
                                      {product.images && product.images.length > 0 ? (
                                        <img 
                                          src={product.images[0].startsWith('http') ? product.images[0] : `http://localhost:8080${product.images[0]}`} 
                                          alt={product.name || product.productName}
                                          className="w-full h-full object-cover"
                                          onError={(e) => {
                                            const target = e.target as HTMLImageElement;
                                            target.src = '/images/placeholders/image-placeholder.png';
                                          }}
                                        />
                                      ) : (
                                        <div className="w-full h-full flex items-center justify-center text-gray-400">
                                          <span className="text-xs">📦</span>
                                        </div>
                                      )}
                                    </div>
                                    <div className="text-xs text-gray-900 truncate" title={product.name || product.productName}>
                                      {product.name || product.productName}
                                    </div>
                                    <div className="text-xs text-orange-600 font-medium">
                                      ¥{product.price}
                                    </div>
                                  </div>
                                ))}
                              </div>
                              {userShopProducts.length > 6 && (
                                <div className="text-center mt-2">
                                  <button
                                    onClick={() => {
                                      const shopUrl = isMyProfile ? '/shop/my-store' : `/shop/store/${selectedUser.userId}?from=community`;
                                      window.open(shopUrl, '_blank');
                                    }}
                                    className="text-xs text-orange-600 hover:text-orange-700"
                                  >
                                    查看更多商品 →
                                  </button>
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="text-center py-3">
                              <div className="text-sm text-gray-500">
                                {isMyProfile ? '还没有上架商品' : 'TA还没有上架商品'}
                              </div>
                              {isMyProfile && (
                                <button
                                  onClick={() => {
                                    window.open('/shop/products', '_blank');
                                  }}
                                  className="text-sm text-orange-600 hover:text-orange-700 mt-1"
                                >
                                  去上架商品 →
                                </button>
                              )}
                            </div>
                          )}
                        </div>
                      );
                    } else {
                      return (
                        <div className="bg-gray-50 rounded-xl p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="text-lg">🏪</span>
                            <h4 className="font-medium text-gray-900">
                              {isMyProfile ? '我的店铺' : 'TA的店铺'}
                            </h4>
                          </div>
                          <div className="text-center py-3">
                            <div className="text-sm text-gray-500 mb-2">
                              {isMyProfile ? '您还没有开通店铺' : 'TA还没有开通店铺'}
                            </div>
                            {isMyProfile && (
                              <button
                                onClick={() => {
                                  window.open('/shop/my-store', '_blank');
                                }}
                                className="text-sm bg-orange-500 text-white px-3 py-1 rounded-md hover:bg-orange-600 transition-colors"
                              >
                                开通店铺
                              </button>
                            )}
                          </div>
                        </div>
                      );
                    }
                  })()}

                  {/* 操作按钮 */}
                  <div className="space-y-3">
                    {/* 关注按钮 - 始终显示，根据状态显示不同内容 */}
                    {(() => {
                      console.log('当前用户:', currentUser);
                      console.log('选中用户:', selectedUser);
                      
                      if (!currentUser) {
                        return (
                          <div className="w-full py-3 rounded-xl bg-gray-100 text-gray-500 text-center font-medium">
                            请先登录
                          </div>
                        );
                      }
                      
                      if (currentUser.userId === selectedUser.userId) {
                        return (
                          <div className="w-full py-3 rounded-xl bg-gray-100 text-gray-500 text-center font-medium">
                            这是您自己
                          </div>
                        );
                      }
                      
                      // 显示关注按钮
                      return (
                        <button
                          onClick={() => {
                            console.log('点击关注按钮, 用户ID:', selectedUser.userId, '当前关注状态:', selectedUser.isFollowed);
                            handleFollowUser(selectedUser.userId, selectedUser.isFollowed || false);
                          }}
                          className={`w-full py-3 rounded-xl font-medium transition-all duration-200 ${
                            selectedUser.isFollowed
                              ? 'bg-gray-500 text-white hover:bg-gray-600'
                              : 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600'
                          }`}
                        >
                          {selectedUser.isFollowed ? '已关注' : '关注'}
                        </button>
                      );
                    })()}

                    {/* 其他操作按钮 */}
                    <div className="flex gap-3">
                      <button
                        onClick={() => {
                          // 查看用户作品
                          window.open(`/community?userId=${selectedUser.userId}`, '_blank');
                        }}
                        className="flex-1 bg-gray-100 text-gray-700 py-2 rounded-xl hover:bg-gray-200 transition-colors font-medium"
                      >
                        查看作品
                      </button>
                      
                      <button
                        onClick={() => {
                          // 私信功能（将来可以实现）
                          alert('私信功能即将开放');
                        }}
                        className="flex-1 bg-blue-600 text-white py-2 rounded-xl hover:bg-blue-700 transition-colors font-medium"
                      >
                        发私信
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-2xl mb-2">😕</div>
                  <p className="text-gray-500">获取用户信息失败</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 新增：发布帖子模态框 */}
      {showCreatePost && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            {/* 模态框头部 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900">发布新帖子</h2>
              <button
                onClick={() => setShowCreatePost(false)}
                className="w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
              >
                <span className="text-xl">×</span>
              </button>
            </div>

            {/* 模态框内容 */}
            <div className="p-6 space-y-6">
              {/* 标题输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  帖子标题 *
                </label>
                <input
                  type="text"
                  value={postTitle}
                  onChange={(e) => setPostTitle(e.target.value)}
                  placeholder="请输入帖子标题..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                  maxLength={200}
                />
                <div className="text-xs text-gray-500 mt-1">
                  {postTitle.length}/200
                </div>
              </div>

              {/* 分区选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择分区 *
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {categories.map((category) => (
                    <button
                      key={category.categoryId}
                      onClick={() => setSelectedCategoryId(category.categoryId)}
                      className={`p-3 rounded-xl border-2 transition-all ${
                        selectedCategoryId === category.categoryId
                          ? 'border-purple-500 bg-purple-50 text-purple-700'
                          : 'border-gray-200 hover:border-gray-300 text-gray-800'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{category.icon}</span>
                        <span className="font-medium">{category.categoryName}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* 话题选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择话题（可选）
                </label>
                <div className="flex flex-wrap gap-2 max-h-40 overflow-y-auto">
                  {topics.map((topic) => (
                    <button
                      key={topic.topicId}
                      onClick={() => setSelectedTopicId(
                        selectedTopicId === topic.topicId ? null : topic.topicId
                      )}
                      className={`px-3 py-2 rounded-lg border-2 transition-all text-sm font-medium ${
                        selectedTopicId === topic.topicId
                          ? 'text-white'
                          : 'text-gray-700 border-gray-200 hover:border-gray-300'
                      }`}
                      style={{
                        backgroundColor: selectedTopicId === topic.topicId 
                          ? topic.color || '#6366f1' 
                          : 'transparent',
                        borderColor: topic.color || '#6366f1'
                      }}
                    >
                      #{topic.topicName}
                    </button>
                  ))}
                </div>
              </div>

              {/* 内容输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  帖子内容 *
                </label>
                <textarea
                  value={postContent}
                  onChange={(e) => setPostContent(e.target.value)}
                  placeholder="分享你的想法、设计心得、或其他有趣的内容..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent h-32 resize-none text-gray-800"
                  maxLength={2000}
                />
                <div className="text-xs text-gray-500 mt-1">
                  {postContent.length}/2000
                </div>
              </div>

              {/* 图片上传 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  上传图片（可选）
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => e.target.files && handleImageUpload(e.target.files)}
                    className="hidden"
                    id="image-upload"
                  />
                  <label
                    htmlFor="image-upload"
                    className="cursor-pointer flex flex-col items-center gap-2"
                  >
                    <span className="text-4xl">📷</span>
                    <span className="text-gray-600">
                      {isUploading ? '上传中...' : '点击上传图片'}
                    </span>
                    <span className="text-xs text-gray-500">支持多张图片上传</span>
                  </label>
                </div>

                {/* 已上传图片预览 */}
                {uploadedImages.length > 0 && (
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 mt-4">
                    {uploadedImages.map((imageUrl, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={`http://localhost:8080${imageUrl}`}
                          alt={`上传图片 ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg"
                        />
                        <button
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* 模态框底部 */}
            <div className="flex items-center justify-end gap-4 p-6 border-t border-gray-200">
              <button
                onClick={() => setShowCreatePost(false)}
                className="px-6 py-3 text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-xl transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleSubmitPost}
                disabled={isSubmitting || !postTitle || !postContent || !selectedCategoryId}
                className="px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium"
              >
                {isSubmitting ? '发布中...' : '发布帖子'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 新增：编辑帖子模态框 */}
      {showEditPost && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            {/* 模态框头部 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900">编辑帖子</h2>
              <button
                onClick={() => setShowEditPost(false)}
                className="w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
              >
                <span className="text-xl">×</span>
              </button>
            </div>

            {/* 模态框内容 */}
            <div className="p-6 space-y-6">
              {/* 标题输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  帖子标题 *
                </label>
                <input
                  type="text"
                  value={editTitle}
                  onChange={(e) => setEditTitle(e.target.value)}
                  placeholder="请输入帖子标题..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                  maxLength={200}
                />
                <div className="text-xs text-gray-500 mt-1">
                  {editTitle.length}/200
                </div>
              </div>

              {/* 分区选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择分区 *
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {categories.map((category) => (
                    <button
                      key={category.categoryId}
                      onClick={() => setEditCategoryId(category.categoryId)}
                      className={`p-3 rounded-xl border-2 transition-all ${
                        editCategoryId === category.categoryId
                          ? 'border-purple-500 bg-purple-50 text-purple-700'
                          : 'border-gray-200 hover:border-gray-300 text-gray-800'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{category.icon}</span>
                        <span className="font-medium">{category.categoryName}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* 话题选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择话题（可选）
                </label>
                <div className="flex flex-wrap gap-2 max-h-40 overflow-y-auto">
                  {topics.map((topic) => (
                    <button
                      key={topic.topicId}
                      onClick={() => setEditTopicId(
                        editTopicId === topic.topicId ? null : topic.topicId
                      )}
                      className={`px-3 py-2 rounded-lg border-2 transition-all text-sm font-medium ${
                        editTopicId === topic.topicId
                          ? 'text-white'
                          : 'text-gray-700 border-gray-200 hover:border-gray-300'
                      }`}
                      style={{
                        backgroundColor: editTopicId === topic.topicId 
                          ? topic.color || '#6366f1' 
                          : 'transparent',
                        borderColor: topic.color || '#6366f1'
                      }}
                    >
                      #{topic.topicName}
                    </button>
                  ))}
                </div>
              </div>

              {/* 内容输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  帖子内容 *
                </label>
                <textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  placeholder="分享你的想法、设计心得、或其他有趣的内容..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent h-32 resize-none text-gray-800"
                  maxLength={2000}
                />
                <div className="text-xs text-gray-500 mt-1">
                  {editContent.length}/2000
                </div>
              </div>

              {/* 图片上传 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  上传图片（可选）
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => e.target.files && handleEditImageUpload(e.target.files)}
                    className="hidden"
                    id="edit-image-upload"
                  />
                  <label
                    htmlFor="edit-image-upload"
                    className="cursor-pointer flex flex-col items-center gap-2"
                  >
                    <span className="text-4xl">📷</span>
                    <span className="text-gray-600">
                      {isUploading ? '上传中...' : '点击上传图片'}
                    </span>
                    <span className="text-xs text-gray-500">支持多张图片上传</span>
                  </label>
                </div>

                {/* 已上传图片预览 */}
                {editImages.length > 0 && (
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 mt-4">
                    {editImages.map((imageUrl, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={`http://localhost:8080${imageUrl}`}
                          alt={`上传图片 ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg"
                        />
                        <button
                          onClick={() => removeEditImage(index)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* 模态框底部 */}
            <div className="flex items-center justify-end gap-4 p-6 border-t border-gray-200">
              <button
                onClick={() => setShowEditPost(false)}
                className="px-6 py-3 text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-xl transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleUpdatePost}
                disabled={isUpdating || !editTitle || !editContent || !editCategoryId}
                className="px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium"
              >
                {isUpdating ? '保存中...' : '保存帖子'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 