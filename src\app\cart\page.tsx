'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

// API基础URL
const API_BASE_URL = 'http://localhost:8080/api';

// 购物车商品接口（匹配后端API返回格式）
interface CartItem {
  id: number;
  productId: string;
  quantity: number;
  selected: boolean;
  createTime: string;
  updateTime: string;
  // 商品详细信息
  productName: string;
  description: string;
  price: number;
  originalPrice: number;
  category: string;
  images: string[];
  status: string;
  stock: number;
  // 店铺信息
  shopName: string;
  shopLogo?: string;
  // 计算字段
  totalPrice: number;
  discount: number;
}

export default function CartPage() {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedItems, setSelectedItems] = useState<Set<number>>(new Set());
  const router = useRouter();

  // 获取token
  const getToken = () => {
    return localStorage.getItem('token');
  };

  // 从API获取购物车数据
  const fetchCartItems = async () => {
    try {
      setLoading(true);
      const token = getToken();
      
      if (!token) {
        console.log('未找到登录token，显示空购物车');
        setCartItems([]);
        setLoading(false);
        return;
      }

      const response = await fetch(`${API_BASE_URL}/cart`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log('购物车API响应:', result);
        
        // 后端ApiResponse格式：{ code: 200, message: "操作成功", data: [...], timestamp: ... }
        if (result.code === 200 && result.data && Array.isArray(result.data)) {
          setCartItems(result.data);
          // 设置选中状态
          const selectedIds = new Set<number>(result.data.filter((item: CartItem) => item.selected).map((item: CartItem) => item.id));
          setSelectedItems(selectedIds);
        } else {
          console.warn('购物车API返回格式异常:', result);
          setCartItems([]);
        }
      } else if (response.status === 401) {
        console.log('token已过期，清空购物车显示');
        localStorage.removeItem('token');
        setCartItems([]);
      } else {
        console.error('获取购物车失败，状态码:', response.status);
        setCartItems([]);
      }
    } catch (error) {
      console.error('获取购物车数据失败:', error);
      setCartItems([]);
    } finally {
      setLoading(false);
    }
  };

  // 更新购物车项数量
  const updateQuantity = async (cartId: number, newQuantity: number) => {
    if (newQuantity < 1) return;
    
    try {
      const token = getToken();
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/cart/${cartId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          quantity: newQuantity,
          productId: cartItems.find(item => item.id === cartId)?.productId || ''
        }),
      });

      if (response.ok) {
        // 重新获取购物车数据
        fetchCartItems();
      } else {
        console.error('更新数量失败');
        alert('更新数量失败，请重试');
      }
    } catch (error) {
      console.error('更新数量失败:', error);
      alert('更新数量失败，请重试');
    }
  };

  // 删除购物车项
  const removeItem = async (cartId: number) => {
    try {
      const token = getToken();
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/cart/${cartId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        // 重新获取购物车数据
        fetchCartItems();
      } else {
        console.error('删除商品失败');
        alert('删除商品失败，请重试');
      }
    } catch (error) {
      console.error('删除商品失败:', error);
      alert('删除商品失败，请重试');
    }
  };

  // 切换商品选择状态
  const toggleItemSelection = async (cartId: number) => {
    try {
      const token = getToken();
      if (!token) return;

      const currentItem = cartItems.find(item => item.id === cartId);
      if (!currentItem) return;

      const newSelected = !currentItem.selected;

      const response = await fetch(`${API_BASE_URL}/cart/${cartId}/selection`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selected: newSelected
        }),
      });

      if (response.ok) {
        // 本地更新状态
        setCartItems(prevItems =>
          prevItems.map(item =>
            item.id === cartId ? { ...item, selected: newSelected } : item
          )
        );
        
        // 更新选中集合
        setSelectedItems(prev => {
          const newSet = new Set(prev);
          if (newSelected) {
            newSet.add(cartId);
          } else {
            newSet.delete(cartId);
          }
          return newSet;
        });
      } else {
        console.error('更新选择状态失败');
      }
    } catch (error) {
      console.error('更新选择状态失败:', error);
    }
  };

  // 全选/取消全选
  const toggleSelectAll = async () => {
    try {
      const token = getToken();
      if (!token) return;

      const shouldSelectAll = selectedItems.size !== cartItems.length;

      const response = await fetch(`${API_BASE_URL}/cart/selection/all`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selected: shouldSelectAll
        }),
      });

      if (response.ok) {
        // 重新获取购物车数据
        fetchCartItems();
      } else {
        console.error('全选操作失败');
      }
    } catch (error) {
      console.error('全选操作失败:', error);
    }
  };

  // 删除选中商品
  const removeSelectedItems = async () => {
    try {
      const token = getToken();
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/cart/selected`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        // 重新获取购物车数据
        fetchCartItems();
      } else {
        console.error('删除选中商品失败');
        alert('删除选中商品失败，请重试');
      }
    } catch (error) {
      console.error('删除选中商品失败:', error);
      alert('删除选中商品失败，请重试');
    }
  };

  // 计算总价
  const calculateTotal = () => {
    return cartItems
      .filter(item => item.selected)
      .reduce((total, item) => total + item.price * item.quantity, 0);
  };

  // 计算原价总价
  const calculateOriginalTotal = () => {
    return cartItems
      .filter(item => item.selected)
      .reduce((total, item) => total + (item.originalPrice || item.price) * item.quantity, 0);
  };

  // 计算节省金额
  const calculateSavings = () => {
    return calculateOriginalTotal() - calculateTotal();
  };

  // 获取选中商品数量
  const getSelectedCount = () => {
    return cartItems
      .filter(item => item.selected)
      .reduce((count, item) => count + item.quantity, 0);
  };

  // 结算购物车
  const handleCheckout = () => {
    const token = getToken();
    if (!token) {
      alert('请先登录！');
      return;
    }
    
    if (selectedItems.size === 0) {
      alert('请至少选择一件商品');
      return;
    }
    
    // 从购物车中获取选中的商品信息
    const selectedProductIds = cartItems
      .filter(item => item.selected)
      .map(item => ({
        productId: item.productId,
        quantity: item.quantity
      }));
    
    if (selectedProductIds.length === 0) {
      alert('请至少选择一件商品');
      return;
    }
    
    // 将选中的商品信息传递给确认订单页面
    const firstProduct = selectedProductIds[0];
    // 跳转到确认订单页面，传递第一个商品的ID和数量
    router.push(`/buy-now?productId=${firstProduct.productId}&quantity=${firstProduct.quantity}`);
  };

  // 组件加载时获取购物车数据
  useEffect(() => {
    fetchCartItems();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-8">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                  L
                </div>
                <span className="text-xl font-bold text-gray-900">灵狐键创</span>
              </Link>
              <div className="hidden md:flex items-center space-x-6">
                <Link href="/shop" className="text-gray-600 hover:text-gray-900 transition-colors">
                  商城
                </Link>
                <span className="text-purple-600 font-medium">购物车</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="bg-gradient-to-r from-purple-600 to-cyan-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">购物车</h1>
          <Link href="/shop" className="text-purple-600 hover:text-purple-700 transition-colors">
            继续购物 →
          </Link>
        </div>

        {cartItems.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">🛒</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">购物车是空的</h3>
            <p className="text-gray-500 mb-6">快去挑选你喜欢的键帽吧！</p>
            <Link
              href="/shop"
              className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors"
            >
              去购物
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 商品列表 */}
            <div className="lg:col-span-2 space-y-4">
              {/* 全选和操作栏 */}
              <div className="bg-white rounded-lg p-4 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectedItems.size === cartItems.length && cartItems.length > 0}
                      onChange={toggleSelectAll}
                      className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"
                    />
                    <span className="text-sm font-medium text-gray-900">全选</span>
                  </label>
                  <span className="text-sm text-gray-500">共 {cartItems.length} 件商品</span>
                </div>
                <button 
                  onClick={removeSelectedItems}
                  disabled={selectedItems.size === 0}
                  className="text-red-600 hover:text-red-700 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  删除选中
                </button>
              </div>

              {/* 商品卡片 */}
              {cartItems.map((item) => (
                <div key={item.id} className="bg-white rounded-lg p-6 shadow-sm">
                  <div className="flex items-start space-x-4">
                    {/* 选择框 */}
                    <label className="flex items-center mt-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={item.selected}
                        onChange={() => toggleItemSelection(item.id)}
                        className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"
                      />
                    </label>

                    {/* 商品图片 */}
                    <div className="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center overflow-hidden flex-shrink-0">
                      {item.images && item.images.length > 0 ? (
                        <img 
                          src={item.images[0]} 
                          alt={item.productName}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="text-2xl">🎯</div>
                      )}
                    </div>

                    {/* 商品信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-gray-900 mb-1">{item.productName}</h3>
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                              {item.category}
                            </span>
                            {item.discount > 0 && (
                              <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded">
                                -{item.discount}%
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-500">店铺：{item.shopName}</p>
                          <p className="text-xs text-gray-400">库存：{item.stock}件</p>
                        </div>

                        {/* 删除按钮 */}
                        <button
                          onClick={() => removeItem(item.id)}
                          className="text-gray-400 hover:text-red-500 transition-colors ml-4"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>

                      {/* 价格和数量 */}
                      <div className="flex items-center justify-between mt-4">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg font-bold text-red-600">¥{item.price}</span>
                          {item.originalPrice && item.originalPrice > item.price && (
                            <span className="text-sm text-gray-400 line-through">¥{item.originalPrice}</span>
                          )}
                        </div>

                        {/* 数量控制 */}
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            disabled={item.quantity <= 1}
                            className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            -
                          </button>
                          <span className="w-12 text-center font-medium">{item.quantity}</span>
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            disabled={item.quantity >= item.stock}
                            className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            +
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 结算面板 */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg p-6 shadow-sm sticky top-24">
                <h3 className="text-lg font-medium text-gray-900 mb-4">结算信息</h3>
                
                <div className="space-y-3 mb-6">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">商品件数</span>
                    <span>{getSelectedCount()} 件</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">商品总价</span>
                    <span>¥{calculateOriginalTotal().toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm text-green-600">
                    <span>优惠减免</span>
                    <span>-¥{calculateSavings().toFixed(2)}</span>
                  </div>
                  <div className="border-t pt-3">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-medium text-gray-900">实付金额</span>
                      <span className="text-2xl font-bold text-red-600">¥{calculateTotal().toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                <button
                  disabled={selectedItems.size === 0}
                  onClick={handleCheckout}
                  className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-shadow disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  结算 ({selectedItems.size})
                </button>

                <div className="mt-4 space-y-2 text-xs text-gray-500">
                  <p>• 支持支付宝、微信支付</p>
                  <p>• 支持7天无理由退换货</p>
                  <p>• 满199元免运费</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 