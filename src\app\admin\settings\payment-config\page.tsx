'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdmin } from '../../../../contexts/AdminContext';
import Link from 'next/link';

interface PaymentQR {
  id: number;
  qrId: string;
  paymentMethod: 'WECHAT' | 'ALIPAY' | 'BANK';
  qrCodeImage: string;
  accountName: string;
  accountInfo?: string;
  isActive: boolean;
  usageCount: number;
  createTime: string;
  lastUsedTime?: string;
}

export default function PaymentConfig() {
  const router = useRouter();
  const { admin, isLoading: adminLoading } = useAdmin();
  const [paymentQRs, setPaymentQRs] = useState<PaymentQR[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadForm, setUploadForm] = useState<{
    paymentMethod: 'WECHAT' | 'ALIPAY' | 'BANK';
    qrCodeImage: string;
    accountName: string;
    accountInfo: string;
  }>({
    paymentMethod: 'WECHAT',
    qrCodeImage: '',
    accountName: '',
    accountInfo: ''
  });

  useEffect(() => {
    if (!adminLoading) {
      if (admin) {
        fetchPaymentQRs();
      } else {
        router.push('/admin/login');
      }
    }
  }, [admin, adminLoading, router]);

  const fetchPaymentQRs = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch('http://localhost:8080/api/admin/fund/payment-qr', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        setPaymentQRs(result.data || []);
      } else {
        console.error('获取收款码列表失败');
      }
    } catch (error) {
      console.error('获取收款码列表错误:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const base64 = e.target?.result as string;
        setUploadForm(prev => ({ ...prev, qrCodeImage: base64 }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUpload = async () => {
    if (!uploadForm.qrCodeImage || !uploadForm.accountName) {
      alert('请填写完整信息并上传二维码图片');
      return;
    }

    setUploading(true);
    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch('http://localhost:8080/api/admin/fund/payment-qr', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(uploadForm)
      });

      if (response.ok) {
        alert('收款码上传成功');
        setShowUploadModal(false);
        setUploadForm({
          paymentMethod: 'WECHAT',
          qrCodeImage: '',
          accountName: '',
          accountInfo: ''
        });
        fetchPaymentQRs();
      } else {
        const result = await response.json();
        alert(`上传失败: ${result.message}`);
      }
    } catch (error) {
      console.error('上传收款码错误:', error);
      alert('上传失败，请稍后重试');
    } finally {
      setUploading(false);
    }
  };

  const toggleQRStatus = async (qrId: string, currentStatus: boolean) => {
    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch(`http://localhost:8080/api/admin/fund/payment-qr/${qrId}/status`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ isActive: !currentStatus })
      });

      if (response.ok) {
        fetchPaymentQRs();
      } else {
        alert('更新状态失败');
      }
    } catch (error) {
      console.error('更新状态错误:', error);
      alert('更新状态失败');
    }
  };

  const deleteQR = async (qrId: string) => {
    if (!confirm('确定要删除这个收款码吗？')) {
      return;
    }

    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch(`http://localhost:8080/api/admin/fund/payment-qr/${qrId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        alert('删除成功');
        fetchPaymentQRs();
      } else {
        alert('删除失败');
      }
    } catch (error) {
      console.error('删除收款码错误:', error);
      alert('删除失败');
    }
  };

  const getPaymentMethodName = (method: string) => {
    switch (method) {
      case 'WECHAT': return '微信支付';
      case 'ALIPAY': return '支付宝';
      case 'BANK': return '银行转账';
      default: return method;
    }
  };

  const getPaymentMethodColor = (method: string) => {
    switch (method) {
      case 'WECHAT': return 'bg-green-500';
      case 'ALIPAY': return 'bg-blue-500';
      case 'BANK': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  if (adminLoading) {
    return <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
    </div>;
  }

  if (!admin) {
    return null; // 会被useEffect重定向到登录页
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <nav className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin/settings" className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-sm mr-3">
                ←
              </Link>
              <h1 className="text-xl font-bold text-white">支付设置</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/admin/fund-management" className="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition-colors">
                💰 资金管理
              </Link>
              <Link href="/admin/dashboard" className="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition-colors">
                📊 仪表盘
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto p-6">
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-2xl font-bold text-white mb-2">收款码管理</h2>
              <p className="text-gray-400">管理支付宝、微信等收款码，用户支付时将显示这些收款码</p>
            </div>
            <button
              onClick={() => setShowUploadModal(true)}
              className="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
            >
              + 添加收款码
            </button>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {paymentQRs.map((qr) => (
                <div key={qr.qrId} className="bg-gray-700 rounded-lg p-6 border border-gray-600">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex items-center">
                      <span className={`${getPaymentMethodColor(qr.paymentMethod)} text-white px-2 py-1 rounded text-sm font-medium`}>
                        {getPaymentMethodName(qr.paymentMethod)}
                      </span>
                      <span className={`ml-2 px-2 py-1 rounded text-sm ${qr.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        {qr.isActive ? '启用' : '停用'}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => toggleQRStatus(qr.qrId, qr.isActive)}
                        className={`px-2 py-1 rounded text-sm transition-colors ${
                          qr.isActive 
                            ? 'bg-yellow-500 hover:bg-yellow-600 text-white' 
                            : 'bg-green-500 hover:bg-green-600 text-white'
                        }`}
                      >
                        {qr.isActive ? '停用' : '启用'}
                      </button>
                      <button
                        onClick={() => deleteQR(qr.qrId)}
                        className="bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded text-sm transition-colors"
                      >
                        删除
                      </button>
                    </div>
                  </div>
                  
                  <div className="text-center mb-4">
                    <img
                      src={qr.qrCodeImage}
                      alt="收款码"
                      className="w-32 h-32 object-contain mx-auto bg-white rounded-lg p-2"
                    />
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">账户名:</span>
                      <span className="text-white">{qr.accountName}</span>
                    </div>
                    {qr.accountInfo && (
                      <div className="flex justify-between">
                        <span className="text-gray-400">账户信息:</span>
                        <span className="text-white">{qr.accountInfo}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-400">使用次数:</span>
                      <span className="text-white">{qr.usageCount} 次</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">创建时间:</span>
                      <span className="text-white">{new Date(qr.createTime).toLocaleDateString()}</span>
                    </div>
                    {qr.lastUsedTime && (
                      <div className="flex justify-between">
                        <span className="text-gray-400">最后使用:</span>
                        <span className="text-white">{new Date(qr.lastUsedTime).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              
              {paymentQRs.length === 0 && (
                <div className="col-span-full text-center py-12">
                  <div className="text-gray-400 text-lg mb-4">还没有收款码</div>
                  <p className="text-gray-500">点击"添加收款码"开始配置支付方式</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 上传收款码模态框 */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-white">添加收款码</h3>
              <button
                onClick={() => setShowUploadModal(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  支付方式
                </label>
                <select
                  value={uploadForm.paymentMethod}
                  onChange={(e) => setUploadForm(prev => ({ 
                    ...prev, 
                    paymentMethod: e.target.value as 'WECHAT' | 'ALIPAY' | 'BANK'
                  }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-green-500"
                >
                  <option value="WECHAT">微信支付</option>
                  <option value="ALIPAY">支付宝</option>
                  <option value="BANK">银行转账</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  账户名 *
                </label>
                <input
                  type="text"
                  value={uploadForm.accountName}
                  onChange={(e) => setUploadForm(prev => ({ ...prev, accountName: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-green-500"
                  placeholder="请输入账户名"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  账户信息（可选）
                </label>
                <input
                  type="text"
                  value={uploadForm.accountInfo}
                  onChange={(e) => setUploadForm(prev => ({ ...prev, accountInfo: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-green-500"
                  placeholder="如账号、备注等"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  收款码图片 *
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-green-500"
                />
                {uploadForm.qrCodeImage && (
                  <div className="mt-2 text-center">
                    <img
                      src={uploadForm.qrCodeImage}
                      alt="预览"
                      className="w-32 h-32 object-contain mx-auto bg-white rounded-lg p-2"
                    />
                  </div>
                )}
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowUploadModal(false)}
                className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleUpload}
                disabled={uploading}
                className="flex-1 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {uploading ? '上传中...' : '确认上传'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 
          </div>
        </div>
      )}
    </div>
  );
} 