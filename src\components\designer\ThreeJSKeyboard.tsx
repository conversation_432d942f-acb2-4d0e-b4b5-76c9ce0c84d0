'use client';

import React, { useRef, useEffect, useMemo } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader.js';
import { createPreciseKeycapGeometry, createKeycapMaterial } from './KeycapGeometry';

interface KeycapCustomization {
  keyId: string;
  textColor: string;
  fontSize: number;
  fontFamily: string;
  fontWeight: string;
  textStyle: string;
  textDecoration: string;
  backgroundImage?: string;
  backgroundOpacity: number;
  backgroundScale: number;
  backgroundPosition: { x: number; y: number };
  coverageMode: 'top-only' | 'full-keycap';
}

interface ThreeJSKeyboardProps {
  layoutId: string;
  selectedKeys: string[];
  onKeyClick: (keyId: string, event?: React.MouseEvent) => void;
  designElements: any[];
  keycapCustomizations?: Record<string, KeycapCustomization>;
  className?: string;
}

interface KeycapInfo {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  text: string;
  row: number;
}

const ThreeJSKeyboard: React.FC<ThreeJSKeyboardProps> = ({
  layoutId,
  selectedKeys,
  onKeyClick,
  designElements,
  keycapCustomizations = {},
  className = ''
}) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene>();
  const rendererRef = useRef<THREE.WebGLRenderer>();
  const cameraRef = useRef<THREE.PerspectiveCamera>();
  const controlsRef = useRef<OrbitControls>();
  const keycapsRef = useRef<Map<string, THREE.Group>>(new Map());

  // 键盘布局数据
  const keyboardLayout = useMemo<KeycapInfo[]>(() => {
    const layout: KeycapInfo[] = [];
    
    // 基于技术图2的精确尺寸计算
    // 标准键帽：17.2mm宽度 + 0.8mm间距 = 18.0mm总间距
    
    // 处理所有支持的布局，默认使用108键布局
    if (layoutId === '108' || layoutId === '104' || layoutId === '97' || layoutId === '87' || layoutId === '75' || layoutId === '68' || layoutId === '61') {
      // 第一行 - ESC和功能键（与SVG布局保持一致）
      layout.push({ id: 'key-esc', x: 20, y: 15, width: 55, height: 55, text: 'ESC', row: 1 });
      // F1-F4组
      layout.push({ id: 'key-f1', x: 135, y: 15, width: 55, height: 55, text: 'F1', row: 1 });
      layout.push({ id: 'key-f2', x: 192, y: 15, width: 55, height: 55, text: 'F2', row: 1 });
      layout.push({ id: 'key-f3', x: 249, y: 15, width: 55, height: 55, text: 'F3', row: 1 });
      layout.push({ id: 'key-f4', x: 306, y: 15, width: 55, height: 55, text: 'F4', row: 1 });
      // F5-F8组
      layout.push({ id: 'key-f5', x: 393, y: 15, width: 55, height: 55, text: 'F5', row: 1 });
      layout.push({ id: 'key-f6', x: 450, y: 15, width: 55, height: 55, text: 'F6', row: 1 });
      layout.push({ id: 'key-f7', x: 507, y: 15, width: 55, height: 55, text: 'F7', row: 1 });
      layout.push({ id: 'key-f8', x: 564, y: 15, width: 55, height: 55, text: 'F8', row: 1 });
      // F9-F12组
      layout.push({ id: 'key-f9', x: 650, y: 15, width: 55, height: 55, text: 'F9', row: 1 });
      layout.push({ id: 'key-f10', x: 707, y: 15, width: 55, height: 55, text: 'F10', row: 1 });
      layout.push({ id: 'key-f11', x: 764, y: 15, width: 55, height: 55, text: 'F11', row: 1 });
      layout.push({ id: 'key-f12', x: 821, y: 15, width: 55, height: 55, text: 'F12', row: 1 });
      // 右上功能键组
      layout.push(
        { id: 'key-prt', x: 894, y: 15, width: 55, height: 55, text: 'PRT', row: 1 },
        { id: 'key-scr', x: 951, y: 15, width: 55, height: 55, text: 'SCR', row: 1 },
        { id: 'key-pau', x: 1008, y: 15, width: 55, height: 55, text: 'PAU', row: 1 }
      );

      // 第二行 - 数字键（与SVG布局保持一致）
      layout.push({ id: 'key-grave', x: 20, y: 100, width: 55, height: 55, text: '~', row: 2 });
      layout.push({ id: 'key-1', x: 77, y: 100, width: 55, height: 55, text: '1', row: 2 });
      layout.push({ id: 'key-2', x: 134, y: 100, width: 55, height: 55, text: '2', row: 2 });
      layout.push({ id: 'key-3', x: 191, y: 100, width: 55, height: 55, text: '3', row: 2 });
      layout.push({ id: 'key-4', x: 248, y: 100, width: 55, height: 55, text: '4', row: 2 });
      layout.push({ id: 'key-5', x: 305, y: 100, width: 55, height: 55, text: '5', row: 2 });
      layout.push({ id: 'key-6', x: 362, y: 100, width: 55, height: 55, text: '6', row: 2 });
      layout.push({ id: 'key-7', x: 419, y: 100, width: 55, height: 55, text: '7', row: 2 });
      layout.push({ id: 'key-8', x: 476, y: 100, width: 55, height: 55, text: '8', row: 2 });
      layout.push({ id: 'key-9', x: 533, y: 100, width: 55, height: 55, text: '9', row: 2 });
      layout.push({ id: 'key-0', x: 590, y: 100, width: 55, height: 55, text: '0', row: 2 });
      layout.push({ id: 'key-minus', x: 647, y: 100, width: 55, height: 55, text: '-', row: 2 });
      layout.push({ id: 'key-equal', x: 704, y: 100, width: 55, height: 55, text: '=', row: 2 });
      layout.push({ id: 'key-backspace', x: 761, y: 100, width: 113, height: 55, text: 'BACK', row: 2 });
      layout.push(
        { id: 'key-ins', x: 894, y: 100, width: 55, height: 55, text: 'INS', row: 2 },
        { id: 'key-home', x: 951, y: 100, width: 55, height: 55, text: 'HM', row: 2 },
        { id: 'key-pageup', x: 1008, y: 100, width: 55, height: 55, text: 'UP', row: 2 }
      );

      // 第三行 - QWERTY（与SVG布局保持一致）
      layout.push({ id: 'key-tab', x: 20, y: 157, width: 84, height: 55, text: 'TAB', row: 3 });
      layout.push({ id: 'key-q', x: 106, y: 157, width: 55, height: 55, text: 'Q', row: 3 });
      layout.push({ id: 'key-w', x: 163, y: 157, width: 55, height: 55, text: 'W', row: 3 });
      layout.push({ id: 'key-e', x: 220, y: 157, width: 55, height: 55, text: 'E', row: 3 });
      layout.push({ id: 'key-r', x: 277, y: 157, width: 55, height: 55, text: 'R', row: 3 });
      layout.push({ id: 'key-t', x: 334, y: 157, width: 55, height: 55, text: 'T', row: 3 });
      layout.push({ id: 'key-y', x: 391, y: 157, width: 55, height: 55, text: 'Y', row: 3 });
      layout.push({ id: 'key-u', x: 448, y: 157, width: 55, height: 55, text: 'U', row: 3 });
      layout.push({ id: 'key-i', x: 505, y: 157, width: 55, height: 55, text: 'I', row: 3 });
      layout.push({ id: 'key-o', x: 562, y: 157, width: 55, height: 55, text: 'O', row: 3 });
      layout.push({ id: 'key-p', x: 619, y: 157, width: 55, height: 55, text: 'P', row: 3 });
      layout.push({ id: 'key-lbracket', x: 676, y: 157, width: 55, height: 55, text: '[', row: 3 });
      layout.push({ id: 'key-rbracket', x: 733, y: 157, width: 55, height: 55, text: ']', row: 3 });
      layout.push({ id: 'key-backslash', x: 790, y: 157, width: 84, height: 55, text: '\\', row: 3 });
      layout.push(
        { id: 'key-del', x: 894, y: 157, width: 55, height: 55, text: 'DEL', row: 3 },
        { id: 'key-end', x: 951, y: 157, width: 55, height: 55, text: 'END', row: 3 },
        { id: 'key-pagedown', x: 1008, y: 157, width: 55, height: 55, text: 'DN', row: 3 }
      );

      // 第四行 - ASDF（与SVG布局保持一致）
      layout.push({ id: 'key-caps', x: 20, y: 214, width: 99, height: 55, text: 'CAPS', row: 4 });
      layout.push({ id: 'key-a', x: 121, y: 214, width: 55, height: 55, text: 'A', row: 4 });
      layout.push({ id: 'key-s', x: 178, y: 214, width: 55, height: 55, text: 'S', row: 4 });
      layout.push({ id: 'key-d', x: 235, y: 214, width: 55, height: 55, text: 'D', row: 4 });
      layout.push({ id: 'key-f', x: 292, y: 214, width: 55, height: 55, text: 'F', row: 4 });
      layout.push({ id: 'key-g', x: 349, y: 214, width: 55, height: 55, text: 'G', row: 4 });
      layout.push({ id: 'key-h', x: 406, y: 214, width: 55, height: 55, text: 'H', row: 4 });
      layout.push({ id: 'key-j', x: 463, y: 214, width: 55, height: 55, text: 'J', row: 4 });
      layout.push({ id: 'key-k', x: 520, y: 214, width: 55, height: 55, text: 'K', row: 4 });
      layout.push({ id: 'key-l', x: 577, y: 214, width: 55, height: 55, text: 'L', row: 4 });
      layout.push({ id: 'key-semicolon', x: 634, y: 214, width: 55, height: 55, text: ';', row: 4 });
      layout.push({ id: 'key-quote', x: 691, y: 214, width: 55, height: 55, text: "'", row: 4 });
      layout.push({ id: 'key-enter', x: 748, y: 214, width: 126, height: 55, text: 'ENTER', row: 4 });

      // 第五行 - ZXCV（与SVG布局保持一致）
      layout.push({ id: 'key-lshift', x: 20, y: 271, width: 129, height: 55, text: 'SHIFT', row: 5 });
      layout.push({ id: 'key-z', x: 151, y: 271, width: 55, height: 55, text: 'Z', row: 5 });
      layout.push({ id: 'key-x', x: 208, y: 271, width: 55, height: 55, text: 'X', row: 5 });
      layout.push({ id: 'key-c', x: 265, y: 271, width: 55, height: 55, text: 'C', row: 5 });
      layout.push({ id: 'key-v', x: 322, y: 271, width: 55, height: 55, text: 'V', row: 5 });
      layout.push({ id: 'key-b', x: 379, y: 271, width: 55, height: 55, text: 'B', row: 5 });
      layout.push({ id: 'key-n', x: 436, y: 271, width: 55, height: 55, text: 'N', row: 5 });
      layout.push({ id: 'key-m', x: 493, y: 271, width: 55, height: 55, text: 'M', row: 5 });
      layout.push({ id: 'key-comma', x: 550, y: 271, width: 55, height: 55, text: ',', row: 5 });
      layout.push({ id: 'key-period', x: 607, y: 271, width: 55, height: 55, text: '.', row: 5 });
      layout.push({ id: 'key-slash', x: 664, y: 271, width: 55, height: 55, text: '/', row: 5 });
      layout.push({ id: 'key-rshift', x: 721, y: 271, width: 154, height: 55, text: 'SHIFT', row: 5 });
      layout.push({ id: 'key-up', x: 951, y: 271, width: 55, height: 55, text: '↑', row: 5 });

      // 第六行 - 底部控制键（与SVG布局保持一致）
      layout.push(
        { id: 'key-lctrl', x: 20, y: 328, width: 71, height: 55, text: 'CTRL', row: 6 },
        { id: 'key-lwin', x: 93, y: 328, width: 71, height: 55, text: 'WIN', row: 6 },
        { id: 'key-lalt', x: 166, y: 328, width: 71, height: 55, text: 'ALT', row: 6 },
        { id: 'key-space', x: 239, y: 328, width: 346, height: 55, text: 'SPACE', row: 6 },
        { id: 'key-ralt', x: 587, y: 328, width: 71, height: 55, text: 'ALT', row: 6 },
        { id: 'key-fn', x: 660, y: 328, width: 71, height: 55, text: 'FN', row: 6 },
        { id: 'key-menu', x: 731, y: 328, width: 71, height: 55, text: 'MENU', row: 6 },
        { id: 'key-rctrl', x: 804, y: 328, width: 71, height: 55, text: 'CTRL', row: 6 },
        { id: 'key-left', x: 894, y: 328, width: 55, height: 55, text: '←', row: 6 },
        { id: 'key-down', x: 951, y: 328, width: 55, height: 55, text: '↓', row: 6 },
        { id: 'key-right', x: 1008, y: 328, width: 55, height: 55, text: '→', row: 6 }
      );

      // 小键盘区域（仅108键版本有）
      if (layoutId === '108') {
        layout.push(
          // 第一行
          { id: 'key-numlock', x: 1081, y: 100, width: 55, height: 55, text: 'NUM', row: 2 },
          { id: 'key-numdiv', x: 1138, y: 100, width: 55, height: 55, text: '/', row: 2 },
          { id: 'key-nummul', x: 1195, y: 100, width: 55, height: 55, text: '*', row: 2 },
          { id: 'key-numsub', x: 1252, y: 100, width: 55, height: 55, text: '-', row: 2 },
          // 第二行
          { id: 'key-num7', x: 1081, y: 157, width: 55, height: 55, text: '7', row: 3 },
          { id: 'key-num8', x: 1138, y: 157, width: 55, height: 55, text: '8', row: 3 },
          { id: 'key-num9', x: 1195, y: 157, width: 55, height: 55, text: '9', row: 3 },
          { id: 'key-numadd', x: 1252, y: 157, width: 55, height: 112, text: '+', row: 3 },
          // 第三行
          { id: 'key-num4', x: 1081, y: 214, width: 55, height: 55, text: '4', row: 4 },
          { id: 'key-num5', x: 1138, y: 214, width: 55, height: 55, text: '5', row: 4 },
          { id: 'key-num6', x: 1195, y: 214, width: 55, height: 55, text: '6', row: 4 },
          // 第四行
          { id: 'key-num1', x: 1081, y: 271, width: 55, height: 55, text: '1', row: 5 },
          { id: 'key-num2', x: 1138, y: 271, width: 55, height: 55, text: '2', row: 5 },
          { id: 'key-num3', x: 1195, y: 271, width: 55, height: 55, text: '3', row: 5 },
          { id: 'key-numenter', x: 1252, y: 271, width: 55, height: 113, text: 'ENT', row: 5 },
          // 第五行
          { id: 'key-num0', x: 1081, y: 328, width: 113, height: 55, text: '0', row: 6 },
          { id: 'key-numdot', x: 1195, y: 328, width: 55, height: 55, text: '.', row: 6 }
        );
      }

      // 根据不同布局调整键帽位置和数量
      if (layoutId === '87' || layoutId === '75' || layoutId === '68' || layoutId === '61') {
        // 紧凑布局：移除功能键区域的间距，调整整体布局
        // 这些布局通常没有小键盘，键帽更紧凑
        console.log(`使用紧凑布局: ${layoutId}`);
      }
    }

    console.log(`3D键盘布局生成完成: ${layoutId}, 键帽数量: ${layout.length}`);
    return layout;
  }, [layoutId]);

  // 基于SVG伪3D参数创建精确的键帽几何体
  const createSVGBasedKeycapGeometry = (
    bottomWidth: number,
    bottomHeight: number,
    topWidth: number,
    topHeight: number,
    height: number
  ) => {
    const geometry = new THREE.BufferGeometry();

    // 顶点数据
    const vertices: number[] = [];
    const indices: number[] = [];
    const normals: number[] = [];
    const uvs: number[] = [];

    // 底面顶点（y=0）
    const bw = bottomWidth / 2;
    const bh = bottomHeight / 2;
    vertices.push(-bw, 0, -bh); // 0: 左下
    vertices.push( bw, 0, -bh); // 1: 右下
    vertices.push( bw, 0,  bh); // 2: 右上
    vertices.push(-bw, 0,  bh); // 3: 左上

    // 顶面顶点（y=height）
    const tw = topWidth / 2;
    const th = topHeight / 2;
    vertices.push(-tw, height, -th); // 4: 左下
    vertices.push( tw, height, -th); // 5: 右下
    vertices.push( tw, height,  th); // 6: 右上
    vertices.push(-tw, height,  th); // 7: 左上

    // 底面（向下的法向量）
    indices.push(0, 2, 1, 0, 3, 2);

    // 顶面（向上的法向量）
    indices.push(4, 5, 6, 4, 6, 7);

    // 侧面（4个面）
    // 前面 (z = -h/2)
    indices.push(0, 1, 5, 0, 5, 4);
    // 右面 (x = w/2)
    indices.push(1, 2, 6, 1, 6, 5);
    // 后面 (z = h/2)
    indices.push(2, 3, 7, 2, 7, 6);
    // 左面 (x = -w/2)
    indices.push(3, 0, 4, 3, 4, 7);

    // 计算法向量（简化处理）
    const bottomNormals = [0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1, 0];
    const topNormals = [0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0];
    const frontNormals = [0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1];
    const rightNormals = [1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0];
    const backNormals = [0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1];
    const leftNormals = [-1, 0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0];

    normals.push(...bottomNormals, ...topNormals, ...frontNormals, ...rightNormals, ...backNormals, ...leftNormals);

    // UV坐标
    const faceUVs = [0, 0, 1, 0, 1, 1, 0, 1];
    for (let i = 0; i < 6; i++) {
      uvs.push(...faceUVs);
    }

    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setAttribute('normal', new THREE.Float32BufferAttribute(normals, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setIndex(indices);

    geometry.computeVertexNormals();
    return geometry;
  };

  // 【已迁移到KeycapGeometry.tsx】- 使用导入的专业PBT键帽材质



  // 创建单个独立键帽 - 与2D SVG参数完全一致
  const createKeycap = (keyInfo: KeycapInfo) => {
    const group = new THREE.Group();
    group.userData = { keyId: keyInfo.id, clickable: false }; // 3D预览时禁用点击

    // 使用与2D SVG完全一致的参数
    // 像素与mm转换比例（基于标准键帽55px = 18.2mm）
    const mmToPixel = 55 / 18.2; // 1mm = 3.02像素
    const pixelToMm = 1 / mmToPixel; // 像素转mm

    // SVG中的精确尺寸参数（mm）
    const keycapHeightMm = 4.0; // 键帽高度：4mm（标准OEM高度）

    // 间距参数（mm）
    const horizontalMarginMm = 3.21; // 左右各3.21mm间距
    const topMarginMm = 0.77;        // 顶部0.77mm间距
    const bottomMarginMm = 3.58;     // 底部3.58mm间距

    // 将SVG像素尺寸转换为3D世界单位
    const scale = 0.1; // 3D世界缩放因子

    // 键帽实际尺寸（基于SVG像素尺寸）
    const bottomWidth = keyInfo.width * pixelToMm * scale;
    const bottomHeight = keyInfo.height * pixelToMm * scale;
    const topWidth = (keyInfo.width - 2 * horizontalMarginMm * mmToPixel) * pixelToMm * scale;
    const topHeight = (keyInfo.height - (topMarginMm + bottomMarginMm) * mmToPixel) * pixelToMm * scale;
    const height = keycapHeightMm * scale;

    // 键盘整体中心点（用于居中显示）
    const keyboardCenter = {
      x: 663.5,  // (20 + 1307) / 2
      y: 199     // (15 + 383) / 2
    };

    // 键帽在3D空间中的位置 - 直接对应SVG坐标
    const x3d = (keyInfo.x + keyInfo.width / 2 - keyboardCenter.x) * pixelToMm * scale;
    const z3d = -(keyInfo.y + keyInfo.height / 2 - keyboardCenter.y) * pixelToMm * scale; // Y轴翻转
    const y3d = height / 2; // 键帽底部在y=0平面上

    group.position.set(x3d, y3d, z3d);

    // 调试信息：输出前几个键帽的位置和尺寸
    if (keyInfo.id === 'key-esc' || keyInfo.id === 'key-f1' || keyInfo.id === 'key-1') {
      console.log(`键帽 ${keyInfo.id}: SVG(${keyInfo.x}, ${keyInfo.y}, ${keyInfo.width}×${keyInfo.height}) -> 3D(${x3d.toFixed(2)}, ${y3d.toFixed(2)}, ${z3d.toFixed(2)}) 尺寸(${bottomWidth.toFixed(2)}×${bottomHeight.toFixed(2)}×${height.toFixed(2)})`);
    }

    // 创建基于SVG伪3D参数的精确几何体
    const geometry = createSVGBasedKeycapGeometry(bottomWidth, bottomHeight, topWidth, topHeight, height);

    // 检查是否有自定义设计
    const hasCustomDesign = designElements.some(el => {
      const keycapCenterX = keyInfo.x + keyInfo.width / 2;
      const keycapCenterY = keyInfo.y + keyInfo.height / 2;
      return el.x <= keycapCenterX &&
             keycapCenterX <= el.x + el.width &&
             el.y <= keycapCenterY &&
             keycapCenterY <= el.y + el.height;
    });

    // 获取键帽自定义设置
    const customization = keycapCustomizations[keyInfo.id];

    // 3D预览模式下不显示选中状态，保持统一的白色外观
    let material = createKeycapMaterial(0xf8f8f8); // 统一白色

    // 如果有背景图像，则应用纹理
    if (customization?.backgroundImage) {
      const textureLoader = new THREE.TextureLoader();
      const texture = textureLoader.load(customization.backgroundImage);
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;

      material = createKeycapMaterial(0xffffff);
      material.map = texture;
      material.transparent = true;
      material.opacity = customization.backgroundOpacity || 0.8;
    }

    // 如果有设计元素覆盖此键帽，应用相应的视觉效果
    if (hasCustomDesign) {
      // 找到覆盖此键帽的设计元素
      const coveringElement = designElements.find(el => {
        const keycapCenterX = keyInfo.x + keyInfo.width / 2;
        const keycapCenterY = keyInfo.y + keyInfo.height / 2;
        return el.x <= keycapCenterX &&
               keycapCenterX <= el.x + el.width &&
               el.y <= keycapCenterY &&
               keycapCenterY <= el.y + el.height;
      });

      if (coveringElement) {
        const textureLoader = new THREE.TextureLoader();
        const texture = textureLoader.load(coveringElement.imageUrl);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;

        material = createKeycapMaterial(0xffffff);
        material.map = texture;
        material.transparent = true;
        material.opacity = coveringElement.opacity || 0.8;
      }
    }

    const keycap = new THREE.Mesh(geometry, material);
    keycap.castShadow = true;
    keycap.receiveShadow = true;

    group.add(keycap);

    return group;
  };

  // 初始化Three.js场景
  useEffect(() => {
    if (!mountRef.current) return;

    console.log('3D键盘初始化开始...');

    // 创建场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf5f5f5);
    sceneRef.current = scene;

    // 创建相机 - 优化视角以观察基于SVG参数的键帽
    const width = mountRef.current.clientWidth || 800;
    const height = mountRef.current.clientHeight || 400;
    const aspectRatio = width / height;
    const camera = new THREE.PerspectiveCamera(45, aspectRatio, 0.1, 1000);
    camera.position.set(0, 25, 35);     // 调整到合适的位置观察键帽
    camera.lookAt(0, 0, 0);             // 看向键盘中心
    cameraRef.current = camera;

    console.log('相机设置完成，位置:', camera.position, '尺寸:', width, 'x', height);

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ 
      antialias: true,
      alpha: false,
      powerPreference: "high-performance"
    });
    
    // 动态设置渲染器大小
    const resizeRenderer = () => {
      const width = mountRef.current?.clientWidth || 800;
      const height = mountRef.current?.clientHeight || 400;
      renderer.setSize(width, height);
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
    };
    
    resizeRenderer();
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.0;
    renderer.setClearColor(0xf5f5f5, 1);
    rendererRef.current = renderer;

    mountRef.current.appendChild(renderer.domElement);

    // 添加控制器 - 优化交互体验
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.08;      // 更平滑的阻尼
    controls.minDistance = 60;          // 最小缩放距离
    controls.maxDistance = 250;         // 最大缩放距离
    controls.maxPolarAngle = Math.PI / 2.1;  // 限制垂直旋转
    controls.minPolarAngle = Math.PI / 8;    // 限制最小角度
    controls.enablePan = true;          // 允许平移
    controls.panSpeed = 0.8;            // 平移速度
    controls.rotateSpeed = 0.5;         // 旋转速度
    controls.zoomSpeed = 0.6;           // 缩放速度
    controls.autoRotate = false;        // 关闭自动旋转
    controls.target.set(0, 0, 0);       // 确保围绕键盘中心旋转
    controls.minDistance = 10;          // 调整最小距离以更好观察键帽
    controls.maxDistance = 100;         // 调整最大距离
    controlsRef.current = controls;

    // 简化光照系统 - 专注于键帽清晰显示
    const lights = {
      // 环境光 - 提供基础照明
      ambient: new THREE.AmbientLight(0xffffff, 0.6),

      // 主要方向光 - 清晰显示键帽形状
      directional: new THREE.DirectionalLight(0xffffff, 0.8),

      // 填充光 - 减少阴影
      fill: new THREE.DirectionalLight(0xffffff, 0.3)
    };

    // 配置主要方向光
    lights.directional.position.set(30, 50, 30);
    lights.directional.castShadow = true;
    lights.directional.shadow.mapSize.width = 1024;
    lights.directional.shadow.mapSize.height = 1024;

    // 配置填充光
    lights.fill.position.set(-30, 30, -30);

    // 添加所有光源到场景
    Object.values(lights).forEach(light => {
      scene.add(light);
    });

    // 移除底座，只保留键帽
    console.log('3D预览模式：只渲染键帽，不包含底座');

    // 创建键帽
    console.log('开始创建键帽，数量:', keyboardLayout.length);
    console.log('布局ID:', layoutId);
    console.log('前5个键帽布局数据:', keyboardLayout.slice(0, 5));

    if (keyboardLayout.length === 0) {
      console.error('❌ 键帽布局为空！检查layoutId:', layoutId);
      return;
    }
    
    keyboardLayout.forEach((keyInfo, index) => {
      const keycap = createKeycap(keyInfo);
      scene.add(keycap);
      keycapsRef.current.set(keyInfo.id, keycap);

      // 调试信息（仅前5个键帽）
      if (index < 5) {
        console.log(`键帽 ${index}:`, keyInfo.id, 'SVG坐标:', {x: keyInfo.x, y: keyInfo.y}, '3D位置:', keycap.position);
      }
    });
    console.log('键帽创建完成，场景中的对象数量:', scene.children.length);

    // 3D预览模式不需要点击事件处理
    console.log('3D预览模式已启用，键帽点击功能已禁用');

    // 渲染循环
    const animate = () => {
      requestAnimationFrame(animate);
      controls.update();
      renderer.render(scene, camera);
    };
    
    console.log('开始渲染循环');
    animate();

    // 清理函数
    return () => {
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();
    };
  }, [keyboardLayout, onKeyClick]);

  // 更新选中状态
  useEffect(() => {
    keyboardLayout.forEach(keyInfo => {
      const keycap = keycapsRef.current.get(keyInfo.id);
      if (keycap) {
        const mesh = keycap.children.find(child => 
          child instanceof THREE.Mesh && 
          child.material instanceof THREE.MeshPhysicalMaterial
        ) as THREE.Mesh<THREE.BufferGeometry, THREE.MeshPhysicalMaterial> | undefined;
        
        if (mesh && mesh.material) {
          const isSelected = selectedKeys.includes(keyInfo.id);
          const hasCustomDesign = designElements.some(el => {
            // 现在坐标已统一，无需额外偏移
            const keycapCenterX = keyInfo.x + keyInfo.width / 2;
            const keycapCenterY = keyInfo.y + keyInfo.height / 2;
            return el.x <= keycapCenterX &&
                   keycapCenterX <= el.x + el.width &&
                   el.y <= keycapCenterY &&
                   keycapCenterY <= el.y + el.height;
          });
          
          // 获取键帽自定义设置
          const customization = keycapCustomizations[keyInfo.id];

          // 更新材质
          if (isSelected) {
            mesh.material.color.setHex(0x3b82f6);
            mesh.material.emissive.setHex(0x1e3a8a);
            mesh.material.emissiveIntensity = 0.1;
          } else {
            // 如果有背景图像且为全键帽覆盖模式，保持白色基础色
            const hasBackgroundImage = customization?.backgroundImage && customization.coverageMode === 'full-keycap';
            mesh.material.color.setHex(hasBackgroundImage || hasCustomDesign ? 0xffffff : 0xf8f8f8);
            mesh.material.emissive.setHex(0x000000);
            mesh.material.emissiveIntensity = 0;
          }

          // 更新背景图像纹理和透明度
          if (customization?.backgroundImage && customization.coverageMode === 'full-keycap') {
            if (!mesh.material.map || mesh.material.map.image?.src !== customization.backgroundImage) {
              const textureLoader = new THREE.TextureLoader();
              const texture = textureLoader.load(customization.backgroundImage);
              texture.wrapS = THREE.RepeatWrapping;
              texture.wrapT = THREE.RepeatWrapping;
              mesh.material.map = texture;
              mesh.material.needsUpdate = true;
            }
            mesh.material.transparent = true;
            mesh.material.opacity = customization.backgroundOpacity;
          } else {
            // 清除纹理
            if (mesh.material.map) {
              mesh.material.map = null;
              mesh.material.transparent = false;
              mesh.material.opacity = 1;
              mesh.material.needsUpdate = true;
            }
          }
        }
      }
    });
  }, [selectedKeys, designElements, keyboardLayout, keycapCustomizations]);

  return (
    <div className={`w-full h-full ${className}`}>
      <div 
        ref={mountRef} 
        className="w-full h-full relative"
        style={{ minHeight: '400px' }}
      >
        {/* 添加加载提示 */}
        <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded z-10">
          3D渲染中... 键盘数量: {keyboardLayout.length}
        </div>
      </div>
    </div>
  );
};

export default ThreeJSKeyboard; 