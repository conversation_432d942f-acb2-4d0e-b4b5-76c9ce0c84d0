'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import Link from 'next/link';

interface User {
  id: number;
  username: string;
  nickname?: string;
  avatarPath?: string;
}

interface Session {
  sessionId: string;
  userId: number;
  username: string;
  nickname?: string;
  avatarPath?: string;
  agentId?: string;
  agentName?: string;
  status: string;
  startTime: string;
  connectTime?: string;
  endTime?: string;
  lastActivity: string;
  rating?: number;
  feedback?: string;
  unreadCount?: number;
}

interface Message {
  messageId: string;
  sessionId: string;
  senderType: string;
  senderId: string;
  senderName: string;
  content: string;
  messageType: string;
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  isRead: boolean;
  timestamp: string;
}

interface Agent {
  agentId: string;
  name: string;
  nickname?: string;
  avatarUrl?: string;
  status: string;
  currentConnections: number;
  maxConnections: number;
  totalServed: number;
  rating: string;
}

export default function AdminCustomerService() {
  const [waitingSessions, setWaitingSessions] = useState<Session[]>([]);
  const [activeSessions, setActiveSessions] = useState<Session[]>([]);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedSession, setSelectedSession] = useState<Session | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [lastMessageCount, setLastMessageCount] = useState(0);
  const [isPolling, setIsPolling] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const sessionPollingRef = useRef<NodeJS.Timeout | null>(null);

  const API_BASE_URL = 'http://localhost:8080/api';

  // 滚动到底部 - 优化版本
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // 只在消息数量增加时滚动
  useEffect(() => {
    if (messages.length > lastMessageCount) {
      scrollToBottom();
      setLastMessageCount(messages.length);
    }
  }, [messages.length, lastMessageCount, scrollToBottom]);

  // 获取会话消息 - 优化版本
  const fetchSessionMessages = useCallback(async (sessionId: string, silent = false) => {
    if (isPolling && silent) return; // 防止重复请求
    
    if (!silent) setIsPolling(true);
    
    try {
      const response = await fetch(`${API_BASE_URL}/admin/customer-service/admin-sessions/${sessionId}/messages`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken') || 'local_admin_token_123'}`
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        
        if (result.code === 200) {
          const newMessages = result.data || [];
          // 只有当消息发生变化时才更新
          setMessages(prevMessages => {
            if (prevMessages.length !== newMessages.length || 
                JSON.stringify(prevMessages) !== JSON.stringify(newMessages)) {
              return newMessages;
            }
            return prevMessages;
          });
        }
      }
    } catch (error) {
      console.error('获取会话消息失败:', error);
    } finally {
      if (!silent) setIsPolling(false);
    }
  }, [isPolling, API_BASE_URL]);

  // 发送客服回复 - 优化版本
  const sendAgentReply = useCallback(async () => {
    if (!newMessage.trim() || !selectedSession) return;

    const messageContent = newMessage;
    setNewMessage('');

    try {
      const response = await fetch(`${API_BASE_URL}/admin/customer-service/reply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken') || 'local_admin_token_123'}`
        },
        body: JSON.stringify({
          sessionId: selectedSession.sessionId,
          content: messageContent,
          type: 'TEXT'
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          // 发送成功后，延迟一点时间再刷新消息
          setTimeout(() => {
            fetchSessionMessages(selectedSession.sessionId, true);
          }, 300);
        } else {
          console.error('发送回复失败:', result.message);
          setNewMessage(messageContent); // 恢复消息内容
        }
      } else {
        console.error('发送回复请求失败:', response.status, response.statusText);
        setNewMessage(messageContent); // 恢复消息内容
      }
    } catch (error) {
      console.error('发送回复失败:', error);
      setNewMessage(messageContent); // 恢复消息内容
    }
  }, [newMessage, selectedSession, fetchSessionMessages, API_BASE_URL]);

  // 获取等待中的会话 - 优化版本
  const fetchWaitingSessions = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/admin/customer-service/sessions/waiting`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken') || 'local_admin_token_123'}`
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          setWaitingSessions(prev => {
            const newData = result.data || [];
            if (JSON.stringify(prev) !== JSON.stringify(newData)) {
              return newData;
            }
            return prev;
          });
        }
      }
    } catch (error) {
      console.error('获取等待会话失败:', error);
    }
  }, [API_BASE_URL]);

  // 获取活跃会话 - 优化版本
  const fetchActiveSessions = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/admin/customer-service/sessions/active`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken') || 'local_admin_token_123'}`
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          setActiveSessions(prev => {
            const newData = result.data || [];
            if (JSON.stringify(prev) !== JSON.stringify(newData)) {
              return newData;
            }
            return prev;
          });
        }
      }
    } catch (error) {
      console.error('获取活跃会话失败:', error);
    }
  }, [API_BASE_URL]);

  // 获取客服代表列表 - 优化版本
  const fetchAgents = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/admin/customer-service/agents`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken') || 'local_admin_token_123'}`
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          setAgents(prev => {
            const newData = result.data || [];
            if (JSON.stringify(prev) !== JSON.stringify(newData)) {
              return newData;
            }
            return prev;
          });
        }
      }
    } catch (error) {
      console.error('获取客服列表失败:', error);
    }
  }, [API_BASE_URL]);

  // 初始化数据
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        await Promise.all([
          fetchWaitingSessions(),
          fetchActiveSessions(),
          fetchAgents()
        ]);
      } catch (error) {
        setError('加载数据失败');
      } finally {
        setLoading(false);
      }
    };

    loadData();

    // 定时刷新会话列表，但频率降低以减少闪烁
    sessionPollingRef.current = setInterval(() => {
      Promise.all([
        fetchWaitingSessions(),
        fetchActiveSessions(),
        fetchAgents()
      ]);
    }, 15000); // 改为15秒，减少频率

    return () => {
      if (sessionPollingRef.current) {
        clearInterval(sessionPollingRef.current);
      }
    };
  }, [fetchWaitingSessions, fetchActiveSessions, fetchAgents]);

  // 选择会话 - 优化版本
  const selectSession = useCallback((session: Session) => {
    setSelectedSession(session);
    setMessages([]); // 清空之前的消息
    fetchSessionMessages(session.sessionId);
  }, [fetchSessionMessages]);

  // 智能刷新消息
  useEffect(() => {
    if (selectedSession) {
      // 立即获取一次消息
      fetchSessionMessages(selectedSession.sessionId);
      
      // 设置定时刷新，但只在非输入状态时刷新
      pollingIntervalRef.current = setInterval(() => {
        if (!isInputFocused) {
          fetchSessionMessages(selectedSession.sessionId, true);
        }
      }, 3000); // 3秒刷新一次

      return () => {
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
        }
      };
    }
  }, [selectedSession, isInputFocused, fetchSessionMessages]);

  // 处理输入框焦点 - 优化版本
  const handleInputFocus = useCallback(() => {
    setIsInputFocused(true);
  }, []);

  const handleInputBlur = useCallback(() => {
    setIsInputFocused(false);
  }, []);

  // 获取用户头像
  const getUserAvatar = (userId: number, username: string, avatarPath?: string) => {
    if (avatarPath) {
      return `http://localhost:8080${avatarPath}`;
    }
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(username)}&background=6366f1&color=ffffff&size=128`;
  };

  // 获取用户显示名称
  const getUserDisplayName = (username: string, nickname?: string) => {
    return nickname && nickname.trim() ? nickname : username;
  };

  // 格式化时间 - 优化版本
  const formatTime = useMemo(() => {
    return (timestamp: string) => {
      const now = new Date();
      const time = new Date(timestamp);
      const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
      
      if (diffInMinutes < 1) {
        return '刚刚';
      } else if (diffInMinutes < 60) {
        return `${diffInMinutes}分钟前`;
      } else if (diffInMinutes < 1440) {
        return `${Math.floor(diffInMinutes / 60)}小时前`;
      } else {
        return time.toLocaleString('zh-CN', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
    };
  }, []);

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'WAITING': return 'text-yellow-400';
      case 'CONNECTED': return 'text-green-400';
      case 'ENDED': return 'text-gray-400';
      case 'TIMEOUT': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'WAITING': return '等待中';
      case 'CONNECTED': return '进行中';
      case 'ENDED': return '已结束';
      case 'TIMEOUT': return '已超时';
      default: return '未知';
    }
  };

  // 处理键盘事件 - 优化版本
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendAgentReply();
    }
  }, [sendAgentReply]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-xl">加载中...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* 头部 */}
      <div className="bg-gray-800 border-b border-gray-700 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">客服中心</h1>
            <p className="text-gray-400 mt-1">实时在线客服，为用户提供专业的咨询服务</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-green-400">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm">在线服务中</span>
            </div>
          </div>
        </div>
      </div>

      <div className="flex h-[calc(100vh-120px)]">
        {/* 左侧会话列表 */}
        <div className="w-1/3 border-r border-gray-700 flex flex-col">
          {/* 统计卡片 */}
          <div className="p-4 bg-gray-800 border-b border-gray-700">
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-blue-600/20 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-blue-400">{activeSessions.length}</div>
                <div className="text-xs text-gray-400">进行中对话</div>
              </div>
              <div className="bg-yellow-600/20 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-yellow-400">{waitingSessions.length}</div>
                <div className="text-xs text-gray-400">等待回复</div>
              </div>
              <div className="bg-green-600/20 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-green-400">
                  {agents.filter(a => a.status === 'ONLINE').length}
                </div>
                <div className="text-xs text-gray-400">在线客服</div>
              </div>
            </div>
          </div>

          {/* 会话列表 */}
          <div className="flex-1 overflow-y-auto">
            {/* 等待中的会话 */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-800">等待中的会话</h2>
              <div className="space-y-3">
                {waitingSessions.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">暂无等待中的会话</p>
                ) : (
                  waitingSessions.map((session, index) => (
                    <div
                      key={session.sessionId}
                      onClick={() => selectSession(session)}
                      className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 animate-in slide-in-from-bottom duration-300 ${
                        selectedSession?.sessionId === session.sessionId
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-blue-300'
                      }`}
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <img
                            src={getUserAvatar(session.userId, session.username, session.avatarPath)}
                            alt={getUserDisplayName(session.username, session.nickname)}
                            className="w-10 h-10 rounded-full object-cover border-2 border-orange-200"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(getUserDisplayName(session.username, session.nickname))}&background=f97316&color=ffffff&size=128`;
                            }}
                          />
                          <div>
                            <div className="font-medium text-gray-900">{getUserDisplayName(session.username, session.nickname)}</div>
                            <div className="text-sm text-gray-500">
                              等待时间: {formatTime(session.startTime)}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                            等待中
                          </span>
                          {session.unreadCount && session.unreadCount > 0 && (
                            <span className="px-2 py-1 bg-red-500 text-white text-xs rounded-full">
                              {session.unreadCount}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* 活跃会话 */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-800">活跃会话</h2>
              <div className="space-y-3">
                {activeSessions.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">暂无活跃会话</p>
                ) : (
                  activeSessions.map((session, index) => (
                    <div
                      key={session.sessionId}
                      onClick={() => selectSession(session)}
                      className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 animate-in slide-in-from-bottom duration-300 ${
                        selectedSession?.sessionId === session.sessionId
                          ? 'border-green-500 bg-green-50'
                          : 'border-gray-200 hover:border-green-300'
                      }`}
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <img
                            src={getUserAvatar(session.userId, session.username, session.avatarPath)}
                            alt={getUserDisplayName(session.username, session.nickname)}
                            className="w-10 h-10 rounded-full object-cover border-2 border-green-200"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(getUserDisplayName(session.username, session.nickname))}&background=16a34a&color=ffffff&size=128`;
                            }}
                          />
                          <div>
                            <div className="font-medium text-gray-900">{getUserDisplayName(session.username, session.nickname)}</div>
                            <div className="text-sm text-gray-500">
                              客服: {session.agentName || '未分配'}
                            </div>
                            <div className="text-sm text-gray-500">
                              最后活动: {formatTime(session.lastActivity)}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                            进行中
                          </span>
                          {session.unreadCount && session.unreadCount > 0 && (
                            <span className="px-2 py-1 bg-red-500 text-white text-xs rounded-full">
                              {session.unreadCount}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 右侧聊天区域 */}
        <div className="flex-1 flex flex-col">
          {selectedSession ? (
            <>
              {/* 聊天头部 */}
              <div className="p-4 bg-gray-800 border-b border-gray-700">
                <div className="flex items-center space-x-3">
                  <img
                    src={getUserAvatar(selectedSession.userId, selectedSession.username, selectedSession.avatarPath)}
                    alt={getUserDisplayName(selectedSession.username, selectedSession.nickname)}
                    className="w-12 h-12 rounded-full object-cover border-2 border-blue-400"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(getUserDisplayName(selectedSession.username, selectedSession.nickname))}&background=6366f1&color=ffffff&size=128`;
                    }}
                  />
                  <div>
                    <h3 className="font-medium text-white">{getUserDisplayName(selectedSession.username, selectedSession.nickname)}</h3>
                    <p className="text-sm text-gray-400">
                      用户ID: {selectedSession.userId} • 状态: {getStatusText(selectedSession.status)}
                    </p>
                  </div>
                </div>
              </div>

              {/* 消息列表 */}
              <div className="flex-1 overflow-y-auto p-4">
                {messages.map((message, index) => (
                  <div
                    key={message.messageId}
                    className={`mb-4 animate-in slide-in-from-bottom duration-300`}
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    <div className={`flex ${message.senderType === 'USER' ? 'justify-start' : 'justify-end'}`}>
                      <div
                        className={`max-w-xs px-4 py-2 rounded-lg transition-all duration-200 hover:scale-105 ${
                          message.senderType === 'USER'
                            ? 'bg-gray-200 text-gray-800 shadow-lg'
                            : message.senderType === 'AGENT'
                            ? 'bg-blue-500 text-white shadow-lg'
                            : 'bg-yellow-500 text-white text-center shadow-lg'
                        }`}
                      >
                        <div className="text-sm">{message.content}</div>
                        <div className={`text-xs mt-1 opacity-70 ${
                          message.senderType === 'USER' ? 'text-gray-600' : 'text-white'
                        }`}>
                          {formatTime(message.timestamp)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>

              {/* 输入区域 */}
              <div className="p-4 bg-gray-800 border-t border-gray-700">
                <div className="flex space-x-2">
                  <input
                    ref={inputRef}
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    onFocus={handleInputFocus}
                    onBlur={handleInputBlur}
                    placeholder="输入回复消息..."
                    className="flex-1 px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                  />
                  <button
                    onClick={sendAgentReply}
                    disabled={!newMessage.trim()}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    发送
                  </button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center text-gray-400">
                <div className="text-6xl mb-4">💬</div>
                <h3 className="text-xl font-medium mb-2">选择一个会话开始聊天</h3>
                <p>从左侧选择用户会话，开始与用户对话</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 