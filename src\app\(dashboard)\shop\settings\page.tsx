'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

const API_BASE_URL = 'http://localhost:8080/api';

// 店铺设置接口定义
interface ShopSettings {
  shopId: string;
  shopName: string;
  shopLogo: string;
  shopBanner: string;
  description: string;
  contactPhone: string;
  contactEmail: string;
  businessHours: string;
  returnPolicy: string;
  shippingPolicy: string;
  categories: string[];
  tags: string[];
  socialLinks: {
    website?: string;
    weibo?: string;
    wechat?: string;
    qq?: string;
  };
}

export default function ShopSettings() {
  const router = useRouter();
  const { user } = useAuth();
  const [settings, setSettings] = useState<ShopSettings>({
    shopId: '',
    shopName: '',
    shopLogo: '',
    shopBanner: '',
    description: '',
    contactPhone: '',
    contactEmail: '',
    businessHours: '',
    returnPolicy: '',
    shippingPolicy: '',
    categories: [],
    tags: [],
    socialLinks: {}
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState<'basic' | 'policy' | 'social' | 'danger'>('basic');
  
  // 关闭店铺相关状态
  const [showCloseShopModal, setShowCloseShopModal] = useState(false);
  const [showDeleteAccountModal, setShowDeleteAccountModal] = useState(false);
  const [checkingOrders, setCheckingOrders] = useState(false);
  const [orderCheckResult, setOrderCheckResult] = useState<{
    pendingOrders: number;
    unrefundedOrders: number;
    canClose: boolean;
  } | null>(null);
  const [closingShop, setClosingShop] = useState(false);
  const [deletingAccount, setDeletingAccount] = useState(false);
  const [confirmText, setConfirmText] = useState('');

  // 获取店铺设置
  const fetchShopSettings = async () => {
    try {
      setLoading(true);
      // TODO: 替换为实际API调用
      // const response = await fetch('/api/shop/settings', {
      //   headers: { 'Authorization': `Bearer ${token}` }
      // });
      
      // 模拟数据
      const mockSettings: ShopSettings = {
        shopId: 'shop_123',
        shopName: '键帽工坊',
        shopLogo: '/images/shop-logo.jpg',
        shopBanner: '/images/shop-banner.jpg',
        description: '专业键帽定制店铺，提供高品质的机械键盘键帽产品',
        contactPhone: '***********',
        contactEmail: '<EMAIL>',
        businessHours: '周一至周五 9:00-18:00',
        returnPolicy: '7天无理由退货，15天质量问题免费换货',
        shippingPolicy: '全国包邮，48小时内发货',
        categories: ['键帽套装', '个性键帽', '主题键帽'],
        tags: ['机械键盘', '键帽', '定制', '个性化'],
        socialLinks: {
          website: 'https://keycap-shop.com',
          weibo: '@键帽工坊',
          wechat: 'keycap_shop',
          qq: '123456789'
        }
      };
      
      await new Promise(resolve => setTimeout(resolve, 500));
      setSettings(mockSettings);
    } catch (error) {
      console.error('获取店铺设置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 保存设置
  const handleSaveSettings = async () => {
    try {
      setSaving(true);
      // TODO: 实际API调用
      console.log('保存店铺设置:', settings);
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('设置保存成功！');
    } catch (error) {
      console.error('保存设置失败:', error);
      alert('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  // 检查订单状态
  const checkOrderStatus = async () => {
    try {
      setCheckingOrders(true);
      setOrderCheckResult(null); // 重置之前的结果
      const token = localStorage.getItem('token');
      
      // 调用实际API
      const response = await fetch(`${API_BASE_URL}/shop/orders/check-status`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const result = await response.json();
        setOrderCheckResult(result.data);
      } else {
        // 模拟检查结果
        await new Promise(resolve => setTimeout(resolve, 1500));
        // 随机模拟不同情况用于演示
        const hasIssues = Math.random() > 0.5;
        setOrderCheckResult({
          pendingOrders: hasIssues ? 2 : 0,
          unrefundedOrders: hasIssues ? 1 : 0,
          canClose: !hasIssues
        });
      }
    } catch (error) {
      console.error('检查订单状态失败:', error);
      // 发生错误时显示有问题的情况
      setOrderCheckResult({
        pendingOrders: 0,
        unrefundedOrders: 0,
        canClose: false
      });
    } finally {
      setCheckingOrders(false);
    }
  };

  // 关闭店铺
  const handleCloseShop = async () => {
    try {
      setClosingShop(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch(`${API_BASE_URL}/shop/close`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        alert('店铺已成功关闭！');
        setShowCloseShopModal(false);
        router.push('/dashboard');
      } else {
        throw new Error('关闭店铺失败');
      }
    } catch (error) {
      console.error('关闭店铺失败:', error);
      alert('关闭店铺失败，请重试');
    } finally {
      setClosingShop(false);
    }
  };

  // 注销账号
  const handleDeleteAccount = async () => {
    if (confirmText !== '删除我的账号') {
      alert('请正确输入确认文字');
      return;
    }

    try {
      setDeletingAccount(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch(`${API_BASE_URL}/user/delete`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        alert('账号已成功注销！');
        localStorage.clear();
        router.push('/');
      } else {
        throw new Error('注销账号失败');
      }
    } catch (error) {
      console.error('注销账号失败:', error);
      alert('注销账号失败，请重试');
    } finally {
      setDeletingAccount(false);
    }
  };

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }
    fetchShopSettings();
  }, [user, router]);

  // 重置表单状态当标签页切换时
  useEffect(() => {
    if (activeTab !== 'danger') {
      setOrderCheckResult(null);
      setConfirmText('');
      setShowCloseShopModal(false);
      setShowDeleteAccountModal(false);
    }
  }, [activeTab]);

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* 顶部导航 */}
      <nav className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-sm mr-3">
                K
              </Link>
              <h1 className="text-xl font-bold text-white">店铺设置</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/notifications"
                className="text-gray-300 hover:text-white transition-colors"
              >
                <span className="text-xl">🔔</span>
              </Link>
              <span className="text-gray-300">店主：{user.nickname || user.username}</span>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* 侧边栏 */}
        <aside className="w-64 bg-gray-800 min-h-screen pt-4">
          <nav className="px-4 space-y-2">
            <Link href="/dashboard" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🏠</span>
              返回首页
            </Link>
            <Link href="/shop/my-store" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🏪</span>
              我的店铺
            </Link>
            <Link href="/shop/products" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📦</span>
              商品管理
            </Link>
            <Link href="/shop/orders" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📋</span>
              订单管理
            </Link>
            <Link href="/shop/settings" className="flex items-center px-4 py-2 text-white bg-blue-600 rounded-lg">
              <span className="mr-3">⚙️</span>
              店铺设置
            </Link>
          </nav>
        </aside>

        {/* 主内容区 */}
        <main className="flex-1 p-6">
          {/* 设置标签页 */}
          <div className="bg-gray-800 rounded-2xl p-6 mb-6">
            <div className="flex space-x-2 mb-6">
              {[
                { key: 'basic', label: '基本设置', icon: '🏪' },
                { key: 'policy', label: '政策设置', icon: '📋' },
                { key: 'social', label: '社交媒体', icon: '🌐' },
                { key: 'danger', label: '危险操作', icon: '⚠️' }
              ].map(tab => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                    activeTab === tab.key
                      ? (tab.key === 'danger' ? 'bg-red-600 text-white' : 'bg-blue-600 text-white')
                      : (tab.key === 'danger' ? 'bg-red-900 text-red-300 hover:bg-red-800' : 'bg-gray-700 text-gray-300 hover:bg-gray-600')
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className="ml-3 text-gray-300">加载中...</span>
              </div>
            ) : (
              <div>
                {/* 基本设置 */}
                {activeTab === 'basic' && (
                  <div className="space-y-6">
                    <h3 className="text-xl font-bold text-white mb-4">基本信息</h3>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">店铺名称</label>
                        <input
                          type="text"
                          value={settings.shopName}
                          onChange={(e) => setSettings(prev => ({ ...prev, shopName: e.target.value }))}
                          className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="请输入店铺名称"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">联系邮箱</label>
                        <input
                          type="email"
                          value={settings.contactEmail}
                          onChange={(e) => setSettings(prev => ({ ...prev, contactEmail: e.target.value }))}
                          className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="请输入联系邮箱"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">联系电话</label>
                        <input
                          type="tel"
                          value={settings.contactPhone}
                          onChange={(e) => setSettings(prev => ({ ...prev, contactPhone: e.target.value }))}
                          className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="请输入联系电话"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">营业时间</label>
                        <input
                          type="text"
                          value={settings.businessHours}
                          onChange={(e) => setSettings(prev => ({ ...prev, businessHours: e.target.value }))}
                          className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="例：周一至周五 9:00-18:00"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">店铺描述</label>
                      <textarea
                        value={settings.description}
                        onChange={(e) => setSettings(prev => ({ ...prev, description: e.target.value }))}
                        rows={4}
                        className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="请输入店铺描述"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">店铺标签</label>
                      <div className="flex flex-wrap gap-2 mb-3">
                        {settings.tags.map((tag, index) => (
                          <span key={index} className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm flex items-center">
                            {tag}
                            <button
                              onClick={() => setSettings(prev => ({ 
                                ...prev, 
                                tags: prev.tags.filter((_, i) => i !== index) 
                              }))}
                              className="ml-2 text-blue-200 hover:text-white"
                            >
                              ×
                            </button>
                          </span>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <input
                          type="text"
                          placeholder="添加标签"
                          className="flex-1 px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              const input = e.currentTarget;
                              const value = input.value.trim();
                              if (value && !settings.tags.includes(value)) {
                                setSettings(prev => ({ ...prev, tags: [...prev.tags, value] }));
                                input.value = '';
                              }
                            }
                          }}
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* 政策设置 */}
                {activeTab === 'policy' && (
                  <div className="space-y-6">
                    <h3 className="text-xl font-bold text-white mb-4">政策设置</h3>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">退货政策</label>
                      <textarea
                        value={settings.returnPolicy}
                        onChange={(e) => setSettings(prev => ({ ...prev, returnPolicy: e.target.value }))}
                        rows={4}
                        className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="请输入退货政策"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">配送政策</label>
                      <textarea
                        value={settings.shippingPolicy}
                        onChange={(e) => setSettings(prev => ({ ...prev, shippingPolicy: e.target.value }))}
                        rows={4}
                        className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="请输入配送政策"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">商品分类</label>
                      <div className="flex flex-wrap gap-2 mb-3">
                        {settings.categories.map((category, index) => (
                          <span key={index} className="bg-green-500 text-white px-3 py-1 rounded-full text-sm flex items-center">
                            {category}
                            <button
                              onClick={() => setSettings(prev => ({ 
                                ...prev, 
                                categories: prev.categories.filter((_, i) => i !== index) 
                              }))}
                              className="ml-2 text-green-200 hover:text-white"
                            >
                              ×
                            </button>
                          </span>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <input
                          type="text"
                          placeholder="添加分类"
                          className="flex-1 px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              const input = e.currentTarget;
                              const value = input.value.trim();
                              if (value && !settings.categories.includes(value)) {
                                setSettings(prev => ({ ...prev, categories: [...prev.categories, value] }));
                                input.value = '';
                              }
                            }
                          }}
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* 社交媒体设置 */}
                {activeTab === 'social' && (
                  <div className="space-y-6">
                    <h3 className="text-xl font-bold text-white mb-4">社交媒体</h3>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">官方网站</label>
                        <input
                          type="url"
                          value={settings.socialLinks.website || ''}
                          onChange={(e) => setSettings(prev => ({ 
                            ...prev, 
                            socialLinks: { ...prev.socialLinks, website: e.target.value }
                          }))}
                          className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="https://your-website.com"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">微博账号</label>
                        <input
                          type="text"
                          value={settings.socialLinks.weibo || ''}
                          onChange={(e) => setSettings(prev => ({ 
                            ...prev, 
                            socialLinks: { ...prev.socialLinks, weibo: e.target.value }
                          }))}
                          className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="@您的微博账号"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">微信账号</label>
                        <input
                          type="text"
                          value={settings.socialLinks.wechat || ''}
                          onChange={(e) => setSettings(prev => ({ 
                            ...prev, 
                            socialLinks: { ...prev.socialLinks, wechat: e.target.value }
                          }))}
                          className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="微信号"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">QQ号码</label>
                        <input
                          type="text"
                          value={settings.socialLinks.qq || ''}
                          onChange={(e) => setSettings(prev => ({ 
                            ...prev, 
                            socialLinks: { ...prev.socialLinks, qq: e.target.value }
                          }))}
                          className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="QQ号码"
                        />
                      </div>
                    </div>
                  </div>
                )}

                                 {/* 关闭店铺 */}
                 {activeTab === 'danger' && (
                   <div className="space-y-6">
                     <h3 className="text-xl font-bold text-white mb-4">危险操作</h3>
                     
                     {/* 订单状态检查 */}
                     <div className="bg-gray-700 border border-gray-600 rounded-lg p-6">
                       <h4 className="text-lg font-semibold text-white mb-4">1. 检查订单状态</h4>
                       <p className="text-gray-300 mb-4">在关闭店铺前，需要检查是否有未处理的订单或未退款的订单。</p>
                       
                       <button
                         onClick={checkOrderStatus}
                         disabled={checkingOrders}
                         className="w-full px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center justify-center"
                       >
                         {checkingOrders ? (
                           <>
                             <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                             检查中...
                           </>
                         ) : (
                           <>
                             🔍 检查订单状态
                           </>
                         )}
                       </button>

                       {orderCheckResult && (
                         <div className="mt-4 p-4 bg-gray-800 border border-gray-600 rounded-lg">
                           <div className="space-y-2">
                             <div className="flex justify-between">
                               <span className="text-gray-300">未发货订单:</span>
                               <span className={orderCheckResult.pendingOrders > 0 ? 'text-red-400' : 'text-green-400'}>
                                 {orderCheckResult.pendingOrders} 个
                               </span>
                             </div>
                             <div className="flex justify-between">
                               <span className="text-gray-300">未退款订单:</span>
                               <span className={orderCheckResult.unrefundedOrders > 0 ? 'text-red-400' : 'text-green-400'}>
                                 {orderCheckResult.unrefundedOrders} 个
                               </span>
                             </div>
                           </div>
                           
                           {orderCheckResult.canClose ? (
                             <div className="mt-4 p-3 bg-green-900 border border-green-700 rounded-lg">
                               <p className="text-green-300">✅ 店铺可以安全关闭</p>
                             </div>
                           ) : (
                             <div className="mt-4 p-3 bg-red-900 border border-red-700 rounded-lg">
                               <p className="text-red-300">❌ 店铺暂时无法关闭</p>
                               <p className="text-red-200 text-sm mt-1">
                                 请先处理完所有未发货订单和未退款订单
                               </p>
                             </div>
                           )}
                         </div>
                       )}
                     </div>

                     {/* 关闭店铺 */}
                     <div className="bg-gray-700 border border-gray-600 rounded-lg p-6">
                       <h4 className="text-lg font-semibold text-white mb-4">2. 关闭店铺</h4>
                       <p className="text-gray-300 mb-4">关闭店铺后，将无法再接收新订单，现有商品将下架。</p>
                       
                       <button
                         onClick={() => setShowCloseShopModal(true)}
                         disabled={!orderCheckResult?.canClose}
                         className="w-full px-4 py-3 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                       >
                         {orderCheckResult?.canClose ? '🔒 关闭店铺' : '⚠️ 需要先检查订单状态'}
                       </button>
                     </div>

                     {/* 注销账号 */}
                     <div className="bg-red-900 border border-red-700 rounded-lg p-6">
                       <h4 className="text-lg font-semibold text-white mb-4">3. 注销账号</h4>
                       <p className="text-red-200 mb-4">
                         ⚠️ 危险操作：注销账号将删除您的所有数据，包括店铺、订单、设计作品等，且无法恢复！
                       </p>
                       
                       {orderCheckResult && !orderCheckResult.canClose && (
                         <div className="mb-4 p-3 bg-yellow-900 border border-yellow-700 rounded-lg">
                           <p className="text-yellow-200 text-sm">
                             ⚠️ 注意：您还有未处理的订单，此操作将无法撤销！
                           </p>
                         </div>
                       )}
                       
                       <button
                         onClick={() => setShowDeleteAccountModal(true)}
                         className="w-full px-4 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                       >
                         🗑️ 注销账号
                       </button>
                     </div>
                   </div>
                 )}

                {/* 保存按钮 */}
                <div className="flex justify-end pt-6 border-t border-gray-700">
                  <button
                    onClick={handleSaveSettings}
                    disabled={saving}
                    className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-8 py-3 rounded-lg transition-colors flex items-center"
                  >
                    {saving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        保存中...
                      </>
                    ) : (
                      <>
                        💾 保存设置
                      </>
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>

             {/* 关闭店铺确认对话框 */}
       {showCloseShopModal && (
         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
           <div className="bg-white p-8 rounded-lg max-w-md w-full mx-4">
             <div className="text-center mb-6">
               <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                 <span className="text-3xl">🔒</span>
               </div>
               <h2 className="text-2xl font-bold text-gray-900 mb-2">确认关闭店铺</h2>
             </div>
             
             <div className="space-y-4 mb-6">
               <p className="text-gray-700">关闭店铺后将发生以下变化：</p>
               <ul className="list-disc list-inside space-y-2 text-gray-600">
                 <li>无法接收新的订单</li>
                 <li>所有商品将自动下架</li>
                 <li>店铺页面将显示为"已关闭"</li>
                 <li>可以随时重新开启店铺</li>
               </ul>
               <p className="text-sm text-gray-500 bg-gray-100 p-3 rounded">
                 💡 提示：关闭店铺不会删除您的数据，您可以随时重新开启。
               </p>
             </div>
             
             <div className="flex justify-end space-x-3">
               <button
                 onClick={() => setShowCloseShopModal(false)}
                 className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
               >
                 取消
               </button>
               <button
                 onClick={handleCloseShop}
                 disabled={closingShop}
                 className="bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg transition-colors flex items-center"
               >
                 {closingShop ? (
                   <>
                     <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                     关闭中...
                   </>
                 ) : (
                   '确认关闭'
                 )}
               </button>
             </div>
           </div>
         </div>
       )}

       {/* 注销账号确认对话框 */}
       {showDeleteAccountModal && (
         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
           <div className="bg-white p-8 rounded-lg max-w-md w-full mx-4">
             <div className="text-center mb-6">
               <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                 <span className="text-3xl">⚠️</span>
               </div>
               <h2 className="text-2xl font-bold text-red-600 mb-2">确认注销账号</h2>
             </div>
             
             <div className="space-y-4 mb-6">
               <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                 <p className="text-red-800 font-medium mb-2">⚠️ 危险操作警告</p>
                 <p className="text-red-700 text-sm">
                   注销账号将永久删除以下所有数据，且无法恢复：
                 </p>
               </div>
               
               <ul className="list-disc list-inside space-y-2 text-gray-700">
                 <li>店铺信息和设置</li>
                 <li>所有商品和订单记录</li>
                 <li>设计作品和素材</li>
                 <li>社区互动记录</li>
                 <li>账户所有相关数据</li>
               </ul>
               
               {orderCheckResult && !orderCheckResult.canClose && (
                 <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                   <p className="text-yellow-800 text-sm">
                     ⚠️ 您还有 {orderCheckResult.pendingOrders} 个未发货订单和 {orderCheckResult.unrefundedOrders} 个未退款订单，
                     注销账号后这些订单将无法处理！
                   </p>
                 </div>
               )}
               
               <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                 <p className="text-gray-700 text-sm mb-3">
                   请输入 <strong>"删除我的账号"</strong> 来确认：
                 </p>
                 <input
                   type="text"
                   value={confirmText}
                   onChange={(e) => setConfirmText(e.target.value)}
                   placeholder="请输入：删除我的账号"
                   className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-red-500"
                 />
               </div>
             </div>
             
             <div className="flex justify-end space-x-3">
               <button
                 onClick={() => {
                   setShowDeleteAccountModal(false);
                   setConfirmText('');
                 }}
                 className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
               >
                 取消
               </button>
               <button
                 onClick={handleDeleteAccount}
                 disabled={deletingAccount || confirmText !== '删除我的账号'}
                 className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg transition-colors flex items-center"
               >
                 {deletingAccount ? (
                   <>
                     <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                     注销中...
                   </>
                 ) : (
                   '确认注销'
                 )}
               </button>
             </div>
           </div>
         </div>
       )}
    </div>
  );
} 