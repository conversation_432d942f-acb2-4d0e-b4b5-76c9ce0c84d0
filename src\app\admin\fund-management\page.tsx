'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdmin } from '../../../contexts/AdminContext';
import Link from 'next/link';

export default function FundManagement() {
  const { admin, isLoading: adminLoading } = useAdmin();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [statistics, setStatistics] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!adminLoading) {
      if (admin) {
        fetchStatistics();
      } else {
        router.push('/admin/login');
      }
    }
  }, [admin, adminLoading, router]);

  const fetchStatistics = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch('http://localhost:8080/api/admin/fund/statistics', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        setStatistics(result.data);
      }
    } catch (error) {
      console.error('获取资金统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (adminLoading || (!admin && loading)) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-xl">加载中...</div>
      </div>
    );
  }

  if (!admin) {
    return null; // 会被useEffect重定向到登录页
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <nav className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-sm mr-3">
                ←
              </Link>
              <h1 className="text-xl font-bold text-white">资金管理</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/admin/settings/payment-config" className="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition-colors">
                💳 支付设置
              </Link>
              <Link href="/admin/dashboard" className="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition-colors">
                📊 仪表盘
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto p-6">
        {/* 统计卡片 */}
        {statistics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">总余额</p>
                  <p className="text-2xl font-bold">¥{statistics.totalBalance?.toFixed(2) || '0.00'}</p>
                </div>
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">💰</span>
                </div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-green-600 to-green-700 rounded-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">可用余额</p>
                  <p className="text-2xl font-bold">¥{statistics.availableBalance?.toFixed(2) || '0.00'}</p>
                </div>
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">✅</span>
                </div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-yellow-600 to-yellow-700 rounded-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-100 text-sm">冻结余额</p>
                  <p className="text-2xl font-bold">¥{statistics.frozenBalance?.toFixed(2) || '0.00'}</p>
                </div>
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">🔒</span>
                </div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm">今日收入</p>
                  <p className="text-2xl font-bold">¥{statistics.todayIncome?.toFixed(2) || '0.00'}</p>
                </div>
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">📈</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 功能区域 */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="text-center">
            <div className="text-6xl mb-4">💰</div>
            <h2 className="text-2xl font-bold text-white mb-4">资金管理中心</h2>
            <p className="text-gray-400 mb-8">管理平台资金、查看收支明细、处理提现申请</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-700 rounded-lg p-6">
                <div className="text-blue-400 text-3xl mb-3">📊</div>
                <h3 className="text-white font-bold mb-2">资金统计</h3>
                <p className="text-gray-400 text-sm mb-4">查看总收入、支出和余额</p>
                {statistics && (
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">总收入:</span>
                      <span className="text-green-400">¥{statistics.totalIncome?.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">总支出:</span>
                      <span className="text-red-400">¥{statistics.totalWithdraw?.toFixed(2)}</span>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="bg-gray-700 rounded-lg p-6">
                <div className="text-green-400 text-3xl mb-3">🏦</div>
                <h3 className="text-white font-bold mb-2">银行卡管理</h3>
                <p className="text-gray-400 text-sm mb-4">管理提现银行卡信息</p>
                <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded transition-colors">
                  管理银行卡
                </button>
              </div>
              
              <div className="bg-gray-700 rounded-lg p-6">
                <div className="text-purple-400 text-3xl mb-3">💸</div>
                <h3 className="text-white font-bold mb-2">提现管理</h3>
                <p className="text-gray-400 text-sm mb-4">处理提现申请和记录</p>
                <button className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded transition-colors">
                  提现管理
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 最近交易 */}
        <div className="mt-8 bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h3 className="text-white font-bold text-lg mb-4">最近交易记录</h3>
          <div className="text-center py-8">
            <div className="text-gray-400 text-lg mb-2">暂无交易记录</div>
            <p className="text-gray-500">交易记录将在这里显示</p>
          </div>
        </div>
      </div>
    </div>
  );
} 
        {/* 最近交易 */}
        <div className="mt-8 bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h3 className="text-white font-bold text-lg mb-4">最近交易记录</h3>
          <div className="text-center py-8">
            <div className="text-gray-400 text-lg mb-2">暂无交易记录</div>
            <p className="text-gray-500">交易记录将在这里显示</p>
          </div>
        </div>
      </div>
    </div>
  );
} 