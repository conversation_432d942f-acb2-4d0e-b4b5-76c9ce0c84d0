'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';

export default function UploadPage() {
  const [files, setFiles] = useState<File[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<{ name: string; preview: string }[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setFiles(prev => [...prev, ...acceptedFiles]);
    
    // 创建预览
    const newUploadedFiles = acceptedFiles.map(file => ({
      name: file.name,
      preview: URL.createObjectURL(file)
    }));
    
    setUploadedFiles(prev => [...prev, ...newUploadedFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({ 
    onDrop, 
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.svg']
    },
    multiple: true 
  });

  const handleUpload = async () => {
    if (files.length === 0) return;
    
    setUploading(true);
    
    // 模拟上传操作
    setTimeout(() => {
      setUploading(false);
      setUploadSuccess(true);
      
      setTimeout(() => {
        setUploadSuccess(false);
      }, 3000);
    }, 1500);
  };

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
    
    // 释放预览URL
    URL.revokeObjectURL(uploadedFiles[index].preview);
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div style={{ 
        backgroundColor: 'white', 
        padding: '1rem 1.5rem',
        borderBottom: '1px solid #e5e7eb'
      }}>
        <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>上传素材</h1>
      </div>
      
      <div style={{ 
        maxWidth: '1200px', 
        margin: '0 auto', 
        padding: '2rem 1rem' 
      }}>
        {/* 拖放区域 */}
        <div 
          {...getRootProps()} 
          style={{ 
            border: isDragActive ? '2px dashed #4f46e5' : '2px dashed #d1d5db',
            borderRadius: '0.75rem',
            padding: '3rem 2rem',
            textAlign: 'center',
            backgroundColor: isDragActive ? 'rgba(79, 70, 229, 0.05)' : 'white',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            marginBottom: '2rem'
          }}
        >
          <input {...getInputProps()} />
          
          <div style={{ marginBottom: '1rem' }}>
            <svg 
              width="64" 
              height="64" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="1" 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              style={{ 
                margin: '0 auto',
                color: isDragActive ? '#4f46e5' : '#9ca3af'
              }}
            >
              <path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"></path>
              <path d="M12 12v9"></path>
              <path d="m16 16-4-4-4 4"></path>
            </svg>
          </div>
          
          <p style={{ 
            fontSize: '1.125rem', 
            fontWeight: '500', 
            color: isDragActive ? '#4f46e5' : '#374151',
            marginBottom: '0.5rem'
          }}>
            {isDragActive ? '放开鼠标上传文件' : '拖拽文件到这里上传'}
          </p>
          
          <p style={{ fontSize: '0.875rem', color: '#6b7280' }}>
            或点击此处选择文件
          </p>
          
          <p style={{ 
            fontSize: '0.75rem', 
            color: '#9ca3af',
            marginTop: '1rem'
          }}>
            支持 JPG, PNG, GIF, SVG 格式，单个文件不超过10MB
          </p>
        </div>
        
        {/* 文件预览区域 */}
        {uploadedFiles.length > 0 && (
          <div style={{ marginBottom: '2rem' }}>
            <h2 style={{ 
              fontSize: '1.125rem', 
              fontWeight: '600',
              marginBottom: '1rem' 
            }}>
              已选择的文件 ({uploadedFiles.length})
            </h2>
            
            <div style={{ 
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(180px, 1fr))',
              gap: '1rem'
            }}>
              {uploadedFiles.map((file, index) => (
                <div 
                  key={`${file.name}-${index}`}
                  style={{ 
                    position: 'relative',
                    backgroundColor: 'white',
                    borderRadius: '0.5rem',
                    overflow: 'hidden',
                    boxShadow: '0 1px 3px 0 rgba(0,0,0,0.1)',
                    aspectRatio: '1',
                    border: '1px solid #e5e7eb'
                  }}
                >
                  <img 
                    src={file.preview} 
                    alt={file.name}
                    style={{ 
                      width: '100%',
                      height: '140px',
                      objectFit: 'cover'
                    }}
                  />
                  
                  <div style={{ 
                    padding: '0.75rem',
                    borderTop: '1px solid #e5e7eb'
                  }}>
                    <p style={{ 
                      fontSize: '0.75rem', 
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}>
                      {file.name}
                    </p>
                  </div>
                  
                  {/* 删除按钮 */}
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFile(index);
                    }}
                    style={{
                      position: 'absolute',
                      top: '0.25rem',
                      right: '0.25rem',
                      width: '1.5rem',
                      height: '1.5rem',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(0, 0, 0, 0.5)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '0.875rem',
                      cursor: 'pointer',
                      border: 'none'
                    }}
                  >
                    ✕
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* 上传按钮 */}
        {files.length > 0 && (
          <div style={{ 
            display: 'flex',
            justifyContent: 'flex-end',
            marginTop: '1rem'
          }}>
            <button
              onClick={handleUpload}
              disabled={uploading}
              style={{ 
                backgroundColor: '#4f46e5',
                color: 'white',
                padding: '0.75rem 1.5rem',
                borderRadius: '0.375rem',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                cursor: uploading ? 'not-allowed' : 'pointer',
                opacity: uploading ? 0.7 : 1
              }}
            >
              {uploading ? (
                <>
                  <svg 
                    width="20" 
                    height="20" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    style={{ animation: 'spin 1s linear infinite' }}
                  >
                    <circle cx="12" cy="12" r="10" strokeWidth="4" stroke="#ffffff" strokeDasharray="30 65" />
                  </svg>
                  上传中...
                </>
              ) : uploadSuccess ? (
                <>
                  <svg 
                    width="20" 
                    height="20" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  上传成功
                </>
              ) : (
                <>
                  <svg 
                    width="20" 
                    height="20" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="17 8 12 3 7 8"></polyline>
                    <line x1="12" y1="3" x2="12" y2="15"></line>
                  </svg>
                  上传所有文件
                </>
              )}
            </button>
          </div>
        )}
        
        {/* 上传历史记录 */}
        <div style={{ marginTop: '3rem' }}>
          <h2 style={{ 
            fontSize: '1.25rem', 
            fontWeight: '600',
            marginBottom: '1rem',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <span>上传历史</span>
          </h2>
          
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f9fafb' }}>
                <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontWeight: '500', fontSize: '0.875rem', color: '#6b7280', borderBottom: '1px solid #e5e7eb' }}>文件名称</th>
                <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontWeight: '500', fontSize: '0.875rem', color: '#6b7280', borderBottom: '1px solid #e5e7eb' }}>类型</th>
                <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontWeight: '500', fontSize: '0.875rem', color: '#6b7280', borderBottom: '1px solid #e5e7eb' }}>大小</th>
                <th style={{ padding: '0.75rem 1rem', textAlign: 'left', fontWeight: '500', fontSize: '0.875rem', color: '#6b7280', borderBottom: '1px solid #e5e7eb' }}>上传时间</th>
                <th style={{ padding: '0.75rem 1rem', textAlign: 'right', fontWeight: '500', fontSize: '0.875rem', color: '#6b7280', borderBottom: '1px solid #e5e7eb' }}>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td style={{ padding: '1rem', borderBottom: '1px solid #e5e7eb', fontSize: '0.875rem' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                    <div style={{ 
                      width: '2.5rem', 
                      height: '2.5rem', 
                      borderRadius: '0.25rem',
                      overflow: 'hidden',
                      backgroundColor: '#f3f4f6',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ color: '#9ca3af' }}>
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <circle cx="8.5" cy="8.5" r="1.5"></circle>
                        <polyline points="21 15 16 10 5 21"></polyline>
                      </svg>
                    </div>
                    <span>background-image.jpg</span>
                  </div>
                </td>
                <td style={{ padding: '1rem', borderBottom: '1px solid #e5e7eb', fontSize: '0.875rem' }}>JPG</td>
                <td style={{ padding: '1rem', borderBottom: '1px solid #e5e7eb', fontSize: '0.875rem' }}>2.4 MB</td>
                <td style={{ padding: '1rem', borderBottom: '1px solid #e5e7eb', fontSize: '0.875rem' }}>2023-06-10</td>
                <td style={{ padding: '1rem', borderBottom: '1px solid #e5e7eb', textAlign: 'right' }}>
                  <button style={{ 
                    color: '#4f46e5', 
                    fontWeight: '500', 
                    fontSize: '0.875rem',
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer'
                  }}>
                    使用
                  </button>
                </td>
              </tr>
              <tr>
                <td style={{ padding: '1rem', borderBottom: '1px solid #e5e7eb', fontSize: '0.875rem' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                    <div style={{ 
                      width: '2.5rem', 
                      height: '2.5rem', 
                      borderRadius: '0.25rem',
                      overflow: 'hidden',
                      backgroundColor: '#f3f4f6',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ color: '#9ca3af' }}>
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <circle cx="8.5" cy="8.5" r="1.5"></circle>
                        <polyline points="21 15 16 10 5 21"></polyline>
                      </svg>
                    </div>
                    <span>pattern-design.png</span>
                  </div>
                </td>
                <td style={{ padding: '1rem', borderBottom: '1px solid #e5e7eb', fontSize: '0.875rem' }}>PNG</td>
                <td style={{ padding: '1rem', borderBottom: '1px solid #e5e7eb', fontSize: '0.875rem' }}>1.8 MB</td>
                <td style={{ padding: '1rem', borderBottom: '1px solid #e5e7eb', fontSize: '0.875rem' }}>2023-06-08</td>
                <td style={{ padding: '1rem', borderBottom: '1px solid #e5e7eb', textAlign: 'right' }}>
                  <button style={{ 
                    color: '#4f46e5', 
                    fontWeight: '500', 
                    fontSize: '0.875rem',
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer'
                  }}>
                    使用
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
} 