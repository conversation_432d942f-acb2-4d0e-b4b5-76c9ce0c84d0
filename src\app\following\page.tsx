'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';

interface UserProfile {
  userId: string;
  username: string;
  nickname?: string;
  avatarPath: string;
  userTitle?: string;
  designAdvantage: string;
  bio: string;
  postCount: number;
  likeCount: number;
  followersCount: number;
  followingCount: number;
  hasShop: boolean;
  shopName: string;
  isFollowed?: boolean;
  createTime?: string;
}

interface FollowingResponse {
  content: UserProfile[];
  totalElements: number;
  totalPages: number;
  number: number;
  size: number;
  first: boolean;
  last: boolean;
}

export default function FollowingPage() {
  const [followingUsers, setFollowingUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);

  // 获取关注列表
  const loadFollowingUsers = async (page = 0) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      if (!token) {
        alert('请先登录');
        return;
      }

      const response = await fetch(`http://localhost:8080/api/users/following?page=${page}&size=20`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          const data: FollowingResponse = result.data;
          setFollowingUsers(data.content || []);
          setTotalPages(data.totalPages || 0);
          setTotalElements(data.totalElements || 0);
          setCurrentPage(data.number || 0);
        } else {
          console.error('获取关注列表失败:', result.message);
        }
      }
    } catch (error) {
      console.error('获取关注列表异常:', error);
    } finally {
      setLoading(false);
    }
  };

  // 取消关注
  const handleUnfollow = async (userId: string, username: string) => {
    if (!confirm(`确定要取消关注 ${username} 吗？`)) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8080/api/users/${userId}/follow`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          // 从列表中移除该用户
          setFollowingUsers(prev => prev.filter(user => user.userId !== userId));
          setTotalElements(prev => prev - 1);
          alert('取消关注成功');
        }
      }
    } catch (error) {
      console.error('取消关注失败:', error);
      alert('取消关注失败，请重试');
    }
  };

  useEffect(() => {
    loadFollowingUsers();
  }, []);

  const handlePageChange = (newPage: number) => {
    loadFollowingUsers(newPage);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '未知';
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <header className="bg-white border-b border-gray-200 px-6 py-4 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link
              href="/dashboard"
              className="inline-flex items-center space-x-2 p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
            >
              <span>← 返回</span>
            </Link>
            <div>
              <h1 className="text-xl font-bold text-gray-900">我的关注</h1>
              <p className="text-sm text-gray-600">共关注了 {totalElements} 位设计师</p>
            </div>
          </div>
          
          <Link 
            href="/community"
            className="text-pink-600 hover:text-pink-700 font-medium"
          >
            发现更多设计师
          </Link>
        </div>
      </header>

      <div className="max-w-6xl mx-auto p-6">
        {loading ? (
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <div className="text-4xl mb-4">⏳</div>
              <p className="text-gray-500">加载中...</p>
            </div>
          </div>
        ) : followingUsers.length === 0 ? (
          <div className="text-center py-20">
            <div className="text-6xl mb-4">👥</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">还没有关注任何人</h3>
            <p className="text-gray-500 mb-6">去社区发现优秀的设计师吧！</p>
            <Link 
              href="/community"
              className="inline-flex items-center space-x-2 bg-pink-600 text-white px-6 py-3 rounded-lg hover:bg-pink-700 transition-colors"
            >
              <span>探索社区</span>
              <span>→</span>
            </Link>
          </div>
        ) : (
          <>
            {/* 关注列表 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {followingUsers.map((user) => (
                <div key={user.userId} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                  {/* 用户头像和基本信息 */}
                  <div className="text-center mb-4">
                    <div className="w-16 h-16 rounded-full overflow-hidden bg-gradient-to-r from-pink-400 to-red-500 flex items-center justify-center mx-auto mb-3">
                      {user.avatarPath ? (
                        <img 
                          src={`http://localhost:8080${user.avatarPath}`} 
                          alt={`${user.username}的头像`} 
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const fallback = target.parentElement?.querySelector('.fallback-avatar') as HTMLElement;
                            if (fallback) fallback.style.display = 'flex';
                          }}
                        />
                      ) : null}
                      <div className={`w-full h-full text-white font-bold text-xl flex items-center justify-center fallback-avatar ${user.avatarPath ? 'hidden' : ''}`}>
                        {(user.nickname || user.username)[0]?.toUpperCase() || 'U'}
                      </div>
                    </div>
                    
                    <h3 className="font-bold text-gray-900 text-lg mb-1">
                      {user.nickname || user.username}
                    </h3>
                    
                    {user.userTitle && (
                      <p className="text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded-full inline-block mb-2">
                        {user.userTitle}
                      </p>
                    )}
                    
                    {user.bio && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {user.bio}
                      </p>
                    )}
                  </div>

                  {/* 统计数据 */}
                  <div className="grid grid-cols-3 gap-4 mb-4 text-center">
                    <div>
                      <div className="text-lg font-bold text-gray-900">{user.postCount}</div>
                      <div className="text-xs text-gray-500">作品</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-gray-900">{user.likeCount}</div>
                      <div className="text-xs text-gray-500">获赞</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-gray-900">{user.followersCount}</div>
                      <div className="text-xs text-gray-500">粉丝</div>
                    </div>
                  </div>

                  {/* 设计优势 */}
                  {user.designAdvantage && (
                    <div className="mb-4">
                      <p className="text-xs text-gray-500 mb-1">设计优势</p>
                      <p className="text-sm text-gray-700 line-clamp-2">{user.designAdvantage}</p>
                    </div>
                  )}

                  {/* 操作按钮 */}
                  <div className="flex space-x-2">
                    <Link
                      href={`/community/post?userId=${user.userId}`}
                      className="flex-1 bg-gray-100 text-gray-700 text-center py-2 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                    >
                      查看作品
                    </Link>
                    
                    {user.hasShop && (
                      <Link
                        href={`/shop/store/${user.userId}`}
                        className="flex-1 bg-blue-600 text-white text-center py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
                      >
                        访问店铺
                      </Link>
                    )}
                    
                    <button
                      onClick={() => handleUnfollow(user.userId, user.nickname || user.username)}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
                    >
                      取消关注
                    </button>
                  </div>

                  {/* 关注时间 */}
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <p className="text-xs text-gray-500 text-center">
                      关注于 {formatDate(user.createTime)}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* 分页 */}
            {totalPages > 1 && (
              <div className="flex justify-center mt-8">
                <div className="flex space-x-2">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 0}
                    className="px-4 py-2 bg-white border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    上一页
                  </button>
                  
                  <span className="px-4 py-2 bg-pink-600 text-white rounded-lg">
                    {currentPage + 1} / {totalPages}
                  </span>
                  
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages - 1}
                    className="px-4 py-2 bg-white border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    下一页
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
} 
 