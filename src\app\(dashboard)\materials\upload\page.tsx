'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';

interface UploadFormData {
  title: string;
  description: string;
  isPublic: boolean;
  file: File | null;
}

export default function MaterialUploadPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  
  // 检查是否是公共上传
  const isPublicUpload = searchParams.get('type') === 'public';
  
  const [formData, setFormData] = useState<UploadFormData>({
    title: '',
    description: '',
    isPublic: isPublicUpload, // 根据类型设置默认值
    file: null,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 根据上传类型设置页面配置
  const pageConfig = isPublicUpload ? {
    title: '上传到公共素材库',
    subtitle: '分享您的创意素材给所有用户',
    backLink: '/materials',
    backText: '返回素材库',
    gradient: 'from-indigo-400 to-purple-500'
  } : {
    title: '上传到我的素材',
    subtitle: '上传素材到您的私人素材库',
    backLink: '/materials/my',
    backText: '我的素材',
    gradient: 'from-purple-400 to-pink-500'
  };

  // 侧边栏菜单项 - 统一深色主题风格
  const menu = [
    { 
      name: '仪表盘', 
      href: '/dashboard', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
        </svg>
      ), 
      active: false, 
      gradient: 'from-blue-500/20 to-cyan-500/20',
      borderColor: 'border-blue-500/30'
    },
    { 
      name: '设计器', 
      href: '/designer', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z" />
        </svg>
      ), 
      gradient: 'from-purple-500/20 to-pink-500/20',
      borderColor: 'border-purple-500/30'
    },
    { 
      name: '我的设计', 
      href: '/designs', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ), 
      gradient: 'from-emerald-500/20 to-teal-500/20',
      borderColor: 'border-emerald-500/30'
    },
    { 
      name: '我的素材', 
      href: '/materials/my', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 5H6a2 2 0 00-2 2v6a2 2 0 002 2h2m0-10h8a2 2 0 012 2v6a2 2 0 01-2 2h-8m0-10v10" />
        </svg>
      ), 
      active: !isPublicUpload, 
      gradient: 'from-orange-500/20 to-red-500/20',
      borderColor: 'border-orange-500/30'
    },
    { 
      name: '素材库', 
      href: '/materials', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ), 
      active: isPublicUpload, 
      gradient: 'from-indigo-500/20 to-purple-500/20',
      borderColor: 'border-indigo-500/30'
    },
  ];

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // 验证文件类型
      const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];
      if (!allowedTypes.includes(file.type)) {
        setErrors({ ...errors, file: '只支持 PNG、JPG、JPEG、SVG 格式的图片' });
        return;
      }

      // 验证文件大小 (最大 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setErrors({ ...errors, file: '文件大小不能超过 10MB' });
        return;
      }

      setFormData({ ...formData, file });
      setErrors({ ...errors, file: '' });

      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // 如果标题为空，使用文件名
      if (!formData.title) {
        const fileName = file.name.split('.')[0];
        setFormData(prev => ({ ...prev, title: fileName }));
      }
    }
  };

  // 处理表单输入
  const handleInputChange = (field: keyof UploadFormData, value: any) => {
    setFormData({ ...formData, [field]: value });
    if (errors[field]) {
      setErrors({ ...errors, [field]: '' });
    }
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '请输入素材标题';
    }

    if (!formData.file) {
      newErrors.file = '请选择要上传的文件';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 提交表单 - 使用一步上传接口
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setUploading(true);

    try {
      // 构建FormData - 一步完成上传和创建素材记录
      const uploadData = new FormData();
      uploadData.append('file', formData.file!);
      uploadData.append('title', formData.title.trim());
      uploadData.append('description', formData.description.trim());
      uploadData.append('category', 'OTHER'); // 默认分类为全部
      uploadData.append('isPublic', formData.isPublic.toString());

      // 直接调用upload-image接口
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8080/api/materials/upload-image', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: uploadData,
      });

      if (response.ok) {
        // 根据上传类型跳转到不同页面
        const redirectPath = isPublicUpload ? '/materials' : '/materials/my';
        router.push(redirectPath);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || '上传失败');
      }
    } catch (error) {
      console.error('上传失败:', error);
      alert(`上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900">
      {/* 现代化侧边栏 - 深色主题 */}
      <aside className={`${sidebarOpen ? 'w-72' : 'w-20'} bg-gray-800/95 backdrop-blur-xl border-r border-gray-700/50 transition-all duration-300 ease-in-out fixed left-0 top-0 h-screen overflow-y-auto scrollbar-thin shadow-xl z-50`}>
        <div className="p-6 flex justify-between items-center">
          {sidebarOpen && (
            <Link href="/" className="flex items-center space-x-3 hover-float">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center text-white font-bold text-xs shadow-md">
                Keycap
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-medium text-white">
                  Keycap
                </span>
                <span className="text-xs text-gray-400 font-normal -mt-1">
                  Keycap Design Platform
                </span>
              </div>
            </Link>
          )}
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 rounded-lg bg-gray-700/30 text-gray-300 hover:text-white hover:bg-gray-600/40 transition-all duration-200"
          >
            {sidebarOpen ? '◀' : '▶'}
          </button>
        </div>
        
        <nav className="mt-8 px-4">
          <ul className="space-y-3">
            {menu.map((item) => (
              <li key={item.name}>
                <Link 
                  href={item.href}
                  className={`group flex items-center ${sidebarOpen ? 'px-4' : 'px-3'} py-3 rounded-xl transition-all duration-300 border backdrop-blur-sm ${
                    item.active 
                      ? `bg-gradient-to-r ${item.gradient} text-white shadow-lg border-white/10 hover:shadow-xl` 
                      : 'text-gray-400 hover:text-white hover:bg-gray-700/20 border-transparent hover:border-gray-600/30'
                  }`}
                >
                  <div className={`flex items-center justify-center w-6 h-6 ${
                    item.active ? 'text-white' : 'text-gray-400 group-hover:text-white'
                  } transition-colors duration-300`}>
                    {item.icon}
                  </div>
                  {sidebarOpen && (
                    <>
                      <span className="ml-3 font-medium tracking-wide">{item.name}</span>
                      {item.active && (
                        <div className="ml-auto">
                          <div className="w-2 h-2 rounded-full bg-white/80 shadow-sm"></div>
                        </div>
                      )}
                    </>
                  )}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </aside>

      {/* 主内容区 */}
      <main className={`flex-1 overflow-auto ${sidebarOpen ? 'ml-72' : 'ml-20'} transition-all duration-300 ease-in-out`}>
        {/* 顶部标题栏 - 深色主题 */}
        <div className="bg-gray-800/80 backdrop-blur-md border-b border-gray-700/50 text-white p-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2 text-white">{pageConfig.title}</h1>
              <p className="text-gray-300">{pageConfig.subtitle}</p>
            </div>
            <Link 
              href={pageConfig.backLink}
              className="bg-gray-700/50 backdrop-blur-sm text-gray-200 px-6 py-3 rounded-xl font-medium hover:bg-gray-600/50 transition-colors flex items-center space-x-2 border border-gray-600/50"
            >
              <span>📂</span>
              <span>{pageConfig.backText}</span>
            </Link>
          </div>

          {/* 上传类型提示 */}
          <div className="mt-6 bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 border border-gray-600/30">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{isPublicUpload ? '🌍' : '🔒'}</span>
              <div>
                <h3 className="font-semibold text-white">
                  {isPublicUpload ? '公共素材库上传' : '私人素材库上传'}
                </h3>
                <p className="text-sm text-gray-300">
                  {isPublicUpload 
                    ? '上传的素材将对所有用户可见，任何人都可以查看和使用'
                    : '上传的素材只有您能看到，属于您的私人素材库'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 上传表单 */}
        <div className="p-8 bg-gray-900/95 min-h-screen">
          <div className="max-w-4xl mx-auto">
            <form onSubmit={handleSubmit} className="space-y-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* 左侧 - 文件上传 */}
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-semibold mb-4 text-white">选择文件</h2>
                    <div className="border-2 border-dashed border-gray-600 bg-gray-800/50 rounded-xl p-8 text-center hover:border-purple-400 transition-colors">
                      {previewUrl ? (
                        <div className="space-y-4">
                          <img 
                            src={previewUrl} 
                            alt="预览" 
                            className="max-w-full h-48 mx-auto object-contain rounded-lg"
                          />
                          <div className="text-sm text-gray-300">
                            {formData.file?.name}
                          </div>
                          <button
                            type="button"
                            onClick={() => {
                              setPreviewUrl(null);
                              setFormData({ ...formData, file: null });
                            }}
                            className="text-red-400 hover:text-red-300"
                          >
                            重新选择
                          </button>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <div className="text-6xl text-gray-400">📎</div>
                          <div>
                            <p className="text-lg font-medium text-white">选择图片文件</p>
                            <p className="text-sm text-gray-400">支持 PNG、JPG、SVG 格式，最大 10MB</p>
                          </div>
                          <input
                            type="file"
                            accept="image/png,image/jpeg,image/jpg,image/svg+xml"
                            onChange={handleFileChange}
                            className="hidden"
                            id="file-upload"
                          />
                          <label
                            htmlFor="file-upload"
                            className="inline-block bg-purple-600 text-white px-6 py-3 rounded-lg cursor-pointer hover:bg-purple-700 transition-colors"
                          >
                            选择文件
                          </label>
                        </div>
                      )}
                    </div>
                    {errors.file && (
                      <p className="mt-2 text-sm text-red-400">{errors.file}</p>
                    )}
                  </div>
                </div>

                {/* 右侧 - 素材信息 */}
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold text-white">素材信息</h2>

                  {/* 标题 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      标题 *
                    </label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      placeholder="给您的素材起个名字"
                      className={`w-full px-4 py-3 bg-gray-700 border text-white placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                        errors.title ? 'border-red-400' : 'border-gray-600'
                      }`}
                    />
                    {errors.title && (
                      <p className="mt-1 text-sm text-red-400">{errors.title}</p>
                    )}
                  </div>

                  {/* 描述 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      描述
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="描述一下这个素材的特点和用途"
                      rows={4}
                      className="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  </div>

                  {/* 可见性设置 - 根据上传类型显示不同内容 */}
                  <div>
                    {isPublicUpload ? (
                      <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4">
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl">🌍</span>
                          <div>
                            <h4 className="font-medium text-blue-300">公共素材库上传</h4>
                            <p className="text-sm text-blue-200">
                              素材将对所有用户公开，任何人都可以查看、点赞和下载
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <label className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={formData.isPublic}
                            onChange={(e) => handleInputChange('isPublic', e.target.checked)}
                            className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
                          />
                          <span className="text-sm font-medium text-gray-300">
                            同时发布到公共素材库（其他用户可以看到和使用）
                          </span>
                        </label>
                        
                        {!formData.isPublic && (
                          <div className="bg-orange-500/20 border border-orange-500/30 rounded-lg p-4">
                            <div className="flex items-center space-x-3">
                              <span className="text-2xl">🔒</span>
                              <div>
                                <h4 className="font-medium text-orange-300">私人素材</h4>
                                <p className="text-sm text-orange-200">
                                  素材只有您能看到，存储在您的个人素材库中
                                </p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 提交按钮 */}
              <div className="flex justify-end space-x-4 pt-6 border-t border-gray-700">
                <Link
                  href={pageConfig.backLink}
                  className="px-6 py-3 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700 transition-colors"
                >
                  取消
                </Link>
                <button
                  type="submit"
                  disabled={uploading}
                  className={`px-8 py-3 rounded-lg font-medium transition-colors ${
                    uploading
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      : 'bg-purple-600 text-white hover:bg-purple-700'
                  }`}
                >
                  {uploading ? '上传中...' : '上传素材'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </main>
    </div>
  );
}