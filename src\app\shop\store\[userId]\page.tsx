'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';

interface Product {
  id: string;
  productId: string;
  name: string;
  description: string;
  price: number;
  stock: number;
  images: string[];
  status: string;
  salesCount: number;
  viewCount: number;
  createTime: string;
}

interface ShopInfo {
  shopId: string;
  shopName: string;
  shopDescription: string;
  contactName: string;
  status: string;
  userId: string;
}

interface UserProfile {
  userId: string;
  username: string;
  nickname: string;
  avatarPath: string;
  hasShop: boolean;
  shopName: string;
  postCount: number;
  likeCount: number;
  followersCount: number;
}

export default function StorePage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const userId = params.userId as string;
  const fromPage = searchParams.get('from'); // 获取来源页面参数
  
  const [products, setProducts] = useState<Product[]>([]);
  const [shopInfo, setShopInfo] = useState<ShopInfo | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [totalProducts, setTotalProducts] = useState(0);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const pageSize = 12;

  useEffect(() => {
    loadShopData();
    getCurrentUser();
  }, [userId, currentPage]);

  const getCurrentUser = async () => {
    const token = localStorage.getItem('token');
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        setCurrentUser({ userId: payload.userId });
      } catch (error) {
        console.error('解析token失败:', error);
      }
    }
  };

  const loadShopData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // 获取用户信息
      const userResponse = await fetch(`http://localhost:8080/api/users/${userId}/profile`, {
        headers
      });

      if (userResponse.ok) {
        const userResult = await userResponse.json();
        if (userResult.code === 200) {
          setUserProfile(userResult.data);
        }
      }

      // 获取店铺商品
      const productsResponse = await fetch(`http://localhost:8080/api/shop/products/user/${userId}?page=${currentPage}&size=${pageSize}`, {
        headers
      });

      if (productsResponse.ok) {
        const productsResult = await productsResponse.json();
        if (productsResult.code === 200) {
          setProducts(productsResult.data.content || []);
          setTotalProducts(productsResult.data.totalElements || 0);
          setTotalPages(productsResult.data.totalPages || 0);
        }
      }

    } catch (error) {
      console.error('加载店铺数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBuyNow = (product: Product) => {
    if (!currentUser) {
      alert('请先登录');
      router.push('/login');
      return;
    }
    
    // 跳转到购买页面，传递商品信息
    router.push(`/buy-now?productId=${product.productId}&quantity=1`);
  };

  const handleAddToCart = async (product: Product) => {
    if (!currentUser) {
      alert('请先登录');
      router.push('/login');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8080/api/cart/add', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productId: product.productId,
          quantity: 1
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          alert('已添加到购物车');
        } else {
          alert(result.message || '添加到购物车失败');
        }
      } else {
        alert('网络错误，添加到购物车失败');
      }
    } catch (error) {
      console.error('添加到购物车失败:', error);
      alert('添加到购物车失败');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">⏳</div>
          <p className="text-gray-500">加载中...</p>
        </div>
      </div>
    );
  }

  if (!userProfile || !userProfile.hasShop) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🏪</div>
          <h3 className="text-xl font-medium text-gray-900 mb-2">店铺不存在</h3>
          <p className="text-gray-500 mb-6">该用户还没有开通店铺</p>
          <Link 
            href={fromPage === 'community' ? '/community' : '/shop'}
            className="inline-flex items-center space-x-2 bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 transition-colors"
          >
            <span>←</span>
            <span>{fromPage === 'community' ? '返回社区' : '返回商城'}</span>
          </Link>
        </div>
      </div>
    );
  }

  const isMyStore = currentUser && currentUser.userId === userId;

  // 根据来源页面决定返回按钮的文本和链接
  const getBackButton = () => {
    if (fromPage === 'community') {
      return {
        text: '← 返回社区',
        href: '/community'
      };
    }
    return {
      text: '← 返回商城',
      href: '/shop'
    };
  };

  const backButton = getBackButton();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <header className="bg-white border-b border-gray-200 px-6 py-4 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link
              href={backButton.href}
              className="inline-flex items-center space-x-2 p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
            >
              <span>{backButton.text}</span>
            </Link>
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                {isMyStore ? '我的店铺' : userProfile.shopName}
              </h1>
            </div>
          </div>
          
          {isMyStore && (
            <Link 
              href="/shop/my-store"
              className="text-orange-600 hover:text-orange-700 font-medium"
            >
              店铺管理
            </Link>
          )}
        </div>
      </header>

      <div className="max-w-7xl mx-auto p-6">
        {/* 店铺信息 */}
        <div className="bg-white rounded-xl shadow-sm p-8 mb-8">
          <div className="flex items-start space-x-6">
            {/* 店主头像 */}
            <div className="w-24 h-24 rounded-full overflow-hidden bg-gradient-to-r from-orange-400 to-red-500 flex items-center justify-center flex-shrink-0">
              {userProfile.avatarPath ? (
                <img 
                  src={`http://localhost:8080${userProfile.avatarPath}`} 
                  alt="店主头像" 
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const fallback = target.parentElement?.querySelector('.fallback-avatar') as HTMLElement;
                    if (fallback) fallback.style.display = 'flex';
                  }}
                />
              ) : null}
              <div className={`w-full h-full text-white font-bold text-3xl flex items-center justify-center fallback-avatar ${userProfile.avatarPath ? 'hidden' : ''}`}>
                {(userProfile.nickname || userProfile.username)[0]?.toUpperCase() || 'S'}
              </div>
            </div>
            
            {/* 店铺信息 */}
            <div className="flex-1">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">{userProfile.shopName}</h2>
              <p className="text-gray-600 mb-4">店主：{userProfile.nickname || userProfile.username}</p>
              
              {/* 店铺统计 */}
              <div className="grid grid-cols-3 gap-8 text-center">
                <div>
                  <div className="text-2xl font-bold text-gray-900">{totalProducts}</div>
                  <div className="text-sm text-gray-500">商品数量</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-900">{userProfile.postCount}</div>
                  <div className="text-sm text-gray-500">发帖数</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-900">{userProfile.followersCount}</div>
                  <div className="text-sm text-gray-500">粉丝数</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 商品标题 */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-gray-900">
            店铺商品 ({totalProducts})
          </h3>
          
          {/* 分页信息 */}
          {totalPages > 1 && (
            <div className="text-sm text-gray-500">
              第 {currentPage + 1} 页，共 {totalPages} 页
            </div>
          )}
        </div>

        {/* 商品列表 */}
        {products.length > 0 ? (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
              {products.map((product) => (
                <div key={product.id} className="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-lg transition-shadow">
                  {/* 商品图片 */}
                  <div className="aspect-square bg-gray-100 overflow-hidden">
                    {product.images && product.images.length > 0 ? (
                      <img 
                        src={product.images[0].startsWith('http') ? product.images[0] : `http://localhost:8080${product.images[0]}`} 
                        alt={product.name}
                        className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                        onClick={() => {
                          window.open(`/shop/product/${product.productId}`, '_blank');
                        }}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/images/placeholders/image-placeholder.png';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400">
                        <span className="text-4xl">📦</span>
                      </div>
                    )}
                  </div>
                  
                  {/* 商品信息 */}
                  <div className="p-4">
                    <h4 className="font-medium text-gray-900 mb-2 line-clamp-2 hover:text-orange-600 cursor-pointer"
                        onClick={() => {
                          window.open(`/shop/product/${product.productId}`, '_blank');
                        }}>
                      {product.name}
                    </h4>
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">{product.description}</p>
                    
                    {/* 价格和销量 */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="text-2xl font-bold text-orange-600">¥{product.price}</div>
                      <div className="text-sm text-gray-500">已售 {product.salesCount}</div>
                    </div>
                    
                    {/* 操作按钮 */}
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleAddToCart(product)}
                        className="flex-1 bg-orange-100 text-orange-600 py-2 px-4 rounded-lg hover:bg-orange-200 transition-colors font-medium"
                      >
                        加入购物车
                      </button>
                      <button
                        onClick={() => handleBuyNow(product)}
                        className="flex-1 bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors font-medium"
                      >
                        立即购买
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 分页控件 */}
            {totalPages > 1 && (
              <div className="flex justify-center space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                  disabled={currentPage === 0}
                  className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  上一页
                </button>
                
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum = Math.max(0, Math.min(totalPages - 5, currentPage - 2)) + i;
                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`px-4 py-2 border rounded-lg ${
                        currentPage === pageNum
                          ? 'bg-orange-600 text-white border-orange-600'
                          : 'border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum + 1}
                    </button>
                  );
                })}
                
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
                  disabled={currentPage === totalPages - 1}
                  className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  下一页
                </button>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📦</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              {isMyStore ? '还没有上架商品' : '该店铺暂无商品'}
            </h3>
            <p className="text-gray-500 mb-6">
              {isMyStore ? '快去上架您的第一个商品吧！' : '店主还没有上架任何商品'}
            </p>
            {isMyStore && (
              <Link 
                href="/shop/products"
                className="inline-flex items-center space-x-2 bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 transition-colors"
              >
                <span>📦</span>
                <span>上架商品</span>
              </Link>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
