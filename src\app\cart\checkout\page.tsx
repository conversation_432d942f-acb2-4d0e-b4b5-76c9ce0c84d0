'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

// API基础URL
const API_BASE_URL = 'http://localhost:8080/api';

// 结算商品接口
interface CheckoutItem {
  productId: string;
  quantity: number;
  price: number;
  productName: string;
  images: string[];
  shopName: string;
}

// 用户地址接口
interface UserAddress {
  id: number;
  receiverName: string;
  phone: string;
  province: string;
  city: string;
  district: string;
  detailAddress: string;
  isDefault: boolean;
}

export default function CartCheckoutPage() {
  const router = useRouter();
  
  const [checkoutItems, setCheckoutItems] = useState<CheckoutItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  
  // 用户地址相关
  const [addresses, setAddresses] = useState<UserAddress[]>([]);
  const [selectedAddressId, setSelectedAddressId] = useState<number | null>(null);
  
  // 订单信息
  const [buyerNote, setBuyerNote] = useState('');
  
  // 新地址表单
  const [showNewAddressForm, setShowNewAddressForm] = useState(false);
  const [newAddress, setNewAddress] = useState({
    receiverName: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    detailAddress: '',
    saveAddress: false
  });

  // 获取token
  const getToken = () => {
    return localStorage.getItem('token');
  };

  // 获取地址列表
  const fetchAddresses = async () => {
    try {
      const token = getToken();
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/user/addresses`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200 && result.data) {
          setAddresses(result.data);
          // 自动选择默认地址
          const defaultAddress = result.data.find((addr: UserAddress) => addr.isDefault);
          if (defaultAddress) {
            setSelectedAddressId(defaultAddress.id);
          }
        }
      }
    } catch (error) {
      console.error('获取地址列表失败:', error);
    }
  };

  // 创建订单
  const createOrder = async () => {
    if (checkoutItems.length === 0) return;

    // 验证表单
    let receiverInfo;
    if (selectedAddressId) {
      const selectedAddress = addresses.find(addr => addr.id === selectedAddressId);
      if (!selectedAddress) {
        alert('请选择收货地址！');
        return;
      }
      receiverInfo = {
        receiverName: selectedAddress.receiverName,
        receiverPhone: selectedAddress.phone,
        receiverAddress: `${selectedAddress.province}${selectedAddress.city}${selectedAddress.district}${selectedAddress.detailAddress}`
      };
    } else if (showNewAddressForm) {
      // 使用新地址
      if (!newAddress.receiverName) {
        alert('请输入收货人姓名！');
        return;
      }
      if (!newAddress.phone) {
        alert('请输入联系电话！');
        return;
      }
      if (!newAddress.province || !newAddress.city || !newAddress.district) {
        alert('请选择完整的省市区信息！');
        return;
      }
      if (!newAddress.detailAddress) {
        alert('请输入详细地址！');
        return;
      }
      receiverInfo = {
        receiverName: newAddress.receiverName,
        receiverPhone: newAddress.phone,
        receiverAddress: `${newAddress.province}${newAddress.city}${newAddress.district}${newAddress.detailAddress}`
      };
    } else {
      alert('请选择收货地址！');
      return;
    }

    try {
      setSubmitting(true);
      const token = getToken();
      
      if (!token) {
        alert('请先登录！');
        router.push('/login');
        return;
      }
      
      let orderData;
      
      if (selectedAddressId) {
        // 使用已保存的地址
        orderData = {
          orderType: 'CART_BUY',
          items: checkoutItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: Number(item.price)
          })),
          addressId: selectedAddressId,
          ...receiverInfo,
          buyerNote: buyerNote
        };
      } else {
        // 使用新地址
        orderData = {
          orderType: 'CART_BUY',
          items: checkoutItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: Number(item.price)
          })),
          ...receiverInfo,
          buyerNote: buyerNote,
          saveAddress: newAddress.saveAddress,
          // 发送详细的地址信息给后端用于保存
          province: newAddress.province,
          city: newAddress.city,
          district: newAddress.district,
          detailAddress: newAddress.detailAddress
        };
      }

      const response = await fetch(`${API_BASE_URL}/orders`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200 && result.data) {
          // 清除localStorage中的结算商品
          localStorage.removeItem('checkout_items');
          // 订单创建成功，跳转到支付页面
          router.push(`/payment?orderId=${result.data.orderId}`);
        } else {
          throw new Error(result.message || '订单创建失败');
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || '订单创建失败');
      }
    } catch (error) {
      console.error('创建订单失败:', error);
      alert(error instanceof Error ? error.message : '订单创建失败，请重试！');
    } finally {
      setSubmitting(false);
    }
  };

  // 计算总价
  const calculateTotal = () => {
    return checkoutItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  // 计算总数量
  const getTotalQuantity = () => {
    return checkoutItems.reduce((total, item) => total + item.quantity, 0);
  };

  // 页面加载时获取数据
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      
      // 从localStorage获取结算商品
      const storedItems = localStorage.getItem('checkout_items');
      if (storedItems) {
        try {
          const items = JSON.parse(storedItems);
          setCheckoutItems(items);
        } catch (error) {
          console.error('解析结算商品失败:', error);
          router.push('/cart');
          return;
        }
      } else {
        // 没有结算商品，返回购物车
        router.push('/cart');
        return;
      }
      
      await fetchAddresses();
      setLoading(false);
    };
    loadData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 cart-checkout-page">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-8">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                  L
                </div>
                <span className="text-xl font-bold text-gray-900">灵狐键创</span>
              </Link>
              <div className="hidden md:flex items-center space-x-6">
                <Link href="/shop" className="text-gray-600 hover:text-gray-900 transition-colors">
                  商城
                </Link>
                <Link href="/cart" className="text-gray-600 hover:text-gray-900 transition-colors">
                  购物车
                </Link>
                <span className="text-purple-600 font-medium">确认订单</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="bg-gradient-to-r from-purple-600 to-cyan-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">确认订单</h1>
          <Link href="/cart" className="text-purple-600 hover:text-purple-700 transition-colors">
            ← 返回购物车
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要内容区域 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 商品信息 */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-5 h-5 bg-cyan-100 rounded-full flex items-center justify-center">
                  <span className="text-cyan-600 text-xs">🛍️</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900">商品信息</h3>
              </div>
              
              <div className="space-y-4">
                {checkoutItems.map((item, index) => (
                  <div key={`${item.productId}-${index}`} className="flex items-center space-x-4 p-4 border border-gray-100 rounded-lg">
                    {/* 商品图片 */}
                    <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center overflow-hidden flex-shrink-0">
                      {item.images && item.images.length > 0 ? (
                        <img 
                          src={item.images[0].startsWith('http') ? item.images[0] : `http://localhost:8080${item.images[0]}`} 
                          alt={item.productName}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="text-xl">🎯</div>
                      )}
                    </div>
                    
                    {/* 商品信息 */}
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{item.productName}</h4>
                      <p className="text-sm text-gray-500">店铺：{item.shopName}</p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-lg font-bold text-red-600">¥{item.price}</span>
                        <span className="text-sm text-gray-600">x {item.quantity}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 收货地址 */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 bg-pink-100 rounded-full flex items-center justify-center">
                    <span className="text-pink-600 text-xs">📍</span>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900">收货地址</h3>
                </div>
                <button
                  onClick={() => setShowNewAddressForm(!showNewAddressForm)}
                  className="text-purple-600 hover:text-purple-700 text-sm"
                >
                  {showNewAddressForm ? '选择已有地址' : '新增地址'}
                </button>
              </div>

              {!showNewAddressForm ? (
                <div className="space-y-3">
                  {addresses.map((address) => (
                    <label key={address.id} className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-purple-300 transition-colors">
                      <input
                        type="radio"
                        name="address"
                        value={address.id}
                        checked={selectedAddressId === address.id}
                        onChange={() => setSelectedAddressId(address.id)}
                        className="mt-1 w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 focus:ring-purple-500"
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900">{address.receiverName}</span>
                          <span className="text-gray-600">{address.phone}</span>
                          {address.isDefault && (
                            <span className="px-2 py-1 bg-purple-100 text-purple-600 text-xs rounded">默认</span>
                          )}
                        </div>
                        <p className="text-gray-600 text-sm mt-1">
                          {address.province}{address.city}{address.district}{address.detailAddress}
                        </p>
                      </div>
                    </label>
                  ))}
                  
                  {addresses.length === 0 && (
                    <p className="text-gray-500 text-center py-4">暂无收货地址，请添加新地址</p>
                  )}
                </div>
              ) : (
                /* 新地址表单 */
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        收货人姓名 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={newAddress.receiverName}
                        onChange={(e) => setNewAddress({...newAddress, receiverName: e.target.value})}
                        placeholder="请输入收货人姓名"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        联系电话 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="tel"
                        value={newAddress.phone}
                        onChange={(e) => setNewAddress({...newAddress, phone: e.target.value})}
                        placeholder="请输入手机号码"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">省份</label>
                      <input
                        type="text"
                        value={newAddress.province}
                        onChange={(e) => setNewAddress({...newAddress, province: e.target.value})}
                        placeholder="省份"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">城市</label>
                      <input
                        type="text"
                        value={newAddress.city}
                        onChange={(e) => setNewAddress({...newAddress, city: e.target.value})}
                        placeholder="城市"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">区县</label>
                      <input
                        type="text"
                        value={newAddress.district}
                        onChange={(e) => setNewAddress({...newAddress, district: e.target.value})}
                        placeholder="区县"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      详细地址 <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      value={newAddress.detailAddress}
                      onChange={(e) => setNewAddress({...newAddress, detailAddress: e.target.value})}
                      rows={3}
                      placeholder="请输入详细地址，如：街道、门牌号、楼层等"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                    />
                  </div>
                  
                  <div>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={newAddress.saveAddress}
                        onChange={(e) => setNewAddress({...newAddress, saveAddress: e.target.checked})}
                        className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500"
                      />
                      <span className="text-sm text-gray-700">保存为常用地址</span>
                    </label>
                  </div>
                </div>
              )}
            </div>

            {/* 买家备注 */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-5 h-5 bg-yellow-100 rounded-full flex items-center justify-center">
                  <span className="text-yellow-600 text-xs">💬</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900">买家备注</h3>
                <span className="text-sm text-gray-500">（可选）</span>
              </div>
              <textarea
                value={buyerNote}
                onChange={(e) => setBuyerNote(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                placeholder="给卖家留言（可选）"
                maxLength={500}
              />
              <div className="text-xs text-gray-400 mt-1">{buyerNote.length}/500</div>
            </div>
          </div>

          {/* 订单摘要 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 sticky top-24">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 text-xs">💰</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900">订单摘要</h3>
              </div>
              
              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">商品数量</span>
                  <span className="text-gray-900">{getTotalQuantity()} 件</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">商品小计</span>
                  <span className="text-gray-900">¥{calculateTotal().toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">运费</span>
                  <span className="text-green-600">免运费</span>
                </div>
                <div className="border-t pt-3">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-medium text-gray-900">实付金额</span>
                    <span className="text-2xl font-bold text-red-600">¥{calculateTotal().toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <button
                onClick={createOrder}
                disabled={submitting}
                className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-shadow disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {submitting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>创建订单中...</span>
                  </div>
                ) : (
                  '提交订单'
                )}
              </button>

              <div className="mt-4 space-y-2 text-xs text-gray-500">
                <p>• 支持支付宝、微信支付</p>
                <p>• 支持7天无理由退换货</p>
                <p>• 满199元免运费</p>
                <p>• 订单提交后请在10分钟内完成支付</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

// API基础URL
const API_BASE_URL = 'http://localhost:8080/api';

// 结算商品接口
interface CheckoutItem {
  productId: string;
  quantity: number;
  price: number;
  productName: string;
  images: string[];
  shopName: string;
}

// 用户地址接口
interface UserAddress {
  id: number;
  receiverName: string;
  phone: string;
  province: string;
  city: string;
  district: string;
  detailAddress: string;
  isDefault: boolean;
}

export default function CartCheckoutPage() {
  const router = useRouter();
  
  const [checkoutItems, setCheckoutItems] = useState<CheckoutItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  
  // 用户地址相关
  const [addresses, setAddresses] = useState<UserAddress[]>([]);
  const [selectedAddressId, setSelectedAddressId] = useState<number | null>(null);
  
  // 订单信息
  const [buyerNote, setBuyerNote] = useState('');
  
  // 新地址表单
  const [showNewAddressForm, setShowNewAddressForm] = useState(false);
  const [newAddress, setNewAddress] = useState({
    receiverName: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    detailAddress: '',
    saveAddress: false
  });

  // 获取token
  const getToken = () => {
    return localStorage.getItem('token');
  };

  // 获取地址列表
  const fetchAddresses = async () => {
    try {
      const token = getToken();
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/user/addresses`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200 && result.data) {
          setAddresses(result.data);
          // 自动选择默认地址
          const defaultAddress = result.data.find((addr: UserAddress) => addr.isDefault);
          if (defaultAddress) {
            setSelectedAddressId(defaultAddress.id);
          }
        }
      }
    } catch (error) {
      console.error('获取地址列表失败:', error);
    }
  };

  // 创建订单
  const createOrder = async () => {
    if (checkoutItems.length === 0) return;

    // 验证表单
    let receiverInfo;
    if (selectedAddressId) {
      const selectedAddress = addresses.find(addr => addr.id === selectedAddressId);
      if (!selectedAddress) {
        alert('请选择收货地址！');
        return;
      }
      receiverInfo = {
        receiverName: selectedAddress.receiverName,
        receiverPhone: selectedAddress.phone,
        receiverAddress: `${selectedAddress.province}${selectedAddress.city}${selectedAddress.district}${selectedAddress.detailAddress}`
      };
    } else if (showNewAddressForm) {
      // 使用新地址
      if (!newAddress.receiverName) {
        alert('请输入收货人姓名！');
        return;
      }
      if (!newAddress.phone) {
        alert('请输入联系电话！');
        return;
      }
      if (!newAddress.province || !newAddress.city || !newAddress.district) {
        alert('请选择完整的省市区信息！');
        return;
      }
      if (!newAddress.detailAddress) {
        alert('请输入详细地址！');
        return;
      }
      receiverInfo = {
        receiverName: newAddress.receiverName,
        receiverPhone: newAddress.phone,
        receiverAddress: `${newAddress.province}${newAddress.city}${newAddress.district}${newAddress.detailAddress}`
      };
    } else {
      alert('请选择收货地址！');
      return;
    }

    try {
      setSubmitting(true);
      const token = getToken();
      
      if (!token) {
        alert('请先登录！');
        router.push('/login');
        return;
      }
      
      let orderData;
      
      if (selectedAddressId) {
        // 使用已保存的地址
        orderData = {
          orderType: 'CART_BUY',
          items: checkoutItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: Number(item.price)
          })),
          addressId: selectedAddressId,
          ...receiverInfo,
          buyerNote: buyerNote
        };
      } else {
        // 使用新地址
        orderData = {
          orderType: 'CART_BUY',
          items: checkoutItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: Number(item.price)
          })),
          ...receiverInfo,
          buyerNote: buyerNote,
          saveAddress: newAddress.saveAddress,
          // 发送详细的地址信息给后端用于保存
          province: newAddress.province,
          city: newAddress.city,
          district: newAddress.district,
          detailAddress: newAddress.detailAddress
        };
      }

      const response = await fetch(`${API_BASE_URL}/orders`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200 && result.data) {
          // 清除localStorage中的结算商品
          localStorage.removeItem('checkout_items');
          // 订单创建成功，跳转到支付页面
          router.push(`/payment?orderId=${result.data.orderId}`);
        } else {
          throw new Error(result.message || '订单创建失败');
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || '订单创建失败');
      }
    } catch (error) {
      console.error('创建订单失败:', error);
      alert(error instanceof Error ? error.message : '订单创建失败，请重试！');
    } finally {
      setSubmitting(false);
    }
  };

  // 计算总价
  const calculateTotal = () => {
    return checkoutItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  // 计算总数量
  const getTotalQuantity = () => {
    return checkoutItems.reduce((total, item) => total + item.quantity, 0);
  };

  // 页面加载时获取数据
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      
      // 从localStorage获取结算商品
      const storedItems = localStorage.getItem('checkout_items');
      if (storedItems) {
        try {
          const items = JSON.parse(storedItems);
          setCheckoutItems(items);
        } catch (error) {
          console.error('解析结算商品失败:', error);
          router.push('/cart');
          return;
        }
      } else {
        // 没有结算商品，返回购物车
        router.push('/cart');
        return;
      }
      
      await fetchAddresses();
      setLoading(false);
    };
    loadData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 cart-checkout-page">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-8">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                  L
                </div>
                <span className="text-xl font-bold text-gray-900">灵狐键创</span>
              </Link>
              <div className="hidden md:flex items-center space-x-6">
                <Link href="/shop" className="text-gray-600 hover:text-gray-900 transition-colors">
                  商城
                </Link>
                <Link href="/cart" className="text-gray-600 hover:text-gray-900 transition-colors">
                  购物车
                </Link>
                <span className="text-purple-600 font-medium">确认订单</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="bg-gradient-to-r from-purple-600 to-cyan-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">确认订单</h1>
          <Link href="/cart" className="text-purple-600 hover:text-purple-700 transition-colors">
            ← 返回购物车
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要内容区域 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 商品信息 */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-5 h-5 bg-cyan-100 rounded-full flex items-center justify-center">
                  <span className="text-cyan-600 text-xs">🛍️</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900">商品信息</h3>
              </div>
              
              <div className="space-y-4">
                {checkoutItems.map((item, index) => (
                  <div key={`${item.productId}-${index}`} className="flex items-center space-x-4 p-4 border border-gray-100 rounded-lg">
                    {/* 商品图片 */}
                    <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center overflow-hidden flex-shrink-0">
                      {item.images && item.images.length > 0 ? (
                        <img 
                          src={item.images[0].startsWith('http') ? item.images[0] : `http://localhost:8080${item.images[0]}`} 
                          alt={item.productName}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="text-xl">🎯</div>
                      )}
                    </div>
                    
                    {/* 商品信息 */}
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{item.productName}</h4>
                      <p className="text-sm text-gray-500">店铺：{item.shopName}</p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-lg font-bold text-red-600">¥{item.price}</span>
                        <span className="text-sm text-gray-600">x {item.quantity}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 收货地址 */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 bg-pink-100 rounded-full flex items-center justify-center">
                    <span className="text-pink-600 text-xs">📍</span>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900">收货地址</h3>
                </div>
                <button
                  onClick={() => setShowNewAddressForm(!showNewAddressForm)}
                  className="text-purple-600 hover:text-purple-700 text-sm"
                >
                  {showNewAddressForm ? '选择已有地址' : '新增地址'}
                </button>
              </div>

              {!showNewAddressForm ? (
                <div className="space-y-3">
                  {addresses.map((address) => (
                    <label key={address.id} className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-purple-300 transition-colors">
                      <input
                        type="radio"
                        name="address"
                        value={address.id}
                        checked={selectedAddressId === address.id}
                        onChange={() => setSelectedAddressId(address.id)}
                        className="mt-1 w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 focus:ring-purple-500"
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900">{address.receiverName}</span>
                          <span className="text-gray-600">{address.phone}</span>
                          {address.isDefault && (
                            <span className="px-2 py-1 bg-purple-100 text-purple-600 text-xs rounded">默认</span>
                          )}
                        </div>
                        <p className="text-gray-600 text-sm mt-1">
                          {address.province}{address.city}{address.district}{address.detailAddress}
                        </p>
                      </div>
                    </label>
                  ))}
                  
                  {addresses.length === 0 && (
                    <p className="text-gray-500 text-center py-4">暂无收货地址，请添加新地址</p>
                  )}
                </div>
              ) : (
                /* 新地址表单 */
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        收货人姓名 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={newAddress.receiverName}
                        onChange={(e) => setNewAddress({...newAddress, receiverName: e.target.value})}
                        placeholder="请输入收货人姓名"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        联系电话 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="tel"
                        value={newAddress.phone}
                        onChange={(e) => setNewAddress({...newAddress, phone: e.target.value})}
                        placeholder="请输入手机号码"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">省份</label>
                      <input
                        type="text"
                        value={newAddress.province}
                        onChange={(e) => setNewAddress({...newAddress, province: e.target.value})}
                        placeholder="省份"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">城市</label>
                      <input
                        type="text"
                        value={newAddress.city}
                        onChange={(e) => setNewAddress({...newAddress, city: e.target.value})}
                        placeholder="城市"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">区县</label>
                      <input
                        type="text"
                        value={newAddress.district}
                        onChange={(e) => setNewAddress({...newAddress, district: e.target.value})}
                        placeholder="区县"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      详细地址 <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      value={newAddress.detailAddress}
                      onChange={(e) => setNewAddress({...newAddress, detailAddress: e.target.value})}
                      rows={3}
                      placeholder="请输入详细地址，如：街道、门牌号、楼层等"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                    />
                  </div>
                  
                  <div>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={newAddress.saveAddress}
                        onChange={(e) => setNewAddress({...newAddress, saveAddress: e.target.checked})}
                        className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500"
                      />
                      <span className="text-sm text-gray-700">保存为常用地址</span>
                    </label>
                  </div>
                </div>
              )}
            </div>

            {/* 买家备注 */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-5 h-5 bg-yellow-100 rounded-full flex items-center justify-center">
                  <span className="text-yellow-600 text-xs">💬</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900">买家备注</h3>
                <span className="text-sm text-gray-500">（可选）</span>
              </div>
              <textarea
                value={buyerNote}
                onChange={(e) => setBuyerNote(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                placeholder="给卖家留言（可选）"
                maxLength={500}
              />
              <div className="text-xs text-gray-400 mt-1">{buyerNote.length}/500</div>
            </div>
          </div>

          {/* 订单摘要 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 sticky top-24">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 text-xs">💰</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900">订单摘要</h3>
              </div>
              
              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">商品数量</span>
                  <span className="text-gray-900">{getTotalQuantity()} 件</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">商品小计</span>
                  <span className="text-gray-900">¥{calculateTotal().toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">运费</span>
                  <span className="text-green-600">免运费</span>
                </div>
                <div className="border-t pt-3">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-medium text-gray-900">实付金额</span>
                    <span className="text-2xl font-bold text-red-600">¥{calculateTotal().toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <button
                onClick={createOrder}
                disabled={submitting}
                className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-shadow disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {submitting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>创建订单中...</span>
                  </div>
                ) : (
                  '提交订单'
                )}
              </button>

              <div className="mt-4 space-y-2 text-xs text-gray-500">
                <p>• 支持支付宝、微信支付</p>
                <p>• 支持7天无理由退换货</p>
                <p>• 满199元免运费</p>
                <p>• 订单提交后请在10分钟内完成支付</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 