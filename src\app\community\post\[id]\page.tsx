'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';

interface Post {
  postId: number;
  userId: string;
  username: string;
  userAvatar: string;
  categoryId: number;
  categoryName: string;
  categoryIcon: string;
  topicId: number;
  topicName: string;
  topicColor: string;
  designId: number;
  designName: string;
  designData: string;
  title: string;
  content: string;
  images: string[];
  postType: string;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  isPinned: boolean;
  isHot: boolean;
  isLiked: boolean;
  isFollowed: boolean;
  status: string;
  createTime: string;
  updateTime: string;
}

interface Comment {
  commentId: number;
  postId: number;
  userId: string;
  username: string;
  userAvatar: string;
  parentCommentId: number;
  parentUsername?: string;
  content: string;
  likeCount: number;
  isLiked: boolean;
  status: string;
  createTime: string;
  updateTime: string;
  replies?: Comment[];
}

interface UserProfile {
  userId: string;
  username: string;
  nickname?: string;
  avatarPath: string;
  designAdvantage: string;
  bio: string;
  postCount: number;
  likeCount: number;
  followersCount: number;
  followingCount: number;
  hasShop: boolean;
  shopName: string;
  isFollowed?: boolean;
}

export default function PostDetailPage() {
  const params = useParams();
  const router = useRouter();
  const postId = params.id as string;
  
  const [post, setPost] = useState<Post | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [currentUser, setCurrentUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<number | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [userShopProducts, setUserShopProducts] = useState<any[]>([]);
  const [shopLoading, setShopLoading] = useState(false);

  useEffect(() => {
    loadPostDetail();
    getCurrentUser();
  }, [postId]);

  const getCurrentUser = async () => {
    const token = localStorage.getItem('token');
    let userId = null;
    
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        console.log('当前登录用户ID:', payload.userId);
        userId = payload.userId;
        setCurrentUserId(payload.userId);
        
        // 获取当前用户的完整信息
        await loadCurrentUserProfile(userId);
      } catch (error) {
        console.error('解析token失败:', error);
        // 如果token解析失败，清除用户状态
        setCurrentUserId(null);
        setCurrentUser(null);
      }
    } else {
      // 如果没有token，清除用户状态
      console.log('没有token，用户未登录');
      setCurrentUserId(null);
      setCurrentUser(null);
    }
  };

  const loadCurrentUserProfile = async (userId: string) => {
    try {
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`http://localhost:8080/api/users/profile`, {
        headers
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200 && result.data) {
          setCurrentUser(result.data);
        }
      }
    } catch (error) {
      console.error('获取当前用户信息失败:', error);
    }
  };

  // 获取用户店铺商品
  const fetchUserShopProducts = async (userId: string) => {
    try {
      setShopLoading(true);
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // 获取用户店铺商品
      const response = await fetch(`http://localhost:8080/api/shop/products/user/${userId}?page=0&size=6`, {
        headers
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          setUserShopProducts(result.data.content || []);
        } else {
          setUserShopProducts([]);
        }
      } else {
        setUserShopProducts([]);
      }
    } catch (error) {
      console.error('获取用户店铺商品失败:', error);
      setUserShopProducts([]);
    } finally {
      setShopLoading(false);
    }
  };

  const loadPostDetail = async () => {
    try {
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // 加载帖子详情
      const postResponse = await fetch(`http://localhost:8080/api/community/posts/${postId}`, {
        headers
      });

      if (postResponse.ok) {
        const postResult = await postResponse.json();
        if (postResult.code === 200) {
          setPost(postResult.data);
          
          // 加载用户资料
          await loadUserProfile(postResult.data.userId);
          
          // 加载评论
          await loadComments();
        } else {
          console.error('加载帖子失败:', postResult.message);
        }
      }
    } catch (error) {
      console.error('加载帖子详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadUserProfile = async (userId: string) => {
    console.log('开始加载用户资料，用户ID:', userId);
    
    try {
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`http://localhost:8080/api/users/${userId}/profile`, {
        headers
      });
      
      console.log('用户资料API响应状态:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('用户资料API响应数据:', result);
        
        if (result.code === 200 && result.data) {
          console.log('✅ 使用API返回的真实用户资料:', result.data);
          setUserProfile(result.data);
          
          // 如果用户有店铺，获取店铺商品
          if (result.data.hasShop) {
            await fetchUserShopProducts(userId);
          }
          return;
        } else {
          console.error('用户资料API返回错误:', result.message);
        }
      } else {
        console.error('用户资料API响应失败:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('用户资料API调用失败:', error);
    }

    // 如果API失败，使用基础用户信息（显示从帖子获取的用户名）
    console.log('⚠️ API失败，使用基础用户信息');
    const fallbackProfile: UserProfile = {
      userId: userId,
      username: post?.username || '用户',
      avatarPath: '',
      designAdvantage: '',
      bio: '',
      postCount: 0,
      likeCount: 0,
      followersCount: 0,
      followingCount: 0,
      hasShop: false,
      shopName: '',
      isFollowed: false
    };
    console.log('设置回退用户资料:', fallbackProfile);
    setUserProfile(fallbackProfile);
  };

  const loadComments = async () => {
    try {
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`http://localhost:8080/api/community/posts/${postId}/comments`, {
        headers
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          setComments(result.data.content || []);
        }
      }
    } catch (error) {
      console.error('加载评论失败:', error);
    }
  };

  const handleLikePost = async () => {
    if (!post) return;
    
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      const response = await fetch(`http://localhost:8080/api/community/posts/${postId}/like`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          setPost({
            ...post,
            isLiked: result.data,
            likeCount: result.data ? post.likeCount + 1 : post.likeCount - 1
          });
        }
      }
    } catch (error) {
      console.error('点赞失败:', error);
    }
  };

  const handleAddComment = async () => {
    if (!newComment.trim()) {
      alert('请输入评论内容');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      const response = await fetch(`http://localhost:8080/api/community/comments`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          postId: parseInt(postId),
          content: newComment,
          parentCommentId: null
        })
      });

      const result = await response.json();
      if (result.code === 200) {
        setNewComment('');
        await loadComments();
        // 更新帖子评论数
        if (post) {
          setPost({
            ...post,
            commentCount: post.commentCount + 1
          });
        }
      } else {
        // 检查是否是禁言错误
        if (result.message && result.message.includes('禁言')) {
          alert('您的账户已被禁言，详情请咨询客服');
        } else {
          alert(result.message || '发表评论失败');
        }
      }
    } catch (error) {
      console.error('发表评论失败:', error);
    }
  };

  // 开始回复评论
  const handleStartReply = (commentId: number, username: string) => {
    setReplyingTo(commentId);
    setReplyContent(`@${username} `);
  };

  // 取消回复
  const handleCancelReply = () => {
    setReplyingTo(null);
    setReplyContent('');
  };

  // 提交回复
  const handleSubmitReply = async () => {
    if (!replyContent.trim()) {
      alert('请输入回复内容');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      const response = await fetch(`http://localhost:8080/api/community/comments`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          postId: parseInt(postId),
          content: replyContent,
          parentCommentId: replyingTo
        })
      });

      const result = await response.json();
      if (result.code === 200) {
        setReplyingTo(null);
        setReplyContent('');
        await loadComments();
        // 更新帖子评论数
        if (post) {
          setPost({
            ...post,
            commentCount: post.commentCount + 1
          });
        }
        alert('回复成功！');
      } else {
        // 检查是否是禁言错误
        if (result.message && result.message.includes('禁言')) {
          alert('您的账户已被禁言，详情请咨询客服');
        } else {
          alert(result.message || '回复失败');
        }
      }
    } catch (error) {
      console.error('回复失败:', error);
      alert('网络错误，回复失败');
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    if (diff < 2592000000) return `${Math.floor(diff / 86400000)}天前`;
    
    return date.toLocaleDateString('zh-CN');
  };

  const isMyPost = currentUserId === post?.userId;

  // 关注/取消关注用户
  const handleFollowUser = async () => {
    if (!userProfile || !currentUserId) {
      alert('请先登录');
      return;
    }
    
    if (currentUserId === userProfile.userId) {
      alert('不能关注自己');
      return;
    }
    
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      console.log('开始关注操作，目标用户:', userProfile.userId, '当前用户:', currentUserId);

      const response = await fetch(`http://localhost:8080/api/users/${userProfile.userId}/follow`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('关注API响应状态:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('关注API响应数据:', result);
        
        if (result.code === 200) {
          // 更新用户资料中的关注状态和关注者数量
          const newFollowersCount = result.data 
            ? (userProfile.followersCount + 1) 
            : Math.max(0, userProfile.followersCount - 1);
            
          setUserProfile({
            ...userProfile,
            isFollowed: result.data,
            followersCount: newFollowersCount
          });
          
          const action = result.data ? '关注成功' : '取消关注成功';
          console.log('✅', action);
          
          // 显示成功提示
          setTimeout(() => {
            alert(action);
          }, 100);
        } else {
          console.error('关注操作失败:', result.message);
          alert(result.message || '操作失败');
        }
      } else {
        console.error('关注API请求失败:', response.status, response.statusText);
        alert(`操作失败 (${response.status})`);
      }
    } catch (error) {
      console.error('关注操作异常:', error);
      alert('网络错误，关注操作失败');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">⏳</div>
          <p className="text-gray-500">加载中...</p>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😕</div>
          <h3 className="text-xl font-medium text-gray-900 mb-2">帖子不存在</h3>
          <p className="text-gray-500 mb-6">该帖子可能已被删除或不存在</p>
          <Link 
            href="/community"
            className="inline-flex items-center space-x-2 bg-pink-600 text-white px-6 py-3 rounded-lg hover:bg-pink-700 transition-colors"
          >
            <span>←</span>
            <span>返回社区</span>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <header className="bg-white border-b border-gray-200 px-6 py-4 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link
              href="/community"
              className="inline-flex items-center space-x-2 p-2 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors"
            >
              <span>← 返回</span>
            </Link>
            <div>
              <h1 className="text-xl font-bold text-gray-900">帖子详情</h1>
            </div>
          </div>
          
          <Link 
            href="/community"
            className="text-pink-600 hover:text-pink-700 font-medium"
          >
            社区首页
          </Link>
        </div>
      </header>

      <div className="max-w-6xl mx-auto p-6">
        <div className="flex gap-8">
          {/* 主内容区 */}
          <div className="flex-1 space-y-6">
            {/* 帖子内容 */}
            <div className="bg-white rounded-xl shadow-sm p-8">
              {/* 帖子头部 */}
              <div className="flex items-start justify-between mb-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-r from-pink-400 to-red-500 flex items-center justify-center flex-shrink-0">
                    {post.userAvatar ? (
                      <img 
                        src={`http://localhost:8080${post.userAvatar}`} 
                        alt={`${post.username}的头像`} 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const fallback = target.parentElement?.querySelector('.fallback-avatar') as HTMLElement;
                          if (fallback) fallback.style.display = 'flex';
                        }}
                      />
                    ) : null}
                    <div className={`w-full h-full text-white font-bold text-lg flex items-center justify-center fallback-avatar ${post.userAvatar ? 'hidden' : ''}`}>
                    {post.username[0]?.toUpperCase() || 'U'}
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center space-x-3">
                      <span className="font-bold text-gray-900 text-lg">{post.username}</span>
                      {post.categoryName && (
                        <span className="text-sm px-3 py-1 bg-gray-100 text-gray-600 rounded-full">
                          {post.categoryIcon} {post.categoryName}
                        </span>
                      )}
                      {post.topicName && (
                        <span 
                          className="text-sm px-3 py-1 text-white rounded-full"
                          style={{ backgroundColor: post.topicColor }}
                        >
                          #{post.topicName}
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      {formatDate(post.createTime)}
                    </div>
                  </div>
                </div>
              </div>

              {/* 帖子标题 */}
              <h1 className="text-2xl font-bold text-gray-900 mb-4">{post.title}</h1>

              {/* 帖子内容 */}
              <div className="text-gray-700 leading-relaxed mb-6 whitespace-pre-wrap">
                {post.content}
              </div>

              {/* 帖子图片 */}
              {post.images && post.images.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                  {post.images.map((image, index) => (
                    <div key={index} className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                      <img 
                                                    src={image.startsWith('http') ? image : `http://localhost:8080${image}`} 
                        alt={`图片 ${index + 1}`} 
                        className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                      />
                    </div>
                  ))}
                </div>
              )}

              {/* 帖子统计和操作 */}
              <div className="flex items-center justify-between pt-6 border-t border-gray-100">
                <div className="flex items-center space-x-8 text-gray-500">
                  <button 
                    onClick={handleLikePost}
                    className={`flex items-center space-x-2 transition-colors ${
                      post.isLiked 
                        ? 'text-pink-600 hover:text-pink-700' 
                        : 'hover:text-pink-600'
                    }`}
                  >
                    <span className="text-xl">{post.isLiked ? '❤️' : '🤍'}</span>
                    <span className="font-medium">{post.likeCount}</span>
                  </button>
                  <div className="flex items-center space-x-2">
                    <span className="text-xl">💬</span>
                    <span className="font-medium">{post.commentCount}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-xl">👁️</span>
                    <span className="font-medium">{post.viewCount}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 评论区 */}
            <div id="comments" className="bg-white rounded-xl shadow-sm p-8">
              <h2 className="text-xl font-bold text-gray-900 mb-6">
                评论 ({post.commentCount})
              </h2>

              {/* 发表评论 */}
              <div className="mb-8">
                <div className="flex space-x-4">
                  {/* 当前用户头像 */}
                  <div className="w-10 h-10 rounded-full overflow-hidden bg-gradient-to-r from-pink-400 to-purple-500 flex items-center justify-center flex-shrink-0">
                    {currentUser?.avatarPath ? (
                      <img 
                        src={`http://localhost:8080${currentUser.avatarPath}`} 
                        alt="我的头像" 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const fallback = target.parentElement?.querySelector('.fallback-avatar') as HTMLElement;
                          if (fallback) fallback.style.display = 'flex';
                        }}
                      />
                    ) : null}
                    <div className={`w-full h-full text-white font-bold text-sm flex items-center justify-center fallback-avatar ${currentUser?.avatarPath ? 'hidden' : ''}`}>
                      {(currentUser?.nickname || currentUser?.username || 'U')[0]?.toUpperCase()}
                    </div>
                  </div>
                  
                  {/* 评论输入框 */}
                  <div className="flex-1">
                <textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="写下您的评论..."
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent resize-none text-gray-800"
                />
                <div className="flex justify-end mt-3">
                  <button
                    onClick={handleAddComment}
                    className="px-6 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors"
                  >
                    发表评论
                  </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* 评论列表 */}
              <div className="space-y-6">
                {comments.map((comment) => (
                  <div key={comment.commentId} className="space-y-4">
                    {/* 主评论 */}
                    <div className="flex space-x-4">
                      {/* 评论用户头像 */}
                      <div className="w-10 h-10 rounded-full overflow-hidden bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center flex-shrink-0">
                        {comment.userAvatar ? (
                          <img 
                            src={`http://localhost:8080${comment.userAvatar}`} 
                            alt={`${comment.username}的头像`} 
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              const fallback = target.parentElement?.querySelector('.fallback-avatar') as HTMLElement;
                              if (fallback) fallback.style.display = 'flex';
                            }}
                          />
                        ) : null}
                        <div className={`w-full h-full text-white font-bold text-sm flex items-center justify-center fallback-avatar ${comment.userAvatar ? 'hidden' : ''}`}>
                      {comment.username[0]?.toUpperCase() || 'U'}
                        </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <span className="font-medium text-gray-900">{comment.username}</span>
                        <span className="text-sm text-gray-500">{formatDate(comment.createTime)}</span>
                      </div>
                      <p className="text-gray-700 leading-relaxed mb-3">{comment.content}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <button className="hover:text-pink-600 transition-colors">
                          ❤️ {comment.likeCount}
                        </button>
                          <button 
                            onClick={() => handleStartReply(comment.commentId, comment.username)}
                            className="hover:text-blue-600 transition-colors"
                          >
                            回复
                          </button>
                        </div>

                        {/* 回复输入框 */}
                        {replyingTo === comment.commentId && (
                          <div className="mt-4 pl-4 border-l-2 border-blue-200">
                            <div className="flex space-x-3">
                              <div className="w-8 h-8 rounded-full overflow-hidden bg-gradient-to-r from-green-400 to-blue-500 flex items-center justify-center flex-shrink-0">
                                {currentUser?.avatarPath ? (
                                  <img 
                                    src={`http://localhost:8080${currentUser.avatarPath}`} 
                                    alt="我的头像" 
                                    className="w-full h-full object-cover"
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement;
                                      target.style.display = 'none';
                                      const fallback = target.parentElement?.querySelector('.fallback-avatar') as HTMLElement;
                                      if (fallback) fallback.style.display = 'flex';
                                    }}
                                  />
                                ) : null}
                                <div className={`w-full h-full text-white font-bold text-xs flex items-center justify-center fallback-avatar ${currentUser?.avatarPath ? 'hidden' : ''}`}>
                                  {(currentUser?.nickname || currentUser?.username || 'U')[0]?.toUpperCase()}
                                </div>
                              </div>
                              
                              <div className="flex-1">
                                <textarea
                                  value={replyContent}
                                  onChange={(e) => setReplyContent(e.target.value)}
                                  placeholder={`回复 @${comment.username}...`}
                                  rows={2}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm text-gray-800"
                                />
                                <div className="flex justify-end space-x-2 mt-2">
                                  <button
                                    onClick={handleCancelReply}
                                    className="px-4 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                                  >
                                    取消
                                  </button>
                                  <button
                                    onClick={handleSubmitReply}
                                    className="px-4 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                                  >
                                    回复
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 回复列表 */}
                    {comment.replies && comment.replies.length > 0 && (
                      <div className="ml-14 space-y-3">
                        {comment.replies.map((reply) => (
                          <div key={reply.commentId} className="flex space-x-3 bg-gray-50 rounded-lg p-4">
                            {/* 回复用户头像 */}
                            <div className="w-8 h-8 rounded-full overflow-hidden bg-gradient-to-r from-green-400 to-blue-500 flex items-center justify-center flex-shrink-0">
                              {reply.userAvatar ? (
                                <img 
                                  src={`http://localhost:8080${reply.userAvatar}`} 
                                  alt={`${reply.username}的头像`} 
                                  className="w-full h-full object-cover"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.style.display = 'none';
                                    const fallback = target.parentElement?.querySelector('.fallback-avatar') as HTMLElement;
                                    if (fallback) fallback.style.display = 'flex';
                                  }}
                                />
                              ) : null}
                              <div className={`w-full h-full text-white font-bold text-xs flex items-center justify-center fallback-avatar ${reply.userAvatar ? 'hidden' : ''}`}>
                                {reply.username[0]?.toUpperCase() || 'U'}
                              </div>
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <span className="font-medium text-gray-900 text-sm">{reply.username}</span>
                                {reply.parentUsername && (
                                  <>
                                    <span className="text-gray-400 text-sm">回复</span>
                                    <span className="text-blue-600 text-sm">@{reply.parentUsername}</span>
                                  </>
                                )}
                                <span className="text-xs text-gray-500">{formatDate(reply.createTime)}</span>
                              </div>
                              <p className="text-gray-700 text-sm leading-relaxed mb-2">{reply.content}</p>
                              <div className="flex items-center space-x-3 text-xs text-gray-500">
                                <button className="hover:text-pink-600 transition-colors">
                                  ❤️ {reply.likeCount}
                                </button>
                                <button 
                                  onClick={() => handleStartReply(comment.commentId, reply.username)}
                                  className="hover:text-blue-600 transition-colors"
                                >
                          回复
                        </button>
                      </div>
                    </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}

                {comments.length === 0 && (
                  <div className="text-center py-12">
                    <div className="text-4xl mb-4">💬</div>
                    <p className="text-gray-500">暂无评论，来发表第一条评论吧！</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 右侧用户资料卡 */}
          <div className="w-80 space-y-6">
            {post && (
              <div className="bg-white rounded-xl shadow-sm p-6 sticky top-24">
                <div className="text-center mb-6">
                  <div className="w-20 h-20 rounded-full overflow-hidden bg-gradient-to-r from-pink-400 to-red-500 flex items-center justify-center mx-auto mb-4">
                    {userProfile?.avatarPath ? (
                      <img 
                        src={`http://localhost:8080${userProfile.avatarPath}`} 
                        alt="用户头像" 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const fallback = target.parentElement?.querySelector('.fallback-avatar') as HTMLElement;
                          if (fallback) fallback.style.display = 'flex';
                        }}
                      />
                    ) : null}
                    <div className={`w-full h-full text-white font-bold text-2xl flex items-center justify-center fallback-avatar ${userProfile?.avatarPath ? 'hidden' : ''}`}>
                      {(userProfile?.nickname || userProfile?.username || post.username)[0]?.toUpperCase() || 'U'}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{userProfile?.nickname || userProfile?.username || post.username}</h3>
                  {userProfile?.bio && (
                    <p className="text-gray-600 text-sm">{userProfile.bio}</p>
                  )}
                </div>

                {/* 统计信息 */}
                <div className="grid grid-cols-3 gap-4 mb-6 text-center">
                  <div>
                    <div className="text-xl font-bold text-gray-900">{userProfile?.postCount || 0}</div>
                    <div className="text-sm text-gray-500">发帖数</div>
                  </div>
                  <div>
                    <div className="text-xl font-bold text-gray-900">{userProfile?.likeCount || 0}</div>
                    <div className="text-sm text-gray-500">获赞数</div>
                  </div>
                  <div>
                    <div className="text-xl font-bold text-gray-900">{userProfile?.followersCount || 0}</div>
                    <div className="text-sm text-gray-500">关注者</div>
                  </div>
                </div>

                {/* 设计优势 */}
                {userProfile?.designAdvantage && (
                  <div className="mb-6">
                    <h4 className="font-medium text-gray-900 mb-2">设计优势</h4>
                    <p className="text-sm text-gray-600">{userProfile.designAdvantage}</p>
                  </div>
                )}

                {/* 店铺信息 */}
                {(() => {
                  // 安全的ID比较，确保类型匹配
                  const isMyProfile = currentUserId && userProfile?.userId && 
                    String(currentUserId) === String(userProfile.userId);
                  
                  // 添加调试信息
                  console.log('店铺显示调试信息:', {
                    currentUserId,
                    userProfileUserId: userProfile?.userId,
                    currentUserIdString: String(currentUserId),
                    userProfileUserIdString: String(userProfile?.userId),
                    isMyProfile,
                    hasShop: userProfile?.hasShop
                  });
                  
                  if (userProfile?.hasShop) {
                    return (
                      <div className="bg-orange-50 rounded-xl p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <span className="text-lg">🏪</span>
                            <h4 className="font-medium text-gray-900">
                              {isMyProfile ? '我的店铺' : 'TA的店铺'}
                            </h4>
                          </div>
                          <button
                            onClick={() => {
                              const shopUrl = isMyProfile ? '/shop/my-store' : `/shop/store/${userProfile.userId}?from=community`;
                              window.open(shopUrl, '_blank');
                            }}
                            className="text-sm text-orange-600 hover:text-orange-700 font-medium"
                          >
                            {isMyProfile ? '管理店铺 →' : '访问店铺 →'}
                          </button>
                        </div>
                        
                        {userProfile.shopName && (
                          <p className="text-sm text-gray-600 mb-3">{userProfile.shopName}</p>
                        )}
                        
                        {/* 店铺商品展示 */}
                        {shopLoading ? (
                          <div className="text-center py-4">
                            <div className="text-sm text-gray-500">加载商品中...</div>
                          </div>
                        ) : userShopProducts.length > 0 ? (
                          <div>
                            <div className="text-xs text-gray-500 mb-2">店铺商品 ({userShopProducts.length})</div>
                            <div className="grid grid-cols-3 gap-2">
                              {userShopProducts.slice(0, 6).map((product, index) => (
                                <div 
                                  key={product.productId || index}
                                  className="bg-white rounded-lg p-2 hover:shadow-md transition-shadow cursor-pointer"
                                                                   onClick={() => {
                                   window.open(`/shop/product/${product.productId || product.id}`, '_blank');
                                 }}
                                >
                                                                     <div className="aspect-square bg-gray-100 rounded-md mb-1 overflow-hidden">
                                     {product.images && product.images.length > 0 ? (
                                       <img 
                                         src={product.images[0].startsWith('http') ? product.images[0] : `http://localhost:8080${product.images[0]}`} 
                                         alt={product.name || product.productName}
                                         className="w-full h-full object-cover"
                                         onError={(e) => {
                                           const target = e.target as HTMLImageElement;
                                           target.src = '/images/placeholders/image-placeholder.png';
                                         }}
                                       />
                                     ) : (
                                       <div className="w-full h-full flex items-center justify-center text-gray-400">
                                         <span className="text-xs">📦</span>
                  </div>
                )}
                                   </div>
                                                                     <div className="text-xs text-gray-900 truncate" title={product.name || product.productName}>
                                     {product.name || product.productName}
                                   </div>
                                  <div className="text-xs text-orange-600 font-medium">
                                    ¥{product.price}
                                  </div>
                                </div>
                              ))}
                            </div>
                            {userShopProducts.length > 6 && (
                              <div className="text-center mt-2">
                                <button
                                  onClick={() => {
                                    const shopUrl = isMyProfile ? '/shop/my-store' : `/shop/store/${userProfile.userId}?from=community`;
                                    window.open(shopUrl, '_blank');
                                  }}
                                  className="text-xs text-orange-600 hover:text-orange-700"
                                >
                                  {isMyProfile ? '管理更多商品 →' : '查看更多商品 →'}
                                </button>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-center py-3">
                            <div className="text-sm text-gray-500">
                              {isMyProfile ? '还没有上架商品' : 'TA还没有上架商品'}
                            </div>
                            {isMyProfile && (
                              <button
                                onClick={() => {
                                  window.open('/shop/products', '_blank');
                                }}
                                className="text-sm text-orange-600 hover:text-orange-700 mt-1"
                              >
                                去上架商品 →
                              </button>
                            )}
                          </div>
                        )}
                      </div>
                    );
                  } else {
                    return (
                      <div className="bg-gray-50 rounded-xl p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-lg">🏪</span>
                          <h4 className="font-medium text-gray-900">
                            {isMyProfile ? '我的店铺' : 'TA的店铺'}
                          </h4>
                        </div>
                        <div className="text-center py-3">
                          <div className="text-sm text-gray-500 mb-2">
                            {isMyProfile ? '您还没有开通店铺' : 'TA还没有开通店铺'}
                          </div>
                          {isMyProfile && (
                            <button
                              onClick={() => {
                                window.open('/shop/my-store', '_blank');
                              }}
                              className="text-sm bg-orange-500 text-white px-3 py-1 rounded-md hover:bg-orange-600 transition-colors"
                            >
                              开通店铺
                            </button>
                          )}
                        </div>
                      </div>
                    );
                  }
                })()}

                {/* 关注按钮 */}
                {currentUserId && userProfile && String(currentUserId) !== String(userProfile.userId) && (
                  <button 
                    onClick={handleFollowUser}
                    className={`w-full mt-4 py-3 rounded-lg transition-colors font-medium ${
                      userProfile?.isFollowed 
                        ? 'bg-gray-500 text-white hover:bg-gray-600' 
                        : 'bg-pink-600 text-white hover:bg-pink-700'
                    }`}
                  >
                    {userProfile?.isFollowed ? '✓ 已关注' : '+ 关注'}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 