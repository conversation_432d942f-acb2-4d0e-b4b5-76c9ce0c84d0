'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdmin } from '../../../contexts/AdminContext';
import Link from 'next/link';

// 商品数据类型定义
interface Product {
  id: number;
  productId: string;
  sellerId: string;
  sellerName: string;
  shopName: string;
  categoryId: number;
  productName: string;
  description: string;
  price: number;
  originalPrice: number;
  mainImage: string;
  images: string;
  stock: number;
  salesCount: number;
  status: string;
  statusText: string;
  isHot: boolean;
  isNew: boolean;
  createTime: string;
  updateTime: string;
}

interface ProductStats {
  totalProducts: number;
  onSaleProducts: number;
  offShelfProducts: number;
  soldOutProducts: number;
  draftProducts: number;
}

interface ApiResponse {
  code: number;
  message: string;
  data: {
    products: Product[];
    pageInfo: {
      currentPage: number;
      totalPages: number;
      totalElements: number;
      pageSize: number;
    };
    stats: ProductStats;
  };
  timestamp: number;
}

const statusOptions = ['全部', '在售', '下架', '售罄', '草稿'];
const statusMapping: { [key: string]: string } = {
  '全部': '',
  '在售': 'ON_SALE',
  '下架': 'OFF_SHELF',
  '售罄': 'SOLD_OUT',
  '草稿': 'DRAFT'
};

export default function AdminProducts() {
  const router = useRouter();
  const { admin, isLoading: adminLoading } = useAdmin();
  const [products, setProducts] = useState<Product[]>([]);
  const [stats, setStats] = useState<ProductStats>({
    totalProducts: 0,
    onSaleProducts: 0,
    offShelfProducts: 0,
    soldOutProducts: 0,
    draftProducts: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('全部');
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 模态框状态
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showProductModal, setShowProductModal] = useState(false);
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionType, setActionType] = useState('');
  const [actionReason, setActionReason] = useState('');
  
  // 图片查看相关状态
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [productImages, setProductImages] = useState<string[]>([]);
  
  // 店铺商品相关状态
  const [showShopModal, setShowShopModal] = useState(false);
  const [shopProducts, setShopProducts] = useState<Product[]>([]);
  const [selectedShopName, setSelectedShopName] = useState('');
  const [selectedSellerId, setSelectedSellerId] = useState('');
  const [shopLoading, setShopLoading] = useState(false);

  useEffect(() => {
    if (!adminLoading) {
    if (!admin) {
      router.push('/admin/login');
      } else {
        loadProducts();
      }
    }
  }, [admin, adminLoading, router, currentPage, statusFilter, searchTerm]);

  // 加载商品数据
  const loadProducts = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: currentPage.toString(),
        size: '20'
      });

      if (statusFilter && statusFilter !== '全部') {
        const mappedStatus = statusMapping[statusFilter];
        if (mappedStatus) {
          params.append('status', mappedStatus);
        }
      }

      if (searchTerm && searchTerm.trim()) {
        params.append('search', searchTerm.trim());
      }

      const response = await fetch(`http://localhost:8080/api/admin/products?${params}`);
      const result: ApiResponse = await response.json();

      if (result.code === 200) {
        setProducts(result.data.products || []);
        // 确保 stats 有值，如果后端返回的 stats 为空，则保持当前状态
        if (result.data.stats) {
          setStats(result.data.stats);
        }
        setTotalPages(result.data.pageInfo?.totalPages || 0);
        setTotalElements(result.data.pageInfo?.totalElements || 0);
      } else {
        setError(result.message || '获取商品数据失败');
      }
    } catch (err) {
      console.error('加载商品失败:', err);
      setError('网络错误，请检查后端服务是否正常运行');
    } finally {
      setLoading(false);
    }
  };

  // 搜索处理
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(0);
    loadProducts();
  };

  const handleSearchKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch(e as any);
    }
  };

  // 重置搜索
  const handleReset = () => {
    setSearchTerm('');
    setStatusFilter('全部');
    setCurrentPage(0);
  };

  // 显示商品详情
  const showProductDetails = (product: Product) => {
    setSelectedProduct(product);
    
    // 解析商品图片
    const images: string[] = [];
    console.log('商品数据:', product);
    console.log('mainImage:', product.mainImage);
    console.log('images:', product.images);
    
    // 优先处理 images 字段
    if (product.images && typeof product.images === 'string' && product.images.trim()) {
      try {
        // 检查是否为 JSON 数组格式
        if (product.images.startsWith('[') && product.images.endsWith(']')) {
          const imageArray = JSON.parse(product.images);
          if (Array.isArray(imageArray)) {
            images.push(...imageArray.filter(img => img && img.trim()));
          }
        } else if (product.images.includes(',')) {
          // 逗号分隔的字符串
          const additionalImages = product.images.split(',').map(img => img.trim()).filter(img => img);
          images.push(...additionalImages);
        } else {
          // 单个图片路径
          images.push(product.images.trim());
        }
      } catch (e) {
        console.error('解析images字段失败:', e);
        // 如果解析失败，按逗号分隔处理
        if (typeof product.images === 'string') {
          const additionalImages = product.images.split(',').map(img => img.trim()).filter(img => img);
          images.push(...additionalImages);
        }
      }
    }
    
    // 如果 mainImage 存在且不在 images 数组中，则添加到开头
    if (product.mainImage && typeof product.mainImage === 'string' && product.mainImage.trim()) {
      const mainImg = product.mainImage.trim();
      if (!images.includes(mainImg)) {
        images.unshift(mainImg);
      }
    }
    
    console.log('解析后的图片数组:', images);
    setProductImages(images);
    setSelectedImageIndex(0);
    setShowProductModal(true);
  };

  // 显示图片查看器
  const showImageViewer = (index: number) => {
    setSelectedImageIndex(index);
    setShowImageModal(true);
  };

  // 图片导航
  const navigateImage = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setSelectedImageIndex(prev => prev > 0 ? prev - 1 : productImages.length - 1);
    } else {
      setSelectedImageIndex(prev => prev < productImages.length - 1 ? prev + 1 : 0);
    }
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (showImageModal) {
        switch (e.key) {
          case 'Escape':
            setShowImageModal(false);
            break;
          case 'ArrowLeft':
            e.preventDefault();
            setSelectedImageIndex(prev => prev > 0 ? prev - 1 : productImages.length - 1);
            break;
          case 'ArrowRight':
            e.preventDefault();
            setSelectedImageIndex(prev => prev < productImages.length - 1 ? prev + 1 : 0);
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showImageModal, productImages.length]);

  // 显示操作确认框
  const showActionConfirm = (product: Product, action: string) => {
    setSelectedProduct(product);
    setActionType(action);
    setActionReason('');
    setShowActionModal(true);
  };

  // 查看店铺商品
  const viewShopProducts = async (sellerId: string, shopName: string) => {
    try {
      setShopLoading(true);
      setSelectedSellerId(sellerId);
      setSelectedShopName(shopName);
      setShowShopModal(true);

      const params = new URLSearchParams({
        page: '0',
        size: '50', // 显示更多商品
        sellerId: sellerId
      });

      const response = await fetch(`http://localhost:8080/api/admin/products?${params}`);
      const result: ApiResponse = await response.json();

      if (result.code === 200) {
        setShopProducts(result.data.products);
      } else {
        alert(result.message || '获取店铺商品失败');
      }
    } catch (err) {
      console.error('加载店铺商品失败:', err);
      alert('网络错误，请稍后重试');
    } finally {
      setShopLoading(false);
    }
  };

  // 执行商品操作
  const handleProductAction = async () => {
    if (!selectedProduct) return;

    try {
      setLoading(true);
      let url = '';
      let method = 'PUT';
      let body: any = {};

      switch (actionType) {
        case 'offline':
          url = `http://localhost:8080/api/admin/products/${selectedProduct.productId}/offline`;
          body = { reason: actionReason || '违规商品' };
          break;
          case 'online':
          url = `http://localhost:8080/api/admin/products/${selectedProduct.productId}/status`;
          body = { status: 'ON_SALE', reason: actionReason || '恢复上架' };
          break;
          case 'delete':
          url = `http://localhost:8080/api/admin/products/${selectedProduct.productId}`;
          method = 'DELETE';
          body = { reason: actionReason || '严重违规' };
          break;
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body)
      });

      const result = await response.json();

      if (result.code === 200) {
        alert(result.message || '操作成功');
        setShowActionModal(false);
        loadProducts(); // 重新加载数据
      } else {
        alert(result.message || '操作失败');
      }
    } catch (error) {
      console.error('操作失败:', error);
      alert('操作失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ON_SALE':
        return 'bg-green-500/20 text-green-400';
      case 'OFF_SHELF':
        return 'bg-red-500/20 text-red-400';
      case 'SOLD_OUT':
        return 'bg-yellow-500/20 text-yellow-400';
      case 'DRAFT':
        return 'bg-gray-500/20 text-gray-400';
          default:
        return 'bg-gray-500/20 text-gray-400';
        }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 处理图片数据，从mainImage或images字段获取图片
  const getProductImageSrc = (product: Product): string => {
    // 优先使用mainImage
    if (product.mainImage && typeof product.mainImage === 'string' && product.mainImage.trim()) {
      return product.mainImage;
    }
    
    // 如果mainImage为空，尝试从images字段解析
    if (product.images && typeof product.images === 'string' && product.images.trim()) {
      try {
        // 尝试解析JSON格式的images
        if (product.images.startsWith('[') && product.images.endsWith(']')) {
          const imageArray = JSON.parse(product.images);
          if (Array.isArray(imageArray) && imageArray.length > 0) {
            return imageArray[0];
          }
        } else if (product.images.includes(',')) {
          // 如果是逗号分隔的字符串
          return product.images.split(',')[0].trim();
        } else {
          // 如果只是单个路径
          return product.images;
        }
      } catch (e) {
        console.error('解析商品images字段失败:', e);
      }
    }
    
    return '';
  };

  // 商品图片组件
  const ProductImage = ({ src, alt, className }: { src: string; alt: string; className?: string }) => {
    const [imageError, setImageError] = useState(false);
    const [imageLoaded, setImageLoaded] = useState(false);

    const handleImageError = (e: any) => {
      setImageError(true);
    };

    const handleImageLoad = (e: any) => {
      setImageLoaded(true);
    };

    const getImageUrl = (imagePath: string) => {
      // 如果路径为空，返回空字符串
      if (!imagePath) return '';
      
      // 如果路径已经是完整URL，直接返回
      if (imagePath.startsWith('http')) {
        return imagePath;
      }

      // 去除开头多余的斜杠
      let cleanPath = imagePath;
      while (cleanPath.startsWith('/')) {
        cleanPath = cleanPath.substring(1);
      }

      // 尝试多种可能的路径格式（使用商城页面相同的逻辑）
      // 1. 如果路径包含uploads，则直接使用后端基地址
      if (cleanPath.includes('uploads/')) {
        return `http://localhost:8080/${cleanPath}`;
      }
      
      // 2. 如果路径是api/uploads格式
      if (cleanPath.includes('api/uploads/')) {
        return `http://localhost:8080/${cleanPath}`;
      }
      
      // 3. 如果路径是products/格式(直接指向产品目录)
      if (cleanPath.includes('products/')) {
        return `http://localhost:8080/uploads/${cleanPath}`;
      }

      // 默认情况，尝试作为相对路径处理
      return `http://localhost:8080/uploads/products/${cleanPath}`;
    };

    if (!src || imageError) {
      return (
        <div className={`bg-gray-600 rounded-lg flex items-center justify-center text-gray-400 ${className || 'w-12 h-12'}`}>
          📦
        </div>
      );
    }

    // 检查是否为图片查看器的大图样式
    const isLargeImage = className?.includes('max-h-[90vh]') || className?.includes('object-contain');

    return (
      <div className={`${isLargeImage ? 'bg-transparent' : 'bg-gray-600'} rounded-lg overflow-hidden ${className || 'w-12 h-12'}`}>
        <img
          src={getImageUrl(src)}
          alt={alt}
          className={`w-full h-full ${isLargeImage ? 'object-contain' : 'object-cover'}`}
          onError={handleImageError}
          onLoad={handleImageLoad}
          style={{ 
            display: imageLoaded ? 'block' : 'none',
            maxHeight: isLargeImage ? 'calc(90vh - 120px)' : undefined
          }}
        />
        {!imageLoaded && !imageError && (
          <div className="w-full h-full flex items-center justify-center text-gray-400">
            ⏳
          </div>
        )}
      </div>
    );
  };

  // 分页控件
  const PaginationControls = () => (
    <div className="flex items-center justify-between mt-6">
      <div className="text-gray-400 text-sm">
        共 {totalElements} 条商品，第 {currentPage + 1} / {totalPages} 页
      </div>
      <div className="flex space-x-2">
        <button
          onClick={() => setCurrentPage(0)}
          disabled={currentPage === 0}
          className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50"
        >
          首页
        </button>
        <button
          onClick={() => setCurrentPage(currentPage - 1)}
          disabled={currentPage === 0}
          className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50"
        >
          上一页
        </button>
        <button
          onClick={() => setCurrentPage(currentPage + 1)}
          disabled={currentPage >= totalPages - 1}
          className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50"
        >
          下一页
        </button>
        <button
          onClick={() => setCurrentPage(totalPages - 1)}
          disabled={currentPage >= totalPages - 1}
          className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50"
        >
          末页
        </button>
      </div>
    </div>
  );

  if (adminLoading) {
    return <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
    </div>;
  }

  if (!admin) {
    return null; // 会被useEffect重定向到登录页
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <nav className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-600 rounded-lg flex items-center justify-center text-white font-bold text-sm mr-3">
                管
              </Link>
              <h1 className="text-xl font-bold text-white">商品管理</h1>
            </div>
            <Link href="/admin/products/new" className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors flex items-center my-2">
              <span className="mr-2">+</span>
              添加商品
            </Link>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* 侧边栏 */}
        <aside className="w-64 bg-gray-800 min-h-screen pt-4">
          <nav className="px-4 space-y-2">
            <Link href="/admin/dashboard" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📊</span>
              仪表盘
            </Link>
            <Link href="/admin/users" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">👥</span>
              用户管理
            </Link>
            <Link href="/admin/products" className="flex items-center px-4 py-2 text-white bg-red-600 rounded-lg">
              <span className="mr-3">📦</span>
              商品管理
            </Link>
            <Link href="/admin/orders" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🛒</span>
              订单管理
            </Link>
            <Link href="/admin/customer-service" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">💬</span>
              客服中心
            </Link>
            <Link href="/admin/stores" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🏪</span>
              店铺管理
            </Link>
            <Link href="/admin/logistics" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🚚</span>
              物流管理
            </Link>
            <Link href="/admin/settings" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">⚙️</span>
              系统设置
            </Link>
          </nav>
        </aside>

        {/* 主内容区 */}
        <main className="flex-1 p-6">
          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
            <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
              <div className="text-2xl font-bold text-white">{stats?.totalProducts || 0}</div>
              <div className="text-gray-400 text-sm">商品总数</div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
              <div className="text-2xl font-bold text-green-400">{stats?.onSaleProducts || 0}</div>
              <div className="text-gray-400 text-sm">在售商品</div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
              <div className="text-2xl font-bold text-red-400">{stats?.offShelfProducts || 0}</div>
              <div className="text-gray-400 text-sm">下架商品</div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
              <div className="text-2xl font-bold text-yellow-400">{stats?.soldOutProducts || 0}</div>
              <div className="text-gray-400 text-sm">售罄商品</div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
              <div className="text-2xl font-bold text-gray-400">{stats?.draftProducts || 0}</div>
              <div className="text-gray-400 text-sm">草稿商品</div>
            </div>
          </div>

          {/* 搜索和筛选 */}
          <div className="bg-gray-800 p-4 rounded-lg border border-gray-700 mb-6">
            <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
              <div className="flex-1 min-w-64">
                <input
                  type="text"
                  placeholder="搜索商品名称..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={handleSearchKeyPress}
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-red-500 focus:outline-none"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-red-500 focus:outline-none"
              >
                {statusOptions.map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
              <button
                type="submit"
                className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
              >
                搜索
              </button>
              <button
                type="button"
                onClick={handleReset}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
              >
                重置
              </button>
            </form>
          </div>

          {/* 商品列表 */}
          <div className="bg-gray-800 rounded-lg border border-gray-700">
            <div className="p-4 border-b border-gray-700">
              <h2 className="text-lg font-semibold text-white">商品列表</h2>
            </div>

            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500 mx-auto mb-4"></div>
                <div className="text-gray-400">加载中...</div>
              </div>
            ) : error ? (
              <div className="p-8 text-center">
                <div className="text-red-400 mb-4">❌ {error}</div>
                <button
                  onClick={loadProducts}
                  className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
                >
                  重试
                </button>
              </div>
            ) : products.length === 0 ? (
              <div className="p-8 text-center text-gray-400">
                暂无商品数据
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="text-left p-4 text-gray-300">商品</th>
                      <th className="text-left p-4 text-gray-300">卖家</th>
                      <th className="text-left p-4 text-gray-300">价格</th>
                      <th className="text-left p-4 text-gray-300">库存</th>
                      <th className="text-left p-4 text-gray-300">销量</th>
                      <th className="text-left p-4 text-gray-300">状态</th>
                      <th className="text-left p-4 text-gray-300">创建时间</th>
                      <th className="text-left p-4 text-gray-300">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {products.map((product) => (
                      <tr key={product.productId} className="border-b border-gray-700 hover:bg-gray-750">
                        <td className="p-4">
                          <div className="flex items-center space-x-3">
                            <ProductImage 
                              src={getProductImageSrc(product)} 
                              alt={product.productName}
                              className="w-12 h-12 flex-shrink-0"
                            />
                            <div>
                              <div className="text-white font-medium">{product.productName}</div>
                              <div className="text-gray-400 text-sm">{product.productId}</div>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="text-white">{product.sellerName}</div>
                          <div className="text-gray-400 text-sm">
                            <button
                              onClick={() => viewShopProducts(product.sellerId, product.shopName)}
                              className="text-blue-400 hover:text-blue-300 underline"
                            >
                              {product.shopName}
                            </button>
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="text-white">¥{product.price}</div>
                          {product.originalPrice && product.originalPrice > product.price && (
                            <div className="text-gray-400 text-sm line-through">¥{product.originalPrice}</div>
                          )}
                        </td>
                        <td className="p-4 text-white">{product.stock}</td>
                        <td className="p-4 text-white">{product.salesCount}</td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(product.status)}`}>
                            {product.statusText}
                          </span>
                        </td>
                        <td className="p-4 text-gray-400 text-sm">
                          {formatDate(product.createTime)}
                        </td>
                        <td className="p-4">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => showProductDetails(product)}
                              className="px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-xs transition-colors"
                            >
                              详情
                            </button>
                            {product.status === 'ON_SALE' ? (
                              <button
                                onClick={() => showActionConfirm(product, 'offline')}
                                className="px-2 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-xs transition-colors"
                              >
                                下架
                              </button>
                            ) : (
                              <button
                                onClick={() => showActionConfirm(product, 'online')}
                                className="px-2 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-xs transition-colors"
                              >
                                上架
                              </button>
                            )}
                            <button
                              onClick={() => showActionConfirm(product, 'delete')}
                              className="px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-xs transition-colors"
                            >
                              删除
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {/* 分页控件 */}
            {products.length > 0 && <div className="p-4"><PaginationControls /></div>}
          </div>
        </main>
      </div>

      {/* 商品详情模态框 */}
      {showProductModal && selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-white">商品详情</h3>
              <button
                onClick={() => setShowProductModal(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 左侧：商品图片 */}
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-300 text-sm mb-2">商品图片</label>
                  {productImages.length > 0 ? (
                    <div className="space-y-3">
                      {/* 主图片 */}
                      <div className="relative">
                        <div 
                          className="w-full h-64 cursor-pointer hover:opacity-90 transition-opacity"
                          onClick={() => showImageViewer(0)}
                        >
                          <ProductImage 
                            src={productImages[0]}
                            alt={selectedProduct.productName}
                            className="w-full h-64"
                          />
                        </div>
                        <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                          主图
                        </div>
                      </div>
                      
                      {/* 其他图片缩略图 */}
                      {productImages.length > 1 && (
                        <div className="grid grid-cols-4 gap-2">
                          {productImages.slice(1).map((image, index) => (
                            <div
                              key={index + 1}
                              className="cursor-pointer hover:opacity-90 transition-opacity border-2 border-gray-600 hover:border-gray-400 rounded"
                              onClick={() => showImageViewer(index + 1)}
                            >
                              <ProductImage 
                                src={image}
                                alt={`${selectedProduct.productName} - 图${index + 2}`}
                                className="w-full h-16"
                              />
                            </div>
                          ))}
                        </div>
                      )}
                      
                      <div className="text-gray-400 text-xs text-center">
                        共 {productImages.length} 张图片，点击查看大图
                      </div>
                    </div>
                  ) : (
                    <div className="w-full h-64 bg-gray-700 rounded-lg flex items-center justify-center">
                      <div className="text-gray-400 text-center">
                        <div className="text-4xl mb-2">📷</div>
                        <div>暂无图片</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              {/* 右侧：商品信息 */}
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-300 text-sm mb-1">商品名称</label>
                  <div className="text-white font-medium">{selectedProduct.productName}</div>
                </div>
                
                <div>
                  <label className="block text-gray-300 text-sm mb-1">商品ID</label>
                  <div className="text-gray-400 text-sm font-mono">{selectedProduct.productId}</div>
                </div>
                
                <div>
                  <label className="block text-gray-300 text-sm mb-1">商品描述</label>
                  <div className="text-white bg-gray-700 p-3 rounded-lg max-h-32 overflow-y-auto">
                    {selectedProduct.description || '暂无描述'}
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-300 text-sm mb-1">当前价格</label>
                    <div className="text-white text-lg font-bold">¥{selectedProduct.price}</div>
                  </div>
                  <div>
                    <label className="block text-gray-300 text-sm mb-1">原价</label>
                    <div className="text-gray-400">
                      {selectedProduct.originalPrice && selectedProduct.originalPrice > selectedProduct.price 
                        ? `¥${selectedProduct.originalPrice}` 
                        : '-'
                      }
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-300 text-sm mb-1">库存</label>
                    <div className="text-white">{selectedProduct.stock}</div>
                  </div>
                  <div>
                    <label className="block text-gray-300 text-sm mb-1">已售</label>
                    <div className="text-white">{selectedProduct.salesCount}</div>
                  </div>
                </div>
                
                <div>
                  <label className="block text-gray-300 text-sm mb-1">状态</label>
                  <span className={`px-3 py-1 rounded-full text-sm ${getStatusColor(selectedProduct.status)}`}>
                    {selectedProduct.statusText}
                  </span>
                </div>
                
                <div>
                  <label className="block text-gray-300 text-sm mb-1">卖家信息</label>
                  <div className="bg-gray-700 p-3 rounded-lg">
                    <div className="text-white">{selectedProduct.sellerName}</div>
                    <div className="text-gray-400 text-sm">{selectedProduct.shopName}</div>
                    <div className="text-gray-500 text-xs">ID: {selectedProduct.sellerId}</div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <label className="block text-gray-300 text-xs mb-1">创建时间</label>
                    <div className="text-gray-400">{formatDate(selectedProduct.createTime)}</div>
                  </div>
                  <div>
                    <label className="block text-gray-300 text-xs mb-1">更新时间</label>
                    <div className="text-gray-400">{formatDate(selectedProduct.updateTime)}</div>
                  </div>
                </div>
                
                {/* 特殊标签 */}
                <div className="flex space-x-2">
                  {selectedProduct.isHot && (
                    <span className="px-2 py-1 bg-red-500 text-white text-xs rounded">热门</span>
                  )}
                  {selectedProduct.isNew && (
                    <span className="px-2 py-1 bg-green-500 text-white text-xs rounded">新品</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 操作确认模态框 */}
      {showActionModal && selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold text-white">
                {actionType === 'delete' ? '删除商品' : 
                 actionType === 'offline' ? '下架商品' : '上架商品'}
              </h3>
              <button
                onClick={() => setShowActionModal(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <div className="text-gray-300">
                确定要{actionType === 'delete' ? '删除' : 
                        actionType === 'offline' ? '下架' : '上架'}商品
                <span className="text-white font-medium">「{selectedProduct.productName}」</span>吗？
              </div>
              {(actionType === 'delete' || actionType === 'offline') && (
                <div>
                  <label className="block text-gray-300 text-sm mb-1">
                    {actionType === 'delete' ? '删除原因' : '下架原因'}
                  </label>
                  <textarea
                    value={actionReason}
                    onChange={(e) => setActionReason(e.target.value)}
                    placeholder={actionType === 'delete' ? '请输入删除原因...' : '请输入下架原因...'}
                    className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-red-500 focus:outline-none"
                    rows={3}
                  />
                </div>
              )}
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowActionModal(false)}
                  className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleProductAction}
                  className={`flex-1 px-4 py-2 rounded-lg transition-colors text-white ${
                    actionType === 'delete' ? 'bg-red-500 hover:bg-red-600' :
                    actionType === 'offline' ? 'bg-red-500 hover:bg-red-600' :
                    'bg-green-500 hover:bg-green-600'
                  }`}
                >
                  确定
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 店铺商品模态框 */}
      {showShopModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-6xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold text-white">
                店铺商品管理 - {selectedShopName}
              </h3>
              <button
                onClick={() => setShowShopModal(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            
            {shopLoading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500 mx-auto mb-4"></div>
                <div className="text-gray-400">加载中...</div>
              </div>
            ) : shopProducts.length === 0 ? (
              <div className="p-8 text-center text-gray-400">
                该店铺暂无商品
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="text-left p-4 text-gray-300">商品</th>
                      <th className="text-left p-4 text-gray-300">价格</th>
                      <th className="text-left p-4 text-gray-300">库存</th>
                      <th className="text-left p-4 text-gray-300">销量</th>
                      <th className="text-left p-4 text-gray-300">状态</th>
                      <th className="text-left p-4 text-gray-300">创建时间</th>
                      <th className="text-left p-4 text-gray-300">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {shopProducts.map((product) => (
                      <tr key={product.productId} className="border-b border-gray-700 hover:bg-gray-750">
                        <td className="p-4">
                          <div className="flex items-center space-x-3">
                            <ProductImage 
                              src={getProductImageSrc(product)} 
                              alt={product.productName}
                              className="w-12 h-12 flex-shrink-0"
                            />
                            <div>
                              <div className="text-white font-medium">{product.productName}</div>
                              <div className="text-gray-400 text-sm">{product.productId}</div>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="text-white">¥{product.price}</div>
                          {product.originalPrice && product.originalPrice > product.price && (
                            <div className="text-gray-400 text-sm line-through">¥{product.originalPrice}</div>
                          )}
                        </td>
                        <td className="p-4 text-white">{product.stock}</td>
                        <td className="p-4 text-white">{product.salesCount}</td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(product.status)}`}>
                            {product.statusText}
                          </span>
                        </td>
                        <td className="p-4 text-gray-400 text-sm">
                          {formatDate(product.createTime)}
                        </td>
                        <td className="p-4">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => {
                                setShowShopModal(false);
                                showProductDetails(product);
                              }}
                              className="px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-xs transition-colors"
                            >
                              详情
                            </button>
                            {product.status === 'ON_SALE' ? (
                              <button
                                onClick={() => {
                                  setShowShopModal(false);
                                  showActionConfirm(product, 'offline');
                                }}
                                className="px-2 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-xs transition-colors"
                              >
                                下架
                              </button>
                            ) : (
                              <button
                                onClick={() => {
                                  setShowShopModal(false);
                                  showActionConfirm(product, 'online');
                                }}
                                className="px-2 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-xs transition-colors"
                              >
                                上架
                              </button>
                            )}
                            <button
                              onClick={() => {
                                setShowShopModal(false);
                                showActionConfirm(product, 'delete');
                              }}
                              className="px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-xs transition-colors"
                            >
                              删除
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            
            <div className="mt-4 text-center">
              <div className="text-gray-400 text-sm">
                共找到 {shopProducts.length} 件商品
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 图片查看器模态框 */}
      {showImageModal && productImages.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-[60]">
          <div className="relative max-w-7xl max-h-[90vh] mx-4">
            {/* 关闭按钮 */}
            <button
              onClick={() => setShowImageModal(false)}
              className="absolute top-4 right-4 z-10 w-10 h-10 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full flex items-center justify-center transition-all"
            >
              ✕
            </button>
            
            {/* 图片导航按钮 - 左 */}
            {productImages.length > 1 && (
              <button
                onClick={() => navigateImage('prev')}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 w-12 h-12 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full flex items-center justify-center transition-all"
              >
                ‹
              </button>
            )}
            
            {/* 图片导航按钮 - 右 */}
            {productImages.length > 1 && (
              <button
                onClick={() => navigateImage('next')}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 w-12 h-12 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full flex items-center justify-center transition-all"
              >
                ›
              </button>
            )}
            
            {/* 主图片 */}
            <div className="flex items-center justify-center">
              <ProductImage 
                src={productImages[selectedImageIndex]}
                alt={`${selectedProduct?.productName} - 图片 ${selectedImageIndex + 1}`}
                className="max-w-full max-h-[90vh] object-contain"
              />
            </div>
            
            {/* 图片信息和缩略图 */}
            <div className="absolute bottom-4 left-0 right-0">
              <div className="bg-black bg-opacity-50 text-white p-4 rounded-lg mx-4">
                <div className="text-center mb-3">
                  <div className="text-lg font-medium">{selectedProduct?.productName}</div>
                  <div className="text-sm text-gray-300">
                    图片 {selectedImageIndex + 1} / {productImages.length}
                  </div>
                </div>
                
                {/* 缩略图导航 */}
                {productImages.length > 1 && (
                  <div className="flex justify-center space-x-2 overflow-x-auto pb-2">
                    {productImages.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedImageIndex(index)}
                        className={`flex-shrink-0 w-16 h-16 rounded border-2 transition-all ${
                          index === selectedImageIndex 
                            ? 'border-red-500 opacity-100' 
                            : 'border-gray-400 opacity-60 hover:opacity-80'
                        }`}
                      >
                        <ProductImage 
                          src={image}
                          alt={`缩略图 ${index + 1}`}
                          className="w-full h-full"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 