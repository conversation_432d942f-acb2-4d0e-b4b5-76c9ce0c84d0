'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function RegisterPage() {
  const [step, setStep] = useState(1); // 1: 输入账号, 2: 验证码, 3: 设置用户信息
  const [account, setAccount] = useState(''); // 邮箱或手机号
  const [accountType, setAccountType] = useState<'email' | 'phone'>('email');
  const [verificationCode, setVerificationCode] = useState('');
  const [username, setUsername] = useState('');
  const [nickname, setNickname] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const router = useRouter();

  // 检测输入的是邮箱还是手机号
  const detectAccountType = (value: string) => {
    if (value.includes('@')) {
      setAccountType('email');
    } else if (/^1[3-9]\d{9}$/.test(value)) {
      setAccountType('phone');
    }
  };

  // 发送验证码
  const sendVerificationCode = async () => {
    if (!account) {
      setError('请输入邮箱或手机号');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // 1. 先检查邮箱/手机号是否已注册
      if (accountType === 'email') {
        console.log('🔍 检查邮箱是否已注册:', account);
        const checkResponse = await fetch(`http://localhost:8080/api/auth/check-email?email=${encodeURIComponent(account)}`);
        const checkResult = await checkResponse.json();
        console.log('📧 邮箱检查结果:', checkResult);
        
        if (checkResult.code === 200 && !checkResult.data) {
          setError('该邮箱已被注册，请使用其他邮箱或直接登录');
          setIsLoading(false);
          return;
        }
      } else {
        console.log('🔍 检查手机号是否已注册:', account);
        const checkResponse = await fetch(`http://localhost:8080/api/auth/check-phone?phoneNumber=${encodeURIComponent(account)}`);
        const checkResult = await checkResponse.json();
        console.log('📱 手机号检查结果:', checkResult);
        
        if (checkResult.code === 200 && !checkResult.data) {
          setError('该手机号已被注册，请使用其他手机号或直接登录');
          setIsLoading(false);
          return;
        }
      }

      // 2. 发送验证码
      const endpoint = accountType === 'email' ? '/api/auth/send-email-code' : '/api/auth/send-sms-code';
      const paramName = accountType === 'email' ? 'email' : 'phoneNumber';
      const url = `http://localhost:8080${endpoint}?${paramName}=${encodeURIComponent(account)}`;
      
      console.log('🚀 发送验证码请求:', {
        url,
        method: 'POST',
        accountType,
        account,
        endpoint,
        paramName
      });
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('📡 响应状态:', response.status, response.statusText);
      
      const result = await response.json();
      console.log('📨 响应数据:', result);
      
      if (result.code === 200) {
        setCodeSent(true);
        setSuccess(`验证码已发送到您的${accountType === 'email' ? '邮箱' : '手机'}`);
        setStep(2);
        
        // 开始倒计时
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        console.error('❌ 发送失败:', result);
        setError(result.message || '发送失败');
      }
    } catch (error) {
      console.error('🚨 网络错误:', error);
      setError(`网络错误: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 验证验证码（第二步）
  const verifyCode = async () => {
    if (!verificationCode) {
      setError('请输入验证码');
      return;
    }

    if (verificationCode.length !== 6) {
      setError('请输入6位验证码');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const requestData = {
        [accountType === 'email' ? 'email' : 'phoneNumber']: account,
        code: verificationCode,
        codeType: accountType === 'email' ? 'EMAIL_REGISTER' : 'PHONE_REGISTER'
      };
      
      console.log('发送验证码验证请求:', requestData);

      // 调用后端验证验证码
      const response = await fetch('http://localhost:8080/api/auth/verify-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();
      console.log('验证码验证响应:', result);
      
      if (result.code === 200) {
        // 验证码正确，跳转到第三步
        setSuccess('验证码验证成功！');
        setTimeout(() => {
          setSuccess('');
          setStep(3);
        }, 1000);
      } else {
        setError(result.message || '验证码错误，请重新输入');
      }
    } catch (error) {
      console.error('验证验证码失败:', error);
      setError('验证失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 最终注册（第三步）
  const handleRegister = async () => {
    // 表单验证
    if (!username.trim()) {
      setError('请输入用户名');
      return;
    }
    if (username.length < 2 || username.length > 10) {
      setError('用户名长度必须在2-10个字符之间');
      return;
    }
    if (!nickname.trim()) {
      setError('请输入昵称');
      return;
    }
    if (nickname.length > 100) {
      setError('昵称长度不能超过100个字符');
      return;
    }
    if (!password) {
      setError('请输入密码');
      return;
    }
    if (password.length < 8 || password.length > 20) {
      setError('密码长度必须在8-20个字符之间');
      return;
    }
    // 密码强度验证
    if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,20}$/.test(password)) {
      setError('密码必须包含大写字母、小写字母和数字');
      return;
    }
    if (password !== confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // 检查用户名是否已存在
      console.log('🔍 检查用户名是否可用:', username);
      const usernameCheckResponse = await fetch(`http://localhost:8080/api/auth/check-username?username=${encodeURIComponent(username.trim())}`);
      const usernameCheckResult = await usernameCheckResponse.json();
      console.log('👤 用户名检查结果:', usernameCheckResult);
      
      if (usernameCheckResult.code === 200 && !usernameCheckResult.data) {
        setError('该用户名已被使用，请选择其他用户名');
        setIsLoading(false);
        return;
      }

      const registerData = {
        [accountType === 'email' ? 'email' : 'phoneNumber']: account,
        username: username.trim(),
        nickname: nickname.trim(),
        password,
        confirmPassword,
        verificationCode
      };

      console.log('📝 发送注册请求:', { ...registerData, password: '***', confirmPassword: '***' });

      const response = await fetch('http://localhost:8080/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registerData),
      });

      const result = await response.json();
      console.log('✅ 注册响应:', result);
      
      if (result.code === 200) {
        setSuccess('注册成功！即将跳转到仪表盘...');
        
        // 保存用户信息到localStorage
        const userData = result.data;
        const user = {
          id: userData.userId,
          username: userData.username,
          nickname: userData.nickname,
          email: userData.email,
          avatar: userData.avatarPath
        };
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('token', userData.accessToken);
        
        // 标记为首次注册用户，首次登录仪表盘时会显示阅读须知
        localStorage.setItem('isFirstTimeUser', 'true');

        // 跳转到仪表盘
        setTimeout(() => {
          window.location.href = '/dashboard';
        }, 2000);
      } else {
        setError(result.message || '注册失败');
      }
    } catch (error) {
      console.error('🚨 注册失败:', error);
      setError('注册失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <label htmlFor="account" className="block text-sm font-medium text-white mb-2">
          邮箱/手机号
        </label>
        <input
          type="text"
          id="account"
          value={account}
          onChange={(e) => {
            setAccount(e.target.value);
            detectAccountType(e.target.value);
          }}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
          placeholder="请输入您的邮箱或手机号"
          required
        />
        {account && (
          <p className="text-sm text-gray-400 mt-2">
            检测到：{accountType === 'email' ? '邮箱地址' : '手机号码'}
          </p>
        )}
      </div>

      <button
        type="button"
        onClick={sendVerificationCode}
        disabled={isLoading || !account}
        className="w-full py-3 bg-gradient-to-r from-cyan-500 to-purple-600 text-white rounded-lg font-medium hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300 hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? (
          <span className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
            发送中...
          </span>
        ) : (
          `发送验证码到${accountType === 'email' ? '邮箱' : '手机'}`
        )}
      </button>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <p className="text-gray-300">
          验证码已发送到：
          <span className="text-purple-400 font-medium">{account}</span>
        </p>
      </div>

      <div>
        <label htmlFor="verificationCode" className="block text-sm font-medium text-white mb-2">
          验证码
        </label>
        <input
          type="text"
          id="verificationCode"
          value={verificationCode}
          onChange={(e) => setVerificationCode(e.target.value)}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
          placeholder="请输入6位验证码"
          maxLength={6}
          required
        />
      </div>

      <div className="flex gap-3">
        <button
          type="button"
          onClick={verifyCode}
          disabled={!verificationCode || isLoading}
          className="flex-1 py-3 bg-gradient-to-r from-cyan-500 to-purple-600 text-white rounded-lg font-medium hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300 hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <span className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              验证中...
            </span>
          ) : (
            '验证并下一步'
          )}
        </button>
        
        <button
          type="button"
          onClick={sendVerificationCode}
          disabled={countdown > 0 || isLoading}
          className="px-6 py-3 bg-gray-600 text-white rounded-lg font-medium hover:bg-gray-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {countdown > 0 ? `${countdown}s` : '重发'}
        </button>
      </div>

      <button
        type="button"
        onClick={() => setStep(1)}
        className="w-full py-2 text-gray-400 hover:text-gray-300 transition-colors duration-300"
      >
        返回上一步
      </button>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-5">
      <div>
        <label htmlFor="username" className="block text-sm font-medium text-white mb-2">
          用户名
        </label>
        <input
          type="text"
          id="username"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
          placeholder="请输入用户名（2-10个字符）"
          required
        />
        <p className="text-xs text-gray-500 mt-1">用于登录和展示，可包含字母、数字和下划线</p>
      </div>

      <div>
        <label htmlFor="nickname" className="block text-sm font-medium text-white mb-2">
          昵称
        </label>
        <input
          type="text"
          id="nickname"
          value={nickname}
          onChange={(e) => setNickname(e.target.value)}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
          placeholder="请输入您的昵称"
          required
        />
        <p className="text-xs text-gray-500 mt-1">显示给其他用户的名称，可以使用中文</p>
      </div>

      <div>
        <label htmlFor="password" className="block text-sm font-medium text-white mb-2">
          密码
        </label>
        <input
          type="password"
          id="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
          placeholder="请输入密码（8-20位）"
          required
        />
        <p className="text-xs text-gray-500 mt-1">必须包含大写字母、小写字母和数字</p>
      </div>

      <div>
        <label htmlFor="confirmPassword" className="block text-sm font-medium text-white mb-2">
          确认密码
        </label>
        <input
          type="password"
          id="confirmPassword"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
          placeholder="请再次输入密码"
          required
        />
      </div>

      <button
        type="button"
        onClick={handleRegister}
        disabled={isLoading || !username.trim() || !nickname.trim() || !password || !confirmPassword}
        className="w-full py-3 bg-gradient-to-r from-cyan-500 to-purple-600 text-white rounded-lg font-medium hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300 hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? (
          <span className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
            注册中...
          </span>
        ) : (
          '完成注册'
        )}
      </button>

      <button
        type="button"
        onClick={() => setStep(2)}
        className="w-full py-2 text-gray-400 hover:text-gray-300 transition-colors duration-300"
      >
        返回上一步
      </button>
    </div>
  );

  return (
    <div className="min-h-screen bg-[#1a1b26] relative overflow-hidden">
      {/* 背景渐变效果 */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-[#1a1b26] via-[#1e1b3a] to-[#1a1b26]"></div>
        <div className="absolute top-1/4 left-1/4 w-[600px] h-[600px] bg-cyan-400 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-blob"></div>
        <div className="absolute top-1/3 right-1/4 w-[500px] h-[500px] bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl opacity-15 animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-[550px] h-[550px] bg-pink-500 rounded-full mix-blend-multiply filter blur-3xl opacity-12 animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative z-10 flex items-center justify-center min-h-screen px-4 py-8">
        {/* 返回首页按钮 */}
        <Link 
          href="/" 
          className="absolute top-8 left-8 text-gray-400 hover:text-white transition-colors flex items-center space-x-2"
        >
          <span>←</span>
          <span>返回首页</span>
        </Link>

        <div className="w-full max-w-md">
          <div className="bg-gray-900/50 backdrop-blur-lg rounded-2xl p-8 border border-gray-700">
            {/* Logo和标题 */}
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4">
                灵
              </div>
              <h1 className="text-2xl font-bold text-white mb-2">创建账户</h1>
              <p className="text-gray-400">
                {step === 1 && '输入邮箱或手机号开始注册'}
                {step === 2 && '验证您的身份'}
                {step === 3 && '完善个人信息'}
              </p>
            </div>

            {/* 步骤指示器 */}
            <div className="flex justify-center mb-8">
              <div className="flex items-center space-x-4">
                {[1, 2, 3].map((stepNum) => (
                  <div key={stepNum} className="flex items-center">
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                        step >= stepNum
                          ? 'bg-purple-500 text-white'
                          : 'bg-gray-600 text-gray-400'
                      }`}
                    >
                      {stepNum}
                    </div>
                    {stepNum < 3 && (
                      <div
                        className={`w-8 h-0.5 mx-2 ${
                          step > stepNum ? 'bg-purple-500' : 'bg-gray-600'
                        }`}
                      />
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-6">
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            )}

            {/* 成功提示 */}
            {success && (
              <div className="bg-green-500/20 border-2 border-green-500/50 rounded-lg p-6 mb-6 animate-pulse shadow-lg shadow-green-500/25">
                <div className="flex items-center justify-center">
                  <div className="w-8 h-8 bg-green-500 rounded-full mr-4 flex items-center justify-center animate-bounce">
                    <span className="text-white text-sm font-bold">✓</span>
                  </div>
                  <div>
                    <p className="text-green-300 font-bold text-lg">{success}</p>
                    <div className="w-32 h-1 bg-green-500/30 rounded-full mt-2 overflow-hidden">
                      <div className="h-full bg-green-500 rounded-full animate-pulse" style={{width: '100%'}}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 注册步骤 */}
            {step === 1 && renderStep1()}
            {step === 2 && renderStep2()}
            {step === 3 && renderStep3()}

            <div className="mt-6 text-center">
              <div className="text-gray-400 text-sm mb-4">或</div>
              <p className="text-gray-400 text-sm">
                已有账户？
                <Link href="/login" className="text-purple-400 hover:text-purple-300 ml-1">
                  立即登录
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes blob {
          0% { transform: translate(0px, 0px) scale(1); }
          33% { transform: translate(30px, -50px) scale(1.1); }
          66% { transform: translate(-20px, 20px) scale(0.9); }
          100% { transform: translate(0px, 0px) scale(1); }
        }
        .animate-blob {
          animation: blob 7s infinite;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
      `}</style>
    </div>
  );
} 