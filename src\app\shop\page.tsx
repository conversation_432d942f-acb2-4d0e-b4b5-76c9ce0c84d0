﻿'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import ImageWithFallback from '@/components/ui/ImageWithFallback';

// API基础URL
const API_BASE_URL = 'http://localhost:8080/api';

// 辅助函数：生成商品图片的完整URL
function getProductImageUrl(path: string): string {
  // 如果路径为空，返回默认图片
  if (!path) return '/public/images/placeholders/image-placeholder.png';
  
  // 如果路径已经是完整URL，直接返回
  if (path.startsWith('http')) return path;

  // 去除开头多余的斜杠
  let cleanPath = path;
  while (cleanPath.startsWith('/')) {
    cleanPath = cleanPath.substring(1);
  }

  // 尝试多种可能的路径格式
  // 1. 如果路径包含uploads，则直接使用后端基地址
  if (cleanPath.includes('uploads/')) {
    const baseUrl = 'http://localhost:8080';
    console.log(`处理商城图片路径(uploads): ${path} -> ${baseUrl}/${cleanPath}`);
    return `${baseUrl}/${cleanPath}`;
  }
  
  // 2. 如果路径是api/uploads格式
  if (cleanPath.includes('api/uploads/')) {
    const baseUrl = 'http://localhost:8080';
    console.log(`处理商城图片路径(api/uploads): ${path} -> ${baseUrl}/${cleanPath}`);
    return `${baseUrl}/${cleanPath}`;
  }
  
  // 3. 如果路径是products/格式(直接指向产品目录)
  if (cleanPath.includes('products/')) {
    const baseUrl = 'http://localhost:8080/uploads';
    console.log(`处理商城图片路径(products): ${path} -> ${baseUrl}/${cleanPath}`);
    return `${baseUrl}/${cleanPath}`;
  }

  // 默认情况，尝试作为相对路径处理
  const fullUrl = `http://localhost:8080/uploads/products/${cleanPath}`;
  console.log(`处理商城默认图片路径: ${path} -> ${fullUrl}`);
  return fullUrl;
}

// 商品接口类型定义
interface ApiProduct {
  id: string;
  productId: string;
  shopId: string;
  name: string;
  description: string;
  price: number;
  stock: number;
  category: string;
  images: string[];
  status: string;
  salesCount: number;
  viewCount: number;
  createTime: string;
  updateTime: string;
  shopName?: string;
  shopLogo?: string;
}

  // 转换API数据为组件使用的格式
const convertApiProductToLocal = (apiProduct: ApiProduct) => {
  // 计算真实的原价和折扣 - 暂时使用20%markup作为原价
  const price = Math.floor(apiProduct.price);
  const originalPrice = Math.floor(price * 1.2); // 暂时假设20%折扣
  const discountPercent = 17; // 暂时固定折扣为17%
  
  // 确保图片路径是数组
  let images = [];
  if (Array.isArray(apiProduct.images)) {
    images = apiProduct.images;
  } else if (typeof apiProduct.images === 'string') {
    try {
      // 尝试解析JSON字符串
      const parsedImages = JSON.parse(apiProduct.images);
      images = Array.isArray(parsedImages) ? parsedImages : [];
    } catch (e) {
      console.error('解析商品图片JSON失败:', e);
      images = [];
    }
  }
  
  // 将API产品数据转换为本地产品数据结构
  console.log('转换商品数据:', apiProduct);
  console.log('处理后的图片数组:', images);
  
  return {
    id: parseInt(apiProduct.id),
    productId: apiProduct.productId, // 保留业务ID，这是后端购物车API需要的
    name: apiProduct.name,
    price: price,
    originalPrice: originalPrice,
    category: apiProduct.category || 'GMK',
    style: '现代简约', // 可以后续从数据库获取
    keyCount: 104, // 可以后续从数据库获取
    layout: '全键盘', // 可以后续从数据库获取
    isHot: apiProduct.salesCount > 10, // 降低热销门槛
    isNew: new Date(apiProduct.createTime) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    discount: discountPercent,
    likes: apiProduct.viewCount || 0, // 使用浏览数作为点赞数
    sales: apiProduct.salesCount || 0, // 使用真实销量
    shopName: apiProduct.shopName || '官方店铺',
    images: images // 保存商品图片数组
  };
};

// 本地商品类型定义
type LocalProduct = ReturnType<typeof convertApiProductToLocal> & {
  designerId?: number;
  designer?: string;
  isDesignerProduct?: boolean;
  images?: string[]; // 添加商品图片路径数组
};

// 模拟入驻设计师数据
const designersData = [
  {
    id: 1,
    name: '张艺设计师',
    avatar: '👨‍🎨',
    specialty: '现代简约风格',
    experience: '5年经验',
    rating: 4.9,
    followers: 1520,
    description: '专注现代简约风格键帽设计，擅长极简配色与工业美学结合'
  },
  {
    id: 2,
    name: '李创意工作室',
    avatar: '👩‍🎨',  
    specialty: '艺术创意系列',
    experience: '8年经验',
    rating: 4.8,
    followers: 2340,
    description: '国内知名键帽设计工作室，主攻艺术创意与手工定制系列'
  },
  {
    id: 3,
    name: 'KeyArt团队',
    avatar: '🎯',
    specialty: '电竞游戏风格', 
    experience: '6年经验',
    rating: 4.7,
    followers: 1890,
    description: '专业电竞键帽设计团队，为多个知名战队提供定制服务'
  },
  {
    id: 4,
    name: '古风设计师',
    avatar: '🏮',
    specialty: '中国风复古',
    experience: '7年经验',
    rating: 4.9, 
    followers: 1670,
    description: '传统文化与现代科技完美结合，打造独具匠心的中国风键帽'
  }
];

// 设计师作品数据
const designerProducts = [
  // 张艺设计师作品
  {
    id: 101,
    productId: 'DESIGNER-101',
    designerId: 1,
    name: '极简白·张艺系列',
    price: 899,
    originalPrice: 1099,
    category: 'Designer',
    style: '现代简约',
    keyCount: 104,
    layout: '全键盘',
    discount: 18,
    likes: 456,
    sales: 89,
    designer: '张艺设计师',
    isDesignerProduct: true,
    isHot: false,
    isNew: false,
    shopName: '张艺设计师',
    images: ['/images/placeholders/image-placeholder.png']
  },
  {
    id: 102,
    productId: 'DESIGNER-102',
    designerId: 1, 
    name: '工业美学·机械元素',
    price: 1299,
    originalPrice: 1499,
    category: 'Designer',
    style: '现代简约',
    keyCount: 87,
    layout: '紧凑型',
    discount: 13,
    likes: 320,
    sales: 67,
    designer: '张艺设计师',
    isDesignerProduct: true,
    isHot: false,
    isNew: false,
    shopName: '张艺设计师',
    images: ['/images/placeholders/image-placeholder.png']
  },
  // 李创意工作室作品
  {
    id: 103,
    productId: 'DESIGNER-103',
    designerId: 2,
    name: '梦境森林·李创意系列',
    price: 1599,
    originalPrice: 1899,
    category: 'Designer',
    style: '艺术创意',
    keyCount: 61,
    layout: '60%',
    discount: 16,
    likes: 789,
    sales: 123,
    designer: '李创意工作室',
    isDesignerProduct: true,
    isHot: true,
    isNew: true,
    shopName: '李创意工作室',
    images: [
      '/images/placeholders/image-placeholder.png',
      '/images/placeholders/image-placeholder.png',
      '/images/placeholders/image-placeholder.png'
    ]
  },
  {
    id: 104,
    productId: 'DESIGNER-104',
    designerId: 2,
    name: '星空物语·手工定制',
    price: 2299,
    originalPrice: 2599,
    category: 'Designer',
    style: '艺术创意',
    keyCount: 37,
    layout: '40%',
    discount: 12,
    likes: 654,
    sales: 78,
    designer: '李创意工作室',
    isDesignerProduct: true,
    isHot: false,
    isNew: false,
    shopName: '李创意工作室',
    images: ['/images/placeholders/image-placeholder.png']
  },
  // KeyArt团队作品
  {
    id: 105,
    productId: 'DESIGNER-105',
    designerId: 3,
    name: '电竞之魂·战队定制',
    price: 1799,
    originalPrice: 2099,
    category: 'Designer',
    style: '电竞游戏',
    keyCount: 80,
    layout: '80%',
    discount: 14,
    likes: 567,
    sales: 156,
    designer: 'KeyArt团队',
    isDesignerProduct: true,
    isHot: true,
    isNew: false,
    shopName: 'KeyArt团队',
    images: [
      '/images/placeholders/image-placeholder.png',
      '/images/placeholders/image-placeholder.png',
      '/images/placeholders/image-placeholder.png',
      '/images/placeholders/image-placeholder.png'
    ]
  },
  {
    id: 106,
    productId: 'DESIGNER-106',
    designerId: 3,
    name: '竞技光影·RGB系列',
    price: 1399,
    originalPrice: 1699,
    category: 'Designer',
    style: '电竞游戏',
    keyCount: 104,
    layout: '全键盘',
    discount: 18,
    likes: 432,
    sales: 203,
    designer: 'KeyArt团队',
    isDesignerProduct: true,
    isHot: false,
    isNew: false,
    shopName: 'KeyArt团队',
    images: ['/images/placeholders/image-placeholder.png']
  },
  // 古风设计师作品
  {
    id: 107,
    productId: 'DESIGNER-107',
    designerId: 4,
    name: '诗词雅韵·古风系列',
    price: 1899,
    originalPrice: 2199,
    category: 'Designer',
    style: '复古经典',
    keyCount: 104,
    layout: '全键盘',
    discount: 14,
    likes: 698,
    sales: 145,
    designer: '古风设计师',
    isDesignerProduct: true,
    isHot: true,
    isNew: false,
    shopName: '古风设计师',
    images: [
      '/images/placeholders/image-placeholder.png',
      '/images/placeholders/image-placeholder.png',
      '/images/placeholders/image-placeholder.png',
      '/images/placeholders/image-placeholder.png',
      '/images/placeholders/image-placeholder.png'
    ]
  },
  {
    id: 108,
    productId: 'DESIGNER-108',
    designerId: 4,
    name: '山水画境·水墨键帽',
    price: 2599,
    originalPrice: 2999,
    category: 'Designer',
    style: '复古经典',
    keyCount: 87,
    layout: '紧凑型',
    discount: 13,
    likes: 789,
    sales: 89,
    designer: '古风设计师',
    isDesignerProduct: true,
    isHot: false,
    isNew: false,
    shopName: '古风设计师',
    images: ['/images/placeholders/image-placeholder.png']
  }
];

const categories = [
  { id: 'all', name: '全部', icon: '🏪' },
  { id: 'GMK', name: 'GMK', icon: '🎯' },
  { id: 'SA', name: 'SA', icon: '🏎️' },
  { id: 'Jellykey', name: 'Jellykey', icon: '💎' },
  { id: 'PBT', name: 'PBT', icon: '🎨' },
  { id: 'Wooting', name: 'Wooting', icon: '⚡' }
];

const styles = [
  { id: 'all', name: '全部风格', icon: '🌈' },
  { id: '现代简约', name: '现代简约', icon: '⚪' },
  { id: '赛车主题', name: '赛车主题', icon: '🏁' },
  { id: '艺术创意', name: '艺术创意', icon: '🎨' },
  { id: '电竞游戏', name: '电竞游戏', icon: '🎮' },
  { id: '复古经典', name: '复古经典', icon: '📻' },
  { id: '高对比', name: '高对比', icon: '🔆' }
];

const keyCountRanges = [
  { id: 'all', name: '全部键数', icon: '⌨️' },
  { id: '40', name: '40% (30-45键)', icon: '🔸', min: 30, max: 45 },
  { id: '60', name: '60% (55-65键)', icon: '🔹', min: 55, max: 65 },
  { id: '80', name: '80% (75-85键)', icon: '🔷', min: 75, max: 85 },
  { id: '87', name: '紧凑型 (85-90键)', icon: '🔶', min: 85, max: 90 },
  { id: '104', name: '全键盘 (100+键)', icon: '⬛', min: 100, max: 200 }
];

export default function ShopPage() {
  const router = useRouter();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStyle, setSelectedStyle] = useState('all');
  const [selectedKeyCount, setSelectedKeyCount] = useState('all');
  const [filteredProducts, setFilteredProducts] = useState<LocalProduct[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [showOrderMenu, setShowOrderMenu] = useState(false);
  const [selectedProductCategory, setSelectedProductCategory] = useState('all');
  const [selectedDesigner, setSelectedDesigner] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [apiProducts, setApiProducts] = useState<LocalProduct[]>([]);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewProduct, setPreviewProduct] = useState<LocalProduct | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [cart, setCart] = useState<{id: number, quantity: number}[]>([]);

  // 从API获取真实商品数据
  const fetchProducts = async () => {
    try {
      setLoading(true);
      console.log('正在获取商品数据...');
      
      const response = await fetch(`${API_BASE_URL}/products`);
      if (response.ok) {
        const result = await response.json();
        console.log('API响应:', result);
        
        let productData = null;
        
        // 兼容多种API响应格式
        if (result.success && result.data && Array.isArray(result.data)) {
          // 格式1: {success: true, data: [...]}
          productData = result.data;
        } else if (result.code === 200 && result.data && Array.isArray(result.data)) {
          // 格式2: {code: 200, message: "操作成功", data: [...]}
          productData = result.data;
        } else if (Array.isArray(result)) {
          // 格式3: 直接返回数组
          productData = result;
        }
        
        if (productData && Array.isArray(productData)) {
          console.log('原始商品数据:', productData);
          const convertedProducts = productData.map(convertApiProductToLocal);
          setApiProducts(convertedProducts);
          console.log('转换后的商品数据:', convertedProducts);
          console.log('API商品数量:', convertedProducts.length);
        } else {
          console.warn('API返回数据格式不正确:', result);
          console.warn('productData:', productData);
          setApiProducts([]);
        }
      } else {
        console.error('获取商品失败，状态码:', response.status);
        setApiProducts([]);
      }
    } catch (error) {
      console.error('获取商品数据失败:', error);
      setApiProducts([]);
    } finally {
      setLoading(false);
    }
  };

  // 预览商品
  const handlePreviewProduct = (product: LocalProduct) => {
    console.log('🔍 预览商品:', product.name);
    console.log('📷 商品图片数组:', (product as any).images);
    console.log('📷 图片数量:', (product as any).images?.length || 0);
    if ((product as any).images?.length > 0) {
      (product as any).images.forEach((img: string, idx: number) => {
        console.log(`图片 ${idx + 1}:`, img, '->', getProductImageUrl(img));
      });
    }
    setPreviewProduct(product);
    setCurrentImageIndex(0); // 重置图片索引
    setShowPreviewModal(true);
  };

  // 获取token
  const getToken = () => {
    return localStorage.getItem('token');
  };

  // 立即购买
  const handleBuyNow = (productId: number, quantity: number = 1) => {
    const token = getToken();
    if (!token) {
      alert('请先登录！');
      return;
    }
    
    // 跳转到立即购买页面
    router.push(`/buy-now?productId=${productId}&quantity=${quantity}`);
  };

  // 添加到购物车
  const handleAddToCart = async (productId: number) => {
    try {
      const token = getToken();
      
      if (!token) {
        alert('请先登录！');
        // 可以重定向到登录页面
        return;
      }

      // 找到对应的商品信息
      const product = filteredProducts.find(p => p.id === productId);
      if (!product) {
        alert('商品信息错误！');
        return;
      }

      // 使用商品的业务ID（productId）而不是数字ID
      const businessProductId = product.productId;

      console.log('🛒 准备添加到购物车:');
      console.log('🛒 商品数字ID:', productId);
      console.log('🛒 商品业务ID:', businessProductId);
      console.log('🛒 商品信息:', product);

      const response = await fetch(`${API_BASE_URL}/cart`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: businessProductId, // 使用业务ID
          quantity: 1,
          selected: true
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('🛒 添加到购物车成功:', result);
        console.log('🛒 使用的商品ID:', businessProductId);
        alert('商品已添加到购物车！');
        
        // 更新本地购物车状态（保持原有功能）
        setCart(prevCart => {
          const existingItem = prevCart.find(item => item.id === productId);
          if (existingItem) {
            return prevCart.map(item =>
              item.id === productId ? { ...item, quantity: item.quantity + 1 } : item
            );
          } else {
            return [...prevCart, { id: productId, quantity: 1 }];
          }
        });
      } else if (response.status === 401) {
        alert('登录已过期，请重新登录！');
        localStorage.removeItem('token');
      } else {
        const error = await response.json();
        console.error('🛒 添加到购物车失败:', error);
        alert(error.message || '添加到购物车失败，请重试！');
      }
    } catch (error) {
      console.error('添加到购物车失败:', error);
      alert('添加到购物车失败，请重试！');
    }
  };

  // 获取购物车商品数量
  const getCartItemCount = () => {
    return cart.reduce((total, item) => total + item.quantity, 0);
  };

  // 组件加载时获取商品数据
  useEffect(() => {
    fetchProducts();
  }, []);

  // 默认热销商品数据（当API数据未加载时显示）- 4个火爆产品
  const defaultHotProducts = [
    {
      id: 9991,
      productId: 'HOT-001',
      name: 'RGB背光透明键帽套装',
      price: 399,
      originalPrice: 499,
      category: 'GMK',
      style: '现代简约',
      keyCount: 104,
      layout: '全键盘',
      isHot: true,
      isNew: false,
      discount: 20,
      likes: 1856,
      sales: 634,
      shopName: '精品键帽工坊',
      images: ['/images/placeholders/image-placeholder.png']
    },
    {
      id: 9992,
      productId: 'HOT-002',
      name: '赛博朋克风格定制键帽',
      price: 599,
      originalPrice: 799,
      category: 'Artisan',
      style: '科幻未来',
      keyCount: 87,
      layout: '87键',
      isHot: true,
      isNew: true,
      discount: 25,
      likes: 2342,
      sales: 489,
      shopName: '创意设计工作室',
      images: ['/images/placeholders/image-placeholder.png']
    },
    {
      id: 9993,
      productId: 'HOT-003',
      name: '复古机械键帽经典版',
      price: 299,
      originalPrice: 399,
      category: 'Cherry',
      style: '复古经典',
      keyCount: 104,
      layout: '全键盘',
      isHot: true,
      isNew: false,
      discount: 15,
      likes: 1756,
      sales: 812,
      shopName: '键帽收藏家',
      images: ['/images/placeholders/image-placeholder.png']
    },
    {
      id: 9994,
      productId: 'HOT-004',
      name: '樱花粉主题限定版键帽',
      price: 799,
      originalPrice: 999,
      category: 'SA',
      style: '甜美可爱',
      keyCount: 68,
      layout: '65%',
      isHot: true,
      isNew: true,
      discount: 20,
      likes: 3128,
      sales: 567,
      shopName: '樱花键帽专营店',
      images: ['/images/placeholders/image-placeholder.png']
    }
  ];

  // 获取热销商品（优先使用API真实商品数据，按销量和点击量排序）
  const getHotProducts = () => {
    if (apiProducts.length > 0) {
      // 先尝试获取有销量的商品
      let hotApiProducts = apiProducts
        .filter(product => product.sales > 0 || product.likes > 0)
        .sort((a, b) => (b.sales + b.likes) - (a.sales + a.likes))
        .slice(0, 4);
      
      // 如果有销量的商品不足4个，补充其他商品
      if (hotApiProducts.length < 4) {
        const remainingProducts = apiProducts
          .filter(product => !hotApiProducts.find(hp => hp.id === product.id))
          .sort((a, b) => b.id - a.id) // 按ID倒序，显示最新的商品
          .slice(0, 4 - hotApiProducts.length);
        
        hotApiProducts = [...hotApiProducts, ...remainingProducts];
      }
      
      // 如果还是不足4个，使用默认数据补充
      if (hotApiProducts.length < 4) {
        const needMore = 4 - hotApiProducts.length;
        const defaultToAdd = defaultHotProducts.slice(0, needMore);
        hotApiProducts = [...hotApiProducts, ...defaultToAdd];
      }
      
      return hotApiProducts;
    }
    // 如果API没有商品，使用默认数据
    return defaultHotProducts;
  };
  
  const hotProducts = getHotProducts();
  
  // 调试信息
  console.log('🔥 热销推荐调试信息:');
  console.log('apiProducts数量:', apiProducts.length);
  console.log('hotProducts数量:', hotProducts.length);
  console.log('hotProducts:', hotProducts);

  // 商品分类选项
  const productCategories = [
    { id: 'all', name: '全部', icon: '🛍️' },
    { id: 'hot', name: '热销', icon: '🔥' },
    { id: 'new', name: '新品', icon: '✨' },
    { id: 'recommended', name: '推荐', icon: '👍' },
    { id: 'designers', name: '入驻设计师', icon: '🎨' }
  ];

  // 根据分类筛选商品
  const getProductsByCategory = (category: string, products: LocalProduct[]) => {
    switch (category) {
      case 'hot':
        return products.filter(product => product.isHot);
      case 'new':
        return products.filter(product => product.isNew);
      case 'recommended':
        // 推荐商品：销量高于100或者喜欢数高于200，或者折扣大于10%
        return products.filter(product => 
          product.sales > 100 || 
          product.likes > 200 || 
          product.discount >= 10
        );
      case 'designers':
        return designerProducts;
      default:
        return products;
    }
  };

  useEffect(() => {
    if (hotProducts.length > 1) {
      const timer = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % hotProducts.length);
      }, 4000);
      return () => clearInterval(timer);
    }
  }, [hotProducts.length]);

  useEffect(() => {
    console.log('筛选商品 - 开始执行，apiProducts长度:', apiProducts.length);
    console.log('筛选商品 - selectedProductCategory:', selectedProductCategory);
    
    let baseProducts = selectedProductCategory === 'designers' ? designerProducts : apiProducts;
    let filtered = getProductsByCategory(selectedProductCategory, baseProducts);
    
    console.log('筛选商品 - baseProducts长度:', baseProducts.length);
    console.log('筛选商品 - 分类筛选后长度:', filtered.length);

    // 设计师筛选
    if (selectedProductCategory === 'designers' && selectedDesigner) {
      filtered = filtered.filter(product => product.designerId === selectedDesigner);
    }

    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.style.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.layout.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (product.designer && product.designer.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    if (selectedStyle !== 'all') {
      filtered = filtered.filter(product => product.style === selectedStyle);
    }

    // 键盘数筛选
    if (selectedKeyCount !== 'all') {
      const keyRange = keyCountRanges.find(range => range.id === selectedKeyCount);
      if (keyRange && keyRange.min && keyRange.max) {
        filtered = filtered.filter(product => 
          product.keyCount >= keyRange.min && product.keyCount <= keyRange.max
        );
      }
    }

    console.log('筛选商品 - 最终结果长度:', filtered.length);
    setFilteredProducts(filtered);
  }, [searchQuery, selectedCategory, selectedStyle, selectedKeyCount, selectedProductCategory, selectedDesigner, apiProducts]);

  // 点击外部关闭订单菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showOrderMenu && !target.closest('.order-menu-container')) {
        setShowOrderMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showOrderMenu]);

  // 预览弹窗键盘导航
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!showPreviewModal || !previewProduct) return;
      
      const images = (previewProduct as any).images;
      if (!images || images.length <= 1) return;

      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        setCurrentImageIndex((prev) => 
          prev === 0 ? images.length - 1 : prev - 1
        );
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        setCurrentImageIndex((prev) => 
          prev === images.length - 1 ? 0 : prev + 1
        );
      } else if (event.key === 'Escape') {
        event.preventDefault();
        setShowPreviewModal(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showPreviewModal, previewProduct, currentImageIndex]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 text-white">
      <nav className="bg-gradient-to-r from-gray-800 to-gray-700 border-b border-gray-600 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-8">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-xs">
                  Keycap
                </div>
                <span className="text-xl font-bold text-white" style={{ fontWeight: '700', letterSpacing: '0.025em' }}>Keycap</span>
              </Link>
              <div className="hidden md:flex items-center space-x-6">
                <Link href="/shop" className="text-white font-semibold hover:text-purple-200 transition-colors" style={{ fontWeight: '600', letterSpacing: '0.025em' }}>
                  商城
                </Link>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative order-menu-container">
                <button 
                  onClick={() => setShowOrderMenu(!showOrderMenu)}
                  className="bg-gradient-to-r from-purple-600 to-cyan-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow flex items-center space-x-2 font-semibold"
                  style={{ fontWeight: '600', letterSpacing: '0.025em' }}
                >
                  <span>📋</span>
                  <span>我的订单</span>
                  <span className={`transform transition-transform ${showOrderMenu ? 'rotate-180' : ''}`}>▼</span>
                </button>
                
                {showOrderMenu && (
                  <div className="absolute top-full right-0 mt-2 w-48 bg-purple-100 rounded-lg shadow-lg border border-purple-200 py-2 z-50">
                    <Link href="/orders/paid" className="flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-purple-200 transition-colors">
                      <span>✅</span>
                      <span>已支付订单</span>
                    </Link>
                    <Link href="/orders/pending" className="flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-purple-200 transition-colors">
                      <span>⏳</span>
                      <span>待支付订单</span>
                    </Link>
                    <Link href="/cart" className="flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-purple-200 transition-colors">
                      <span>🛒</span>
                      <span>购物车</span>
                      {getCartItemCount() > 0 && (
                        <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 ml-1">
                          {getCartItemCount()}
                        </span>
                      )}
                    </Link>
                  </div>
                )}
              </div>
              <Link href="/dashboard" className="bg-gradient-to-r from-purple-600 to-cyan-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow font-semibold" style={{ fontWeight: '600', letterSpacing: '0.025em' }}>
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <section className="bg-gradient-to-r from-purple-900/20 to-cyan-900/20 py-8 backdrop-blur-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">🔥 热销推荐</h2>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setCurrentSlide(Math.max(0, currentSlide - 1))}
                disabled={currentSlide === 0}
                className="p-3 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow disabled:opacity-50 disabled:cursor-not-allowed text-blue-600 font-bold"
              >
                ‹
              </button>
              <div className="flex space-x-2">
                {hotProducts.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      currentSlide === index ? 'bg-blue-600 scale-125' : 'bg-gray-400 hover:bg-gray-500'
                    }`}
                  />
                ))}
              </div>
              <button
                onClick={() => setCurrentSlide(Math.min(hotProducts.length - 1, currentSlide + 1))}
                disabled={currentSlide >= hotProducts.length - 1}
                className="p-3 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow disabled:opacity-50 disabled:cursor-not-allowed text-blue-600 font-bold"
              >
                ›
              </button>
            </div>
          </div>
          
          <div className="relative overflow-hidden rounded-2xl">
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
              {hotProducts.map((product, index) => (
                <div key={product.id} className="w-full flex-shrink-0">
                  <div className="bg-gradient-to-b from-purple-800/20 to-cyan-800/20 backdrop-blur-lg rounded-2xl shadow-lg overflow-hidden border border-purple-500/30">
                    <div className="grid md:grid-cols-2 gap-0 items-stretch min-h-[400px]">
                      {/* 左侧产品图片 - 填充满左半部分 */}
                      <div className="relative bg-gradient-to-br from-gray-800 to-gray-900 overflow-hidden rounded-l-2xl">
                        {(product.images && product.images.length > 0) ? (
                          <img 
                            src={getProductImageUrl(product.images[0])} 
                            alt={product.name}
                            className="absolute inset-0 w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                          />
                        ) : (
                          <div className="absolute inset-0 w-full h-full flex items-center justify-center text-8xl text-gray-400">
                            ⌨️
                          </div>
                        )}
                        
                        {/* 折扣角标 */}
                        <div className="absolute top-6 right-6 bg-red-500 text-white px-4 py-2 rounded-full font-bold text-lg shadow-lg">
                          -{product.discount}%
                        </div>
                        
                        {/* 标签组 */}
                        <div className="absolute top-6 left-6 flex flex-col space-y-3">
                          {product.isNew && (
                            <span className="bg-green-500 text-white px-3 py-1.5 rounded-full text-sm font-medium shadow-lg">
                              🆕 新品
                            </span>
                          )}
                          <span className="bg-orange-500 text-white px-3 py-1.5 rounded-full text-sm font-medium shadow-lg">
                            🔥 热销
                          </span>
                        </div>
                        
                        {/* 底部信息条 */}
                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-6">
                          <div className="flex items-center justify-between text-white">
                            <div className="flex items-center space-x-4">
                              <span className="flex items-center space-x-1">
                                <span>❤️</span>
                                <span>{product.likes.toLocaleString()}</span>
                              </span>
                              <span className="flex items-center space-x-1">
                                <span>🛒</span>
                                <span>{product.sales}</span>
                              </span>
                            </div>
                            <span className="text-sm opacity-80">来自 {product.shopName}</span>
                          </div>
                        </div>
                      </div>
                      
                      {/* 右侧产品信息 */}
                      <div className="p-8 md:p-12">
                        <div className="space-y-6">
                          <div>
                            <h3 className="text-3xl font-bold text-white mb-3 leading-tight">
                              {product.name}
                            </h3>
                            <p className="text-gray-300 text-lg">
                              {product.category} • {product.style} • {product.layout}
                            </p>
                          </div>
                          
                          {/* 价格信息 */}
                          <div className="space-y-2">
                            <div className="flex items-baseline space-x-3">
                              <span className="text-4xl font-bold text-red-500">
                                ¥{product.price}
                              </span>
                              <span className="text-xl text-gray-500 line-through">
                                ¥{product.originalPrice}
                              </span>
                            </div>
                            <p className="text-green-600 font-medium">
                              立省 ¥{product.originalPrice - product.price}
                            </p>
                          </div>
                          
                          {/* 产品特色 */}
                          <div className="space-y-3">
                            <h4 className="font-semibold text-white">产品特色：</h4>
                            <div className="flex flex-wrap gap-2">
                              <span className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm">高品质PBT材质</span>
                              <span className="bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm">Cherry轴体兼容</span>
                              <span className="bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm">OEM高度</span>
                              <span className="bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-sm">精美包装</span>
                            </div>
                          </div>
                          
                          {/* 操作按钮 */}
                          <div className="flex space-x-4 pt-4">
                            <button 
                              onClick={() => handleBuyNow(product.id)}
                              className="flex-1 bg-gradient-to-r from-red-500 to-red-600 text-white py-4 px-6 rounded-xl hover:shadow-lg transition-all text-lg font-semibold"
                            >
                              立即购买
                            </button>
                            <button 
                              onClick={() => handleAddToCart(product.id)}
                              className="flex-1 border-2 border-cyan-500 text-white bg-cyan-800/30 py-4 px-6 rounded-xl hover:bg-cyan-700/50 transition-all text-lg font-semibold"
                            >
                              加入购物车
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="bg-gradient-to-r from-purple-900/30 to-cyan-900/30 border-t border-purple-500/30 py-6 backdrop-blur-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索键帽、品牌、风格..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-white/10 backdrop-blur-lg border border-purple-500/30 text-white placeholder-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                />
                                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-300">
                  🔍
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2 px-4 py-2 border border-purple-500/30 rounded-lg hover:bg-purple-800/20 transition-colors bg-white/10 backdrop-blur-lg text-white"
              >
                <span>🎛️</span>
                <span>筛选</span>
              </button>
            </div>
          </div>

          {showFilters && (
            <div className="mt-6 p-6 bg-white/5 backdrop-blur-lg rounded-lg border border-purple-500/20">
              <div className="grid lg:grid-cols-3 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium text-white mb-4">🏷️ 品牌分类</h3>
                  <div className="flex flex-wrap gap-2">
                    {categories.map(category => (
                      <button
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                        className={`flex items-center space-x-1 px-3 py-2 rounded-lg transition-colors ${
                          selectedCategory === category.id
                            ? 'bg-blue-600 text-white'
                            : 'bg-white text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <span>{category.icon}</span>
                        <span>{category.name}</span>
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-white mb-4">🎨 设计风格</h3>
                  <div className="flex flex-wrap gap-2">
                    {styles.map(style => (
                      <button
                        key={style.id}
                        onClick={() => setSelectedStyle(style.id)}
                        className={`flex items-center space-x-1 px-3 py-2 rounded-lg transition-colors ${
                          selectedStyle === style.id
                            ? 'bg-orange-600 text-white'
                            : 'bg-white text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <span>{style.icon}</span>
                        <span>{style.name}</span>
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-white mb-4">⌨️ 键盘数</h3>
                  <div className="flex flex-wrap gap-2">
                    {keyCountRanges.map(range => (
                      <button
                        key={range.id}
                        onClick={() => setSelectedKeyCount(range.id)}
                        className={`flex items-center space-x-1 px-3 py-2 rounded-lg transition-colors ${
                          selectedKeyCount === range.id
                            ? 'bg-green-600 text-white'
                            : 'bg-white text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <span>{range.icon}</span>
                        <span className="text-sm">{range.name}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">
              {productCategories.find(cat => cat.id === selectedProductCategory)?.icon} {productCategories.find(cat => cat.id === selectedProductCategory)?.name}商品
              <span className="text-lg font-normal text-gray-300 ml-2">
                ({filteredProducts.length} 件商品)
              </span>
            </h2>
          </div>

          {/* 商品分类选项卡 */}
          <div className="mb-8">
            <div className="flex items-center space-x-2 overflow-x-auto pb-2">
              {productCategories.map(category => (
                <button
                  key={category.id}
                  onClick={() => {
                    setSelectedProductCategory(category.id);
                    // 切换到非设计师分类时重置设计师选择
                    if (category.id !== 'designers') {
                      setSelectedDesigner(null);
                    }
                  }}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-full whitespace-nowrap transition-all ${
                    selectedProductCategory === category.id
                      ? 'bg-gradient-to-r from-purple-600 to-cyan-600 text-white shadow-lg'
                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                  }`}
                >
                  <span>{category.icon}</span>
                  <span className="font-medium">{category.name}</span>
                  <span className="text-xs bg-black bg-opacity-20 px-2 py-1 rounded-full">
                    {category.id === 'designers' ? designerProducts.length : getProductsByCategory(category.id, apiProducts).length}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* 设计师选择界面 */}
          {selectedProductCategory === 'designers' && (
            <div className="mb-8 bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <span className="mr-2">🎨</span>
                选择入驻设计师
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <button
                  onClick={() => setSelectedDesigner(null)}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    selectedDesigner === null
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-center">
                    <div className="text-3xl mb-2">🌟</div>
                    <div className="font-medium text-gray-900">全部设计师</div>
                    <div className="text-sm text-gray-500">{designerProducts.length} 件作品</div>
                  </div>
                </button>

                {designersData.map(designer => (
                  <button
                    key={designer.id}
                    onClick={() => setSelectedDesigner(designer.id)}
                    className={`p-4 rounded-lg border-2 transition-all text-left ${
                      selectedDesigner === designer.id
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="text-2xl">{designer.avatar}</div>
                      <div>
                        <div className="font-medium text-gray-900">{designer.name}</div>
                        <div className="text-xs text-purple-600">{designer.specialty}</div>
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{designer.experience}</span>
                        <span>⭐ {designer.rating}</span>
                      </div>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>👥 {designer.followers} 关注</span>
                        <span>{designerProducts.filter(p => p.designerId === designer.id).length} 作品</span>
                      </div>
                    </div>
                    
                    <div className="mt-2 text-xs text-gray-400 line-clamp-2">
                      {designer.description}
                    </div>
                  </button>
                ))}
              </div>

              {selectedDesigner && (
                <div className="bg-gradient-to-r from-purple-100 to-cyan-100 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{designersData.find(d => d.id === selectedDesigner)?.avatar}</div>
                    <div>
                      <div className="font-medium text-gray-900">
                        已选择：{designersData.find(d => d.id === selectedDesigner)?.name}
                      </div>
                      <div className="text-sm text-gray-600">
                        专注于 {designersData.find(d => d.id === selectedDesigner)?.specialty}
                      </div>
                    </div>
                    <div className="ml-auto">
                      <span className="bg-purple-600 text-white px-3 py-1 rounded-full text-sm">
                        {designerProducts.filter(p => p.designerId === selectedDesigner).length} 件作品
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {filteredProducts.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">😅</div>
              <h3 className="text-xl font-medium text-gray-900 mb-2">没有找到相关商品</h3>
              <p className="text-gray-500 mb-6">试试调整搜索条件或筛选选项</p>
              <button
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('all');
                  setSelectedStyle('all');
                  setSelectedKeyCount('all');
                  setSelectedProductCategory('all');
                  setSelectedDesigner(null);
                }}
                className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors"
              >
                清除筛选条件
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            {filteredProducts.map((product) => (
                <div 
                  key={product.id} 
                  className="group relative bg-gradient-to-b from-purple-800/20 to-cyan-800/20 backdrop-blur-lg rounded-2xl overflow-hidden hover:shadow-2xl hover:shadow-purple-500/25 transition-all duration-500 hover:scale-105"
                >
                  {/* 折扣标签 */}
                  {product.discount > 0 && (
                    <div className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg z-10">
                      -{product.discount}%
                    </div>
                  )}
                  
                  {/* 产品标签 */}
                  <div className="absolute top-4 left-4 flex flex-wrap gap-1 z-10">
                    {product.isHot && (
                      <span className="bg-black/50 text-white px-2 py-1 rounded text-xs">
                        热销
                      </span>
                    )}
                    {product.isNew && (
                      <span className="bg-black/50 text-white px-2 py-1 rounded text-xs">
                        新品
                      </span>
                    )}
                  </div>

                  {/* 产品图片 */}
                  <div 
                    className="relative h-64 overflow-hidden cursor-pointer"
                    onClick={() => handlePreviewProduct(product)}
                  >
                    {(product as any).images && (product as any).images.length > 0 ? (
                      <>
                        <img
                          src={getProductImageUrl((product as any).images[0])}
                          alt={product.name}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                          style={{
                            minHeight: '100%',
                            minWidth: '100%',
                            display: 'block'
                          }}
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = '/images/placeholders/image-placeholder.png';
                          }}
                        />
                        {(product as any).images.length > 1 && (
                          <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center space-x-1">
                            <span>📷</span>
                            <span>{(product as any).images.length}</span>
                          </div>
                        )}
                      </>
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-4xl text-gray-400 bg-gradient-to-br from-gray-800 to-gray-900">🎯</div>
                    )}
                  </div>

                  {/* 产品信息 */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors">
                      {product.name}
                    </h3>
                    
                    <p className="text-gray-300 text-sm mb-4">
                      {product.keyCount}键 • {product.layout} • {product.category}
                    </p>
                    
                    {/* 评分和销量 */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-1">
                        {[...Array(5)].map((_, i) => (
                          <span key={i} className={`text-sm ${i < 4 ? 'text-yellow-400' : 'text-gray-500'}`}>
                            ★
                          </span>
                        ))}
                        <span className="text-gray-400 text-sm ml-1">(4.8)</span>
                      </div>
                      <span className="text-gray-400 text-sm">销量 {product.sales}</span>
                    </div>

                    {/* 价格和购买按钮 */}
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-2xl font-bold text-cyan-400">¥{product.price}</span>
                        {product.originalPrice > product.price && (
                          <span className="text-gray-500 text-sm line-through ml-2">¥{product.originalPrice}</span>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <button 
                          onClick={() => handleAddToCart(product.id)}
                          className="bg-gradient-to-r from-purple-600 to-cyan-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300 hover:scale-105 text-sm font-medium"
                        >
                          加购物车
                        </button>
                        <button 
                          onClick={() => handleBuyNow(product.id)}
                          className="bg-gradient-to-r from-cyan-500 to-purple-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300 hover:scale-105 text-sm font-medium"
                        >
                          立即购买
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      <footer className="bg-gradient-to-r from-gray-900 to-slate-800 border-t border-gray-700/40 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                  L
                </div>
                <span className="text-xl font-bold text-white">Keycap</span>
              </div>
              <p className="text-gray-600">
                专业的键帽设计与销售平台
              </p>
            </div>
            <div>
              <h3 className="font-bold text-white mb-4">🛍️ 商城</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/shop?category=GMK" className="hover:text-cyan-400 transition-colors">GMK 键帽</Link></li>
                <li><Link href="/shop?category=SA" className="hover:text-cyan-400 transition-colors">SA 键帽</Link></li>
                <li><Link href="/shop?category=Jellykey" className="hover:text-cyan-400 transition-colors">Jellykey</Link></li>
                <li><Link href="/shop?category=PBT" className="hover:text-cyan-400 transition-colors">PBT 键帽</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold text-white mb-4">🎨 服务</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/designer" className="hover:text-cyan-400 transition-colors">键帽设计器</Link></li>
                <li><Link href="/community" className="hover:text-cyan-400 transition-colors">社区</Link></li>
                <li><Link href="/materials" className="hover:text-cyan-400 transition-colors">材质工艺</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold text-white mb-4">📞 联系我们</h3>
              <ul className="space-y-2 text-gray-400">
                <li>邮箱: <EMAIL></li>
                <li>微信: LingHuJianChuang</li>
                <li>QQ: 123456789</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-purple-800/40 mt-8 pt-8 text-center text-gray-400">
                            <p>&copy; 2024 Keycap 版权所有</p>
          </div>
        </div>
      </footer>

      {/* 预览模态框 */}
      {showPreviewModal && previewProduct && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl p-8 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold text-gray-900">商品详情</h3>
                {(previewProduct as any).images && (previewProduct as any).images.length > 1 && (
                  <p className="text-sm text-gray-500 mt-1">
                    💡 使用 ← → 键或点击缩略图切换图片 (共 {(previewProduct as any).images.length} 张)
                  </p>
                )}
              </div>
              <button
                onClick={() => setShowPreviewModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 左侧：商品图片 */}
              <div className="space-y-4">
                <div className="relative w-full aspect-square rounded-lg flex items-center justify-center bg-gray-100 overflow-hidden">
                  {(previewProduct as any).images && (previewProduct as any).images.length > 0 ? (
                    <>
                      <ImageWithFallback
                        key={`preview-main-${currentImageIndex}`}
                        src={getProductImageUrl((previewProduct as any).images[currentImageIndex])}
                        alt={`${previewProduct.name} - 图片 ${currentImageIndex + 1}`}
                        className="w-full h-full object-contain"
                        fallbackSrc="/images/placeholders/image-placeholder.png"
                        transparent={true}
                        enlargeFactor={0.95}
                      />
                      
                      {/* 图片切换箭头 */}
                      {(previewProduct as any).images.length > 1 && (
                        <>
                          <button
                            onClick={() => {
                              const newIndex = currentImageIndex === 0 ? (previewProduct as any).images.length - 1 : currentImageIndex - 1;
                              console.log('切换到上一张图片:', currentImageIndex, '->', newIndex);
                              setCurrentImageIndex(newIndex);
                            }}
                            className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                          >
                            ‹
                          </button>
                          <button
                            onClick={() => {
                              const newIndex = currentImageIndex === (previewProduct as any).images.length - 1 ? 0 : currentImageIndex + 1;
                              console.log('切换到下一张图片:', currentImageIndex, '->', newIndex);
                              setCurrentImageIndex(newIndex);
                            }}
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                          >
                            ›
                          </button>
                          
                          {/* 图片指示器 */}
                          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                            {(previewProduct as any).images.map((_: any, index: number) => (
                              <button
                                key={`dot-${index}`}
                                onClick={() => {
                                  console.log('点击圆点指示器:', currentImageIndex, '->', index);
                                  setCurrentImageIndex(index);
                                }}
                                className={`w-2 h-2 rounded-full transition-all ${
                                  currentImageIndex === index ? 'bg-white scale-125' : 'bg-white/50'
                                }`}
                              />
                            ))}
                          </div>
                        </>
                      )}
                    </>
                  ) : (
                    <div className="text-6xl text-gray-400">🎯</div>
                  )}
                </div>
                
                {/* 缩略图网格 */}
                {(previewProduct as any).images && (previewProduct as any).images.length > 1 && (
                  <div className="grid grid-cols-5 gap-2">
                    {(previewProduct as any).images.map((image: string, index: number) => (
                      <button
                        key={`thumb-${index}`}
                        onClick={() => {
                          console.log('点击缩略图:', currentImageIndex, '->', index);
                          console.log('图片URL:', getProductImageUrl(image));
                          setCurrentImageIndex(index);
                        }}
                        className={`aspect-square rounded-lg overflow-hidden border-2 transition-all ${
                          currentImageIndex === index 
                            ? 'border-blue-500 scale-105 shadow-lg' 
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <ImageWithFallback
                          src={getProductImageUrl(image)}
                          alt={`${previewProduct.name} 图片 ${index + 1}`}
                          className="w-full h-full object-cover"
                          fallbackSrc="/images/placeholders/image-placeholder.png"
                          transparent={true}
                        />
                      </button>
                    ))}
                  </div>
                )}
                
                {/* 图片信息显示 */}
                {(previewProduct as any).images && (previewProduct as any).images.length > 0 && (
                  <div className="text-center text-sm text-gray-500 space-y-1">
                    <div>图片 {currentImageIndex + 1} / {(previewProduct as any).images.length}</div>
                    <div className="text-xs text-gray-400 break-all">
                      当前URL: {getProductImageUrl((previewProduct as any).images[currentImageIndex])}
                    </div>
                  </div>
                )}
              </div>

              {/* 右侧：商品信息 */}
              <div className="space-y-6">
                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    {previewProduct.isNew && (
                      <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                        新品
                      </span>
                    )}
                    {previewProduct.isHot && (
                      <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                        HOT
                      </span>
                    )}
                    {previewProduct.discount > 0 && (
                      <span className="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                        -{previewProduct.discount}%
                      </span>
                    )}
                  </div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">{previewProduct.name}</h1>
                  <p className="text-gray-600">{previewProduct.category} • {previewProduct.style}</p>
                </div>

                <div className="flex items-center space-x-4">
                  <span className="text-3xl font-bold text-red-600">¥{previewProduct.price}</span>
                  {previewProduct.originalPrice > previewProduct.price && (
                    <span className="text-lg text-gray-400 line-through">¥{previewProduct.originalPrice}</span>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <span className="text-gray-500">键数：</span>
                    <span className="font-medium">{previewProduct.keyCount}键</span>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <span className="text-gray-500">布局：</span>
                    <span className="font-medium">{previewProduct.layout}</span>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <span className="text-gray-500">❤️ 喜欢：</span>
                    <span className="font-medium">{previewProduct.likes}</span>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <span className="text-gray-500">📦 销量：</span>
                    <span className="font-medium">{previewProduct.sales}</span>
                  </div>
                </div>

                {(previewProduct as any).isDesignerProduct && (
                  <div className="bg-gradient-to-r from-purple-100 to-cyan-100 p-4 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-lg">🎨</span>
                      <span className="font-medium text-gray-900">设计师作品</span>
                    </div>
                    <p className="text-gray-600">来自：{(previewProduct as any).designer}</p>
                  </div>
                )}

                <div className="flex space-x-4">
                  <button 
                    onClick={() => handleAddToCart(previewProduct.id)}
                    className="flex-1 bg-purple-600 text-white py-3 rounded-lg hover:bg-purple-700 transition-colors font-medium"
                  >
                    🛒 加入购物车
                  </button>
                  <button 
                    onClick={() => handleBuyNow(previewProduct.id)}
                    className="flex-1 bg-gradient-to-r from-purple-600 to-cyan-600 text-white py-3 rounded-lg hover:shadow-lg transition-shadow font-medium"
                  >
                    立即购买
                  </button>
                </div>

                <div className="text-center">
                  <p className="text-gray-500 text-sm">店铺：{previewProduct.shopName}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}