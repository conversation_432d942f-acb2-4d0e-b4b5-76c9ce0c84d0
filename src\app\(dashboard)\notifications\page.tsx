'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

// API配置
const API_BASE_URL = 'http://localhost:8080/api';

interface Notification {
  id: string;
  type: string;
  title: string;
  message: string;
  read: boolean;
  createdAt: string;
  relatedId?: string;
}

export default function NotificationsPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [activeFilter, setActiveFilter] = useState<'all' | 'unread' | 'read'>('all');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);

  // 获取通知列表
  const fetchNotifications = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/notifications`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        console.log('通知API响应:', result);
        
        // 兼容两种响应格式：result.success 和 result.code === 200
        if (result.success || result.code === 200) {
          // 转换后端数据格式到前端格式
          const formattedNotifications = (result.data || []).map((notification: any) => ({
            id: notification.notificationId || notification.notification_id,
            type: notification.type,
            title: notification.title,
            message: notification.content || notification.message,
            read: notification.isRead !== undefined ? notification.isRead : notification.is_read,
            createdAt: notification.createTime || notification.created_at || notification.createdAt,
            relatedId: notification.targetId || notification.target_id || notification.related_id
          }));
          console.log('格式化后的通知数据:', formattedNotifications);
          setNotifications(formattedNotifications);
        } else {
          throw new Error(result.message || '获取通知失败');
        }
      } else {
        const errorResult = await response.json();
        throw new Error(errorResult.message || `获取通知失败，状态码：${response.status}`);
      }
    } catch (error) {
      console.error('获取通知失败:', error);
      // 设置空数组，避免页面崩溃
      setNotifications([]);
    } finally {
      setLoading(false);
    }
  };

  // 标记消息为已读
  const markAsRead = async (notificationIds: string[]) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/notifications/mark-read`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ notificationIds })
      });

      if (response.ok) {
        const result = await response.json();
        // 兼容两种响应格式
        if (result.success || result.code === 200) {
          // 更新本地状态
          setNotifications(prev => prev.map(notification => 
            notificationIds.includes(notification.id) 
              ? { ...notification, read: true }
              : notification
          ));
        } else {
          throw new Error(result.message || '标记已读失败');
        }
      } else {
        const errorResult = await response.json();
        throw new Error(errorResult.message || `标记已读失败，状态码：${response.status}`);
      }
    } catch (error) {
      console.error('标记已读失败:', error);
      // 显示错误但不更新本地状态，保持数据一致性
    }
  };

  // 删除消息
  const deleteNotifications = async (notificationIds: string[]) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/notifications`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ notificationIds })
      });

      if (response.ok) {
        const result = await response.json();
        // 兼容两种响应格式
        if (result.success || result.code === 200) {
          // 更新本地状态
          setNotifications(prev => prev.filter(notification => 
            !notificationIds.includes(notification.id)
          ));
          setSelectedNotifications([]);
        } else {
          throw new Error(result.message || '删除消息失败');
        }
      } else {
        const errorResult = await response.json();
        throw new Error(errorResult.message || `删除消息失败，状态码：${response.status}`);
      }
    } catch (error) {
      console.error('删除消息失败:', error);
      alert(`删除消息失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    switch (activeFilter) {
      case 'unread':
        return !notification.read;
      case 'read':
        return notification.read;
      default:
        return true;
    }
  });

  const unreadCount = notifications.filter(n => !n.read).length;

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'SHOP_REVIEW':
        return '🏪';
      case 'SHOP_APPLY':
        return '📝';
      case 'SYSTEM':
        return '🔔';
      case 'ORDER':
        return '🛒';
      default:
        return '📋';
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'SHOP_REVIEW':
        return 'from-green-400 to-blue-500';
      case 'SHOP_APPLY':
        return 'from-yellow-400 to-orange-500';
      case 'SYSTEM':
        return 'from-purple-400 to-pink-500';
      case 'ORDER':
        return 'from-blue-400 to-indigo-500';
      default:
        return 'from-gray-400 to-gray-500';
    }
  };

  const handleSelectAll = () => {
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(filteredNotifications.map(n => n.id));
    }
  };

  const handleBatchRead = () => {
    if (selectedNotifications.length > 0) {
      markAsRead(selectedNotifications);
      setSelectedNotifications([]);
    }
  };

  const handleBatchDelete = () => {
    if (selectedNotifications.length > 0 && confirm('确定要删除选中的消息吗？')) {
      deleteNotifications(selectedNotifications);
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-8 h-8 bg-blue-600 rounded-full animate-pulse"></div>
            </div>
          </div>
          <p className="mt-6 text-gray-600 text-lg">正在加载消息...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* 面包屑导航 */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 px-6 py-4">
        <nav className="flex items-center space-x-2 text-sm">
          <Link href="/dashboard" className="text-gray-500 hover:text-gray-700 transition-colors">
            仪表盘
          </Link>
          <span className="text-gray-400">&gt;</span>
          <span className="text-gray-900">消息中心</span>
        </nav>
      </div>

      <div className="max-w-6xl mx-auto px-6 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">消息中心</h1>
              <p className="text-gray-600">
                您有 <span className="font-semibold text-blue-600">{unreadCount}</span> 条未读消息
              </p>
            </div>
            <button
              onClick={() => fetchNotifications()}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
            >
              <span>🔄</span>
              <span>刷新</span>
            </button>
          </div>
        </div>

        {/* 筛选和操作栏 */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6 border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div className="flex space-x-2">
              {[
                { key: 'all', label: '全部', count: notifications.length },
                { key: 'unread', label: '未读', count: unreadCount },
                { key: 'read', label: '已读', count: notifications.length - unreadCount }
              ].map(filter => (
                <button
                  key={filter.key}
                  onClick={() => setActiveFilter(filter.key as any)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    activeFilter === filter.key
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {filter.label} ({filter.count})
                </button>
              ))}
            </div>

            {selectedNotifications.length > 0 && (
              <div className="flex space-x-2">
                <button
                  onClick={handleBatchRead}
                  className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors text-sm"
                >
                  标记已读
                </button>
                <button
                  onClick={handleBatchDelete}
                  className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors text-sm"
                >
                  删除选中
                </button>
              </div>
            )}
          </div>

          {filteredNotifications.length > 0 && (
            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedNotifications.length === filteredNotifications.length && filteredNotifications.length > 0}
                  onChange={handleSelectAll}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-600">全选</span>
              </label>
              <span className="text-sm text-gray-500">
                已选择 {selectedNotifications.length} 条消息
              </span>
            </div>
          )}
        </div>

        {/* 消息列表 */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
          {filteredNotifications.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">📪</div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">暂无消息</h3>
              <p className="text-gray-600">
                {activeFilter === 'unread' ? '您已读完所有消息' : 
                 activeFilter === 'read' ? '暂无已读消息' : '暂无消息'}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-6 hover:bg-gray-50 transition-colors ${
                    !notification.read ? 'bg-blue-50/50' : ''
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    <input
                      type="checkbox"
                      checked={selectedNotifications.includes(notification.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedNotifications([...selectedNotifications, notification.id]);
                        } else {
                          setSelectedNotifications(selectedNotifications.filter(id => id !== notification.id));
                        }
                      }}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 mt-1"
                    />
                    
                    <div className={`w-12 h-12 bg-gradient-to-br ${getNotificationColor(notification.type)} rounded-xl flex items-center justify-center text-white text-xl shadow-lg`}>
                      {getNotificationIcon(notification.type)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className={`text-lg font-semibold ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>
                          {notification.title}
                          {!notification.read && (
                            <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              新
                            </span>
                          )}
                        </h3>
                        <span className="text-sm text-gray-500">
                          {new Date(notification.createdAt).toLocaleString()}
                        </span>
                      </div>
                      
                      <p className="text-gray-600 leading-relaxed mb-3">
                        {notification.message}
                      </p>

                      <div className="flex items-center space-x-3">
                        {!notification.read && (
                          <button
                            onClick={() => markAsRead([notification.id])}
                            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                          >
                            标记已读
                          </button>
                        )}
                        <button
                          onClick={() => deleteNotifications([notification.id])}
                          className="text-red-600 hover:text-red-700 text-sm font-medium"
                        >
                          删除
                        </button>
                        {notification.relatedId && (
                          <button className="text-gray-600 hover:text-gray-700 text-sm font-medium">
                            查看详情
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 返回按钮 */}
        <div className="mt-8 text-center">
          <Link
            href="/dashboard"
            className="inline-flex items-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-xl transition-colors space-x-2"
          >
            <span>←</span>
            <span>返回仪表盘</span>
          </Link>
        </div>
      </div>
    </div>
  );
} 