'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '../../../contexts/AuthContext';
import { useRouter } from 'next/navigation';

export default function SettingsPage() {
  const { updateUserProfile } = useAuth();
  const router = useRouter();

  const [activeTab, setActiveTab] = useState('profile');
  const [user, setUser] = useState({
    username: '',
    nickname: '',
    email: '',
    avatar: 'U',
    avatarPath: '',
    userTitle: '初级设计师',
    joinDate: '2024-01-01',
    worksCount: 0,
    shopStatus: '未开通',
    hasShop: false,
    bio: '',
    designAdvantage: '',
    followingCount: 0,
    followersCount: 0
  });

  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    username: '',
    nickname: '',
    email: '',
    bio: '',
    designAdvantage: ''
  });

  // 头像上传相关状态
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [showAvatarModal, setShowAvatarModal] = useState(false);

  // 修改密码相关状态
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  // 注销账号相关状态
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteConfirm, setDeleteConfirm] = useState('');
  const [isDeletingAccount, setIsDeletingAccount] = useState(false);

  // 获取用户信息
  useEffect(() => {
    const loadUserProfile = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) return;

        const userData = localStorage.getItem('user');
        if (!userData) return;

        const userInfo = JSON.parse(userData);

        // 获取真实的用户资料
        const response = await fetch(`http://localhost:8080/api/users/profile`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const result = await response.json();
          if (result.code === 200) {
            const profile = result.data;
            
            // 获取真实的店铺状态
            const shopResponse = await fetch('http://localhost:8080/api/shop/my-shop', {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });

            let shopStatus = '未开通';
            let hasShop = false;
            if (shopResponse.ok) {
              const shopResult = await shopResponse.json();
              if (shopResult.code === 200 && shopResult.data) {
                const shop = shopResult.data;
                hasShop = true;
                switch (shop.status) {
                  case 'PENDING':
                    shopStatus = '审核中';
                    break;
                  case 'APPROVED':
                    shopStatus = '已开通';
                    break;
                  case 'REJECTED':
                    shopStatus = '审核未通过';
                    break;
                  default:
                    shopStatus = '未知状态';
                }
              }
            }

            const fullUser = {
              username: profile.username || '创意设计师',
              nickname: profile.nickname || profile.username || '创意设计师',
              email: userInfo.email || '未绑定邮箱',
              avatar: (profile.nickname || profile.username) ? (profile.nickname || profile.username).charAt(0).toUpperCase() : 'U',
              avatarPath: profile.avatarPath || '',
              userTitle: profile.userTitle || '初级设计师',
              joinDate: profile.createTime ? new Date(profile.createTime).toISOString().split('T')[0] : '2024-01-01',
              worksCount: profile.postCount || 0,
              shopStatus: shopStatus,
              hasShop: hasShop,
              bio: profile.bio || '',
              designAdvantage: profile.designAdvantage || '',
              followingCount: profile.followingCount || 0,
              followersCount: profile.followersCount || 0
            };
            
            setUser(fullUser);
            setEditForm({
              username: fullUser.username,
              nickname: fullUser.nickname,
              email: fullUser.email,
              bio: fullUser.bio,
              designAdvantage: fullUser.designAdvantage
            });
          }
        }
      } catch (error: any) {
        console.error('获取用户资料失败:', error);
      }
    };

    if (typeof window !== 'undefined') {
      loadUserProfile();
    }
  }, []);

  // 保存个人信息
  const handleSaveProfile = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      const response = await fetch('http://localhost:8080/api/users/update-basic-info', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          username: editForm.username,
          nickname: editForm.nickname,
          bio: editForm.bio,
          designAdvantage: editForm.designAdvantage
        })
      });

      if (!response.ok) {
        throw new Error('保存失败，请重试');
      }

      const result = await response.json();
      if (result.code === 200) {
        alert('个人信息保存成功！');
        setUser(prev => ({
          ...prev,
          username: editForm.username,
          nickname: editForm.nickname,
          bio: editForm.bio,
          designAdvantage: editForm.designAdvantage
        }));
        setIsEditing(false);

        // 更新localStorage
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
        const updatedUser = {
          ...currentUser,
          username: editForm.username,
          nickname: editForm.nickname,
        };
        localStorage.setItem('user', JSON.stringify(updatedUser));

        // 更新全局用户状态
        updateUserProfile({
          username: editForm.username,
          nickname: editForm.nickname,
          avatarPath: user.avatarPath
        });
      } else {
        throw new Error(result.message || '保存失败');
      }
    } catch (error: any) {
      console.error('保存个人信息失败:', error);
      alert(`保存失败: ${error.message}`);
    }
  };

  // 头像上传
  const handleAvatarUpload = async (file: File) => {
    setIsUploadingAvatar(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      const formData = new FormData();
      formData.append('file', file);

      // 直接使用后端的upload-avatar接口，一步完成上传和更新
      const response = await fetch('http://localhost:8080/api/users/upload-avatar', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('上传失败');
      }

      const result = await response.json();
      if (result.code === 200 && result.data) {
        const newAvatarPath = result.data.avatarPath;
        
        alert('头像更新成功！');
        setUser(prev => ({ ...prev, avatarPath: newAvatarPath }));
        setShowAvatarModal(false);

        // 更新localStorage
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
        currentUser.avatarPath = newAvatarPath;
        localStorage.setItem('user', JSON.stringify(currentUser));

        // 更新全局用户状态
        updateUserProfile({
          username: user.username,
          nickname: user.nickname,
          avatarPath: newAvatarPath
        });
      } else {
        throw new Error(result.message || '头像上传失败');
      }
    } catch (error: any) {
      console.error('头像上传失败:', error);
      alert(`头像上传失败: ${error.message}`);
    } finally {
      setIsUploadingAvatar(false);
    }
  };

  // 修改密码
  const handleChangePassword = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      alert('新密码和确认密码不一致');
      return;
    }

    if (passwordForm.newPassword.length < 6) {
      alert('新密码长度至少为6位');
      return;
    }

    setIsChangingPassword(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      const response = await fetch('http://localhost:8080/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          currentPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword
        })
      });

      const result = await response.json();
      if (response.ok && result.code === 200) {
        alert('密码修改成功！您已修改密码，请重新登录');
        // 清除本地存储
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        // 跳转到登录页
        router.push('/login');
      } else {
        throw new Error(result.message || '密码修改失败');
      }
    } catch (error: any) {
      console.error('修改密码失败:', error);
      alert(`修改密码失败: ${error.message}`);
    } finally {
      setIsChangingPassword(false);
    }
  };

  // 注销账号
  const handleDeleteAccount = async () => {
    if (deleteConfirm !== '删除我的账号') {
      alert('请输入确认文字：删除我的账号');
      return;
    }

    setIsDeletingAccount(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      const response = await fetch('http://localhost:8080/api/users/delete-account', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      if (response.ok && result.code === 200) {
        alert('账号注销成功');
        // 清除本地存储
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        // 跳转到首页
        router.push('/');
      } else {
        throw new Error(result.message || '账号注销失败');
      }
    } catch (error: any) {
      console.error('注销账号失败:', error);
      alert(`注销账号失败: ${error.message}`);
    } finally {
      setIsDeletingAccount(false);
      setShowDeleteModal(false);
    }
  };

  const tabs = [
    { id: 'profile', name: '个人信息', icon: '👤' },
    { id: 'password', name: '修改密码', icon: '🔐' },
    { id: 'account', name: '账号管理', icon: '⚙️' }
  ];

  return (
    <div className="settings-page">
      <div className="p-8">
        <h1 className="text-3xl font-bold mb-2 text-gray-900">设置</h1>
            <p className="text-gray-600 mt-1">管理您的账户信息和偏好设置</p>
        </div>

      <div className="max-w-6xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧标签页 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors text-left ${
                      activeTab === tab.id
                        ? 'bg-blue-50 text-blue-700 border border-blue-200'
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <span className="text-lg">{tab.icon}</span>
                    <span className="font-medium">{tab.name}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* 右侧内容区 */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              {/* 个人信息标签页 */}
              {activeTab === 'profile' && (
                <div className="p-6">
                  <div className="mb-6">
                    <h2 className="text-xl font-bold text-gray-900 mb-2">个人信息</h2>
                    <p className="text-gray-600">管理您的基本信息和个人资料</p>
                  </div>

                  {/* 头像区域 */}
                  <div className="mb-8">
                    <label className="block text-sm font-medium text-gray-700 mb-4">头像</label>
                    <div className="flex items-center space-x-6">
                      <div className="relative group">
                        <div className="w-20 h-20 rounded-full overflow-hidden bg-gradient-to-r from-blue-600 to-blue-500 flex items-center justify-center shadow-md">
                          {user.avatarPath ? (
                            <img 
                              src={`http://localhost:8080${user.avatarPath}`} 
                              alt="用户头像" 
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                const fallback = target.parentElement?.querySelector('.fallback-avatar') as HTMLElement;
                                if (fallback) fallback.style.display = 'flex';
                              }}
                            />
                          ) : null}
                          <div className={`w-full h-full text-white font-bold text-2xl flex items-center justify-center fallback-avatar ${user.avatarPath ? 'hidden' : ''}`}>
                            {user.avatar}
                          </div>
                        </div>
                        
                        <button
                          onClick={() => setShowAvatarModal(true)}
                          className="absolute inset-0 w-20 h-20 rounded-full bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                        >
                          <span className="text-white text-xs">更换</span>
                        </button>
                      </div>
                      
                      <div>
                        <button
                          onClick={() => setShowAvatarModal(true)}
                          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          更换头像
                        </button>
                        <p className="text-sm text-gray-500 mt-2">支持 JPG、PNG 格式，建议尺寸 200x200</p>
                      </div>
                    </div>
                  </div>

                  {/* 基本信息表单 */}
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                        <input
                          type="text"
                          value={isEditing ? editForm.username : user.username}
                          onChange={(e) => isEditing && setEditForm(prev => ({ ...prev, username: e.target.value }))}
                          disabled={!isEditing}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">昵称</label>
                        <input
                          type="text"
                          value={isEditing ? editForm.nickname : user.nickname}
                          onChange={(e) => isEditing && setEditForm(prev => ({ ...prev, nickname: e.target.value }))}
                          disabled={!isEditing}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                      <input
                        type="email"
                        value={user.email}
                        disabled
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                      />
                      <p className="text-sm text-gray-500 mt-1">邮箱暂不支持修改</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">个人简介</label>
                      <textarea
                        value={isEditing ? editForm.bio : user.bio}
                        onChange={(e) => isEditing && setEditForm(prev => ({ ...prev, bio: e.target.value }))}
                        disabled={!isEditing}
                        rows={3}
                        placeholder="介绍一下自己..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">设计特长</label>
                      <textarea
                        value={isEditing ? editForm.designAdvantage : user.designAdvantage}
                        onChange={(e) => isEditing && setEditForm(prev => ({ ...prev, designAdvantage: e.target.value }))}
                        disabled={!isEditing}
                        rows={3}
                        placeholder="描述您的设计特长和擅长领域..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                      />
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="mt-8 flex items-center space-x-4">
                    {!isEditing ? (
                      <button
                        onClick={() => setIsEditing(true)}
                        className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        编辑信息
                      </button>
                    ) : (
                      <>
                        <button
                          onClick={handleSaveProfile}
                          className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
                        >
                          保存修改
                        </button>
                        <button
                          onClick={() => {
                            setIsEditing(false);
                            setEditForm({
                              username: user.username,
                              nickname: user.nickname,
                              email: user.email,
                              bio: user.bio,
                              designAdvantage: user.designAdvantage
                            });
                          }}
                          className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                        >
                          取消
                        </button>
                      </>
                    )}
                  </div>
                </div>
              )}

              {/* 修改密码标签页 */}
              {activeTab === 'password' && (
                <div className="p-6">
                  <div className="mb-6">
                    <h2 className="text-xl font-bold text-gray-900 mb-2">修改密码</h2>
                    <p className="text-gray-600">更新您的账户密码以保证安全</p>
                  </div>

                  <div className="max-w-md space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">当前密码</label>
                      <input
                        type="password"
                        value={passwordForm.currentPassword}
                        onChange={(e) => setPasswordForm(prev => ({ ...prev, currentPassword: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="请输入当前密码"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">新密码</label>
                      <input
                        type="password"
                        value={passwordForm.newPassword}
                        onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="请输入新密码（至少6位）"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">确认新密码</label>
                      <input
                        type="password"
                        value={passwordForm.confirmPassword}
                        onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="请再次输入新密码"
                      />
                    </div>

                    <button
                      onClick={handleChangePassword}
                      disabled={isChangingPassword || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
                      className="w-full bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                    >
                      {isChangingPassword ? '修改中...' : '修改密码'}
                    </button>

                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <span className="text-yellow-600">⚠️</span>
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-yellow-800">注意事项</h3>
                          <div className="mt-2 text-sm text-yellow-700">
                            <ul className="list-disc pl-5 space-y-1">
                              <li>密码至少包含6个字符</li>
                              <li>修改密码后需要重新登录</li>
                              <li>请确保新密码的安全性</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 账号管理标签页 */}
              {activeTab === 'account' && (
                <div className="p-6">
                  <div className="mb-6">
                    <h2 className="text-xl font-bold text-gray-900 mb-2">账号管理</h2>
                    <p className="text-gray-600">管理您的账号状态和危险操作</p>
                  </div>

                  {/* 账号信息展示 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="font-medium text-gray-900 mb-2">注册时间</h3>
                      <p className="text-gray-600">{user.joinDate}</p>
                    </div>
                    
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="font-medium text-gray-900 mb-2">用户等级</h3>
                      <p className="text-gray-600">{user.userTitle}</p>
                    </div>
                    
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="font-medium text-gray-900 mb-2">设计作品</h3>
                      <p className="text-gray-600">{user.worksCount} 个</p>
                    </div>
                    
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="font-medium text-gray-900 mb-2">店铺状态</h3>
                      <p className="text-gray-600">{user.shopStatus}</p>
                    </div>
                  </div>

                  {/* 危险操作区域 */}
                  <div className="border-t border-gray-200 pt-6">
                    <h3 className="text-lg font-medium text-red-600 mb-4">危险操作</h3>
                    
                    <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <span className="text-red-600 text-xl">⚠️</span>
                        </div>
                        <div className="ml-4 flex-1">
                          <h4 className="text-red-800 font-medium mb-2">注销账号</h4>
                          <p className="text-red-700 text-sm mb-4">
                            注销账号将永久删除您的所有数据，包括设计作品、个人信息等，此操作不可撤销。
                          </p>
                          {user.hasShop && (
                            <div className="bg-red-100 border border-red-300 rounded p-3 mb-4">
                              <p className="text-red-800 text-sm">
                                <strong>注意：</strong>您已开通店铺，注销前系统将检查是否有未处理的订单。如有未发货订单，需要先处理完毕才能注销。
                              </p>
                            </div>
                          )}
                          <button
                            onClick={() => setShowDeleteModal(true)}
                            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                          >
                            注销账号
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 头像上传模态框 */}
      {showAvatarModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-96 max-w-[90vw]">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-gray-900">更换头像</h3>
              <button
                onClick={() => setShowAvatarModal(false)}
                className="text-gray-400 hover:text-gray-600 text-2xl"
              >
                ×
              </button>
            </div>

            <div className="space-y-4">
              <div className="text-center">
                <div className="w-32 h-32 mx-auto rounded-full overflow-hidden bg-gray-200 flex items-center justify-center mb-4">
                  {user.avatarPath ? (
                    <img 
                      src={`http://localhost:8080${user.avatarPath}`} 
                      alt="当前头像" 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <span className="text-4xl font-bold text-gray-500">{user.avatar}</span>
                  )}
                </div>
                <p className="text-sm text-gray-600">当前头像</p>
              </div>

              <div>
                <label className="block">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        if (file.size > 5 * 1024 * 1024) {
                          alert('文件大小不能超过5MB');
                          return;
                        }
                        handleAvatarUpload(file);
                      }
                    }}
                    className="hidden"
                  />
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer">
                    <span className="text-blue-600 text-2xl mb-2 block">📁</span>
                    <p className="text-gray-600">点击选择图片</p>
                    <p className="text-sm text-gray-500 mt-1">支持 JPG、PNG 格式，最大 5MB</p>
                  </div>
                </label>
              </div>

              {isUploadingAvatar && (
                <div className="text-center">
                  <div className="text-blue-600">上传中...</div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 注销账号确认模态框 */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-96 max-w-[90vw]">
            <div className="text-center mb-6">
              <div className="text-red-600 text-4xl mb-4">⚠️</div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">确认注销账号</h3>
              <p className="text-gray-600 text-sm">此操作将永久删除您的账号和所有数据，无法恢复。</p>
            </div>

            {user.hasShop && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                <p className="text-yellow-800 text-sm">
                  系统检测到您已开通店铺，将检查是否有未处理的订单。
                </p>
              </div>
            )}

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                请输入 "删除我的账号" 来确认：
              </label>
              <input
                type="text"
                value={deleteConfirm}
                onChange={(e) => setDeleteConfirm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="删除我的账号"
              />
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowDeleteModal(false)}
                className="flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleDeleteAccount}
                disabled={isDeletingAccount || deleteConfirm !== '删除我的账号'}
                className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isDeletingAccount ? '注销中...' : '确认注销'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 