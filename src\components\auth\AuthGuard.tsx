'use client';

import { useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslations } from '../../contexts/LocaleContext';

interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export default function AuthGuard({ children, fallback }: AuthGuardProps) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const t = useTranslations();

  useEffect(() => {
    // 只有在加载完成且确认未认证的情况下才跳转
    if (!isLoading && !isAuthenticated && !user) {
      console.log('认证失败，跳转到登录页面');
      // 显示提示信息，然后跳转到登录页面
      alert(t('auth.loginExpired'));
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, user, router]);

  // 加载中显示
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#1a1b26] flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4 animate-pulse">
            灵
          </div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-400">{t('auth.verifyingLogin') || '验证登录状态...'}</p>
        </div>
      </div>
    );
  }

  // 未登录显示自定义内容或空
  if (!isAuthenticated || !user) {
    return fallback || null;
  }

  // 已登录显示子组件
  return <>{children}</>;
} 