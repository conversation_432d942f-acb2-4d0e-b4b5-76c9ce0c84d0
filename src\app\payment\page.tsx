'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';

// API基础URL
const API_BASE_URL = 'http://localhost:8080/api';

// 订单信息接口
interface OrderInfo {
  orderId: string;
  status: string;
  totalAmount: number;
  shippingFee: number;
  actualAmount: number;
  receiverName: string;
  receiverPhone: string;
  receiverAddress: string;
  paymentTimeout: string;
  orderItems: {
    productId: number;
    productName: string;
    productImage: string;
    unitPrice: number;
    quantity: number;
    totalPrice: number;
  }[];
}

// 支付方式枚举
type PaymentMethod = 'WECHAT' | 'ALIPAY' | 'UNION_PAY';

// 支付响应接口
interface PaymentResponse {
  orderId: string;
  paymentMethod: string;
  amount: number;
  qrCodeUrl: string;
  qrCodeImage: string;
  expireTime: string;
  timeoutSeconds: number;
  prepayId: string;
  accountName?: string;
  accountInfo?: string;
}

export default function PaymentPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get('orderId');

  const [orderInfo, setOrderInfo] = useState<OrderInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [paymentInfo, setPaymentInfo] = useState<PaymentResponse | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>('WECHAT');
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'processing' | 'success' | 'failed'>('pending');

  // 获取token
  const getToken = () => {
    return localStorage.getItem('token');
  };

  // 获取订单详情
  const fetchOrderInfo = async () => {
    if (!orderId) {
      alert('订单ID参数缺失');
      router.push('/orders');
      return;
    }

    try {
      const token = getToken();
      if (!token) {
        alert('请先登录！');
        router.push('/login');
        return;
      }

      const response = await fetch(`${API_BASE_URL}/orders/${orderId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200 && result.data) {
          setOrderInfo(result.data);
          
          // 计算剩余支付时间
          if (result.data.paymentTimeout) {
            const timeout = new Date(result.data.paymentTimeout).getTime();
            const now = new Date().getTime();
            const remaining = Math.max(0, Math.floor((timeout - now) / 1000));
            setTimeLeft(remaining);
          }
        } else {
          throw new Error(result.message || '订单信息获取失败');
        }
      } else {
        throw new Error('订单不存在或已过期');
      }
    } catch (error) {
      console.error('获取订单信息失败:', error);
      alert(error instanceof Error ? error.message : '获取订单信息失败');
      router.push('/orders');
    } finally {
      setLoading(false);
    }
  };

  // 生成支付二维码
  const generatePaymentQR = async () => {
    if (!orderInfo) return;

    try {
      setPaymentLoading(true);

      // 调用新的收款码API
      const response = await fetch(`${API_BASE_URL}/payment/qr/${selectedPaymentMethod.toLowerCase()}?amount=${orderInfo.actualAmount}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200 && result.data) {
          // 将收款码API的响应格式转换为支付页面期望的格式
          const paymentData = {
            orderId: orderInfo.orderId,
            paymentMethod: result.data.paymentMethod,
            amount: result.data.amount || orderInfo.actualAmount,
            qrCodeUrl: '', // 不需要
            qrCodeImage: result.data.qrCodeImage,
            expireTime: '', // 设置为30分钟后过期
            timeoutSeconds: 30 * 60, // 30分钟
            prepayId: result.data.qrId,
            accountName: result.data.accountName,
            accountInfo: result.data.accountInfo
          };
          
          setPaymentInfo(paymentData);
          setPaymentStatus('processing');
          setTimeLeft(30 * 60); // 30分钟倒计时
        } else {
          throw new Error(result.message || '获取收款码失败');
        }
      } else {
        throw new Error('获取收款码失败，请稍后重试');
      }
    } catch (error) {
      console.error('获取收款码失败:', error);
      alert(error instanceof Error ? error.message : '获取收款码失败，请稍后重试');
    } finally {
      setPaymentLoading(false);
    }
  };

  // 模拟支付（用于测试）
  const mockPayment = async () => {
    if (!orderInfo) return;

    try {
      const token = getToken();
      const response = await fetch(`${API_BASE_URL}/orders/${orderInfo.orderId}/mock-payment`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentMethod: selectedPaymentMethod,
          transactionId: `TEST_${Date.now()}`
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          setPaymentStatus('success');
          setTimeout(() => {
            router.push('/orders/paid');
          }, 2000);
        } else {
          throw new Error(result.message || '模拟支付失败');
        }
      } else {
        throw new Error('模拟支付失败');
      }
    } catch (error) {
      console.error('模拟支付失败:', error);
      alert(error instanceof Error ? error.message : '模拟支付失败');
      setPaymentStatus('failed');
    }
  };

  // 倒计时效果
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && paymentInfo) {
      // 支付超时
      setPaymentStatus('failed');
      alert('支付已超时，请重新下单');
    }
  }, [timeLeft, paymentInfo]);

  // 格式化时间
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 页面加载时获取订单信息
  useEffect(() => {
    fetchOrderInfo();
  }, [orderId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (!orderInfo) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😔</div>
          <h3 className="text-xl font-medium text-gray-900 mb-2">订单不存在</h3>
          <Link href="/orders" className="text-purple-600 hover:text-purple-700">
            返回订单列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-8">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                  L
                </div>
                <span className="text-xl font-bold text-gray-900">灵狐键创</span>
              </Link>
              <div className="hidden md:flex items-center space-x-6">
                <span className="text-purple-600 font-medium">订单支付</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="bg-gradient-to-r from-purple-600 to-cyan-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">订单支付</h1>
          <Link href="/orders" className="text-purple-600 hover:text-purple-700 transition-colors">
            ← 返回订单列表
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 订单信息 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 订单详情 */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-medium text-gray-900 mb-4">订单信息</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">订单号：</span>
                  <span className="font-medium">{orderInfo.orderId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">订单状态：</span>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    orderInfo.status === 'PENDING_PAYMENT' ? 'bg-orange-100 text-orange-600' :
                    orderInfo.status === 'PAID' ? 'bg-green-100 text-green-600' :
                    'bg-gray-100 text-gray-600'
                  }`}>
                    {orderInfo.status === 'PENDING_PAYMENT' ? '待支付' :
                     orderInfo.status === 'PAID' ? '已支付' : orderInfo.status}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">收货人：</span>
                  <span>{orderInfo.receiverName} {orderInfo.receiverPhone}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">收货地址：</span>
                  <span className="text-right">{orderInfo.receiverAddress}</span>
                </div>
              </div>
            </div>

            {/* 商品清单 */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-medium text-gray-900 mb-4">商品清单</h3>
              <div className="space-y-4">
                {orderInfo.orderItems.map((item, index) => (
                  <div key={index} className="flex items-start space-x-4 pb-4 border-b border-gray-100 last:border-b-0 last:pb-0">
                    <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                      {item.productImage ? (
                        <img 
                          src={item.productImage} 
                          alt={item.productName}
                          className="w-full h-full object-cover rounded-lg"
                        />
                      ) : (
                        <div className="text-2xl">🎯</div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{item.productName}</h4>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-gray-600">¥{item.unitPrice} × {item.quantity}</span>
                        <span className="font-medium text-red-600">¥{item.totalPrice}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 支付方式选择 */}
            {paymentStatus === 'pending' && (
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-medium text-gray-900 mb-4">选择支付方式</h3>
                <div className="grid grid-cols-3 gap-4">
                  {[
                    { method: 'WECHAT' as PaymentMethod, name: '微信支付', icon: '💬' },
                    { method: 'ALIPAY' as PaymentMethod, name: '支付宝', icon: '🅰️' },
                    { method: 'UNION_PAY' as PaymentMethod, name: '银联支付', icon: '💳' }
                  ].map((option) => (
                    <label key={option.method} className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value={option.method}
                        checked={selectedPaymentMethod === option.method}
                        onChange={(e) => setSelectedPaymentMethod(e.target.value as PaymentMethod)}
                        className="w-4 h-4 text-purple-600 mr-3"
                      />
                      <div className="flex items-center space-x-2">
                        <span className="text-2xl">{option.icon}</span>
                        <span>{option.name}</span>
                      </div>
                    </label>
                  ))}
                </div>
                <button
                  onClick={generatePaymentQR}
                  disabled={paymentLoading}
                  className="w-full mt-4 bg-gradient-to-r from-purple-600 to-cyan-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-shadow disabled:opacity-50"
                >
                  {paymentLoading ? '获取中...' : '获取收款码'}
                </button>
              </div>
            )}

            {/* 支付二维码 */}
            {paymentInfo && paymentStatus === 'processing' && (
              <div className="bg-white rounded-lg p-6 shadow-sm text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-4">扫码支付</h3>
                <div className="mb-4">
                  <div className="w-48 h-48 mx-auto bg-white border-2 border-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                    {paymentInfo.qrCodeImage ? (
                      <img 
                        src={paymentInfo.qrCodeImage} 
                        alt="支付二维码"
                        className="w-full h-full object-contain"
                      />
                    ) : (
                      <div className="text-center">
                        <div className="text-6xl mb-2">📱</div>
                        <p className="text-sm text-gray-600">扫描二维码支付</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {selectedPaymentMethod === 'WECHAT' ? '微信支付' :
                           selectedPaymentMethod === 'ALIPAY' ? '支付宝' : '银联支付'}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
                <p className="text-gray-600 mb-2">
                  支付金额：<span className="text-xl font-bold text-red-600">¥{orderInfo.actualAmount}</span>
                </p>
                {paymentInfo.accountName && (
                  <div className="bg-blue-50 rounded-lg p-3 mb-4">
                    <p className="text-sm text-blue-800">
                      <span className="font-medium">收款人：</span>{paymentInfo.accountName}
                    </p>
                    {paymentInfo.accountInfo && (
                      <p className="text-xs text-blue-600 mt-1">
                        {paymentInfo.accountInfo}
                      </p>
                    )}
                  </div>
                )}
                {timeLeft > 0 && (
                  <p className="text-orange-600 mb-4">
                    剩余时间：{formatTime(timeLeft)}
                  </p>
                )}
                <div className="flex space-x-3">
                  <button
                    onClick={mockPayment}
                    className="flex-1 bg-green-600 text-white py-2 rounded-lg font-medium hover:bg-green-700 transition-colors"
                  >
                    模拟支付成功
                  </button>
                  <button
                    onClick={() => {
                      setPaymentStatus('pending');
                      setPaymentInfo(null);
                    }}
                    className="flex-1 bg-gray-600 text-white py-2 rounded-lg font-medium hover:bg-gray-700 transition-colors"
                  >
                    重新选择
                  </button>
                </div>
              </div>
            )}

            {/* 支付成功 */}
            {paymentStatus === 'success' && (
              <div className="bg-white rounded-lg p-6 shadow-sm text-center">
                <div className="text-6xl mb-4">✅</div>
                <h3 className="text-xl font-bold text-green-600 mb-2">支付成功！</h3>
                <p className="text-gray-600 mb-4">订单已支付完成，正在跳转到订单详情...</p>
                <Link href="/orders/paid" className="inline-block bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                  查看订单
                </Link>
              </div>
            )}

            {/* 支付失败 */}
            {paymentStatus === 'failed' && (
              <div className="bg-white rounded-lg p-6 shadow-sm text-center">
                <div className="text-6xl mb-4">❌</div>
                <h3 className="text-xl font-bold text-red-600 mb-2">支付失败</h3>
                <p className="text-gray-600 mb-4">支付超时或失败，请重新支付</p>
                <button
                  onClick={() => {
                    setPaymentStatus('pending');
                    setPaymentInfo(null);
                    setTimeLeft(0);
                  }}
                  className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                >
                  重新支付
                </button>
              </div>
            )}
          </div>

          {/* 订单金额汇总 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-6 shadow-sm sticky top-24">
              <h3 className="text-lg font-medium text-gray-900 mb-4">订单金额</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">商品总计</span>
                  <span>¥{orderInfo.totalAmount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">运费</span>
                  <span className={orderInfo.shippingFee === 0 ? 'text-green-600' : ''}>
                    {orderInfo.shippingFee === 0 ? '免运费' : `¥${orderInfo.shippingFee}`}
                  </span>
                </div>
                <hr />
                <div className="flex justify-between text-lg font-bold">
                  <span>实付金额</span>
                  <span className="text-red-600">¥{orderInfo.actualAmount}</span>
                </div>
              </div>
              
              {orderInfo.status === 'PENDING_PAYMENT' && timeLeft > 0 && (
                <div className="mt-4 p-3 bg-orange-50 rounded-lg">
                  <div className="text-center">
                    <p className="text-sm text-orange-600">订单将在以下时间后自动取消</p>
                    <p className="text-lg font-bold text-orange-600">{formatTime(timeLeft)}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 