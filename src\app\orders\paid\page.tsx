'use client';

import React, { useState } from 'react';
import Link from 'next/link';

// 模拟已支付订单数据
const mockPaidOrders = [
  {
    id: 'ORD001',
    orderNumber: '2024031501',
    date: '2024-03-15',
    status: '已发货',
    statusColor: 'text-blue-600 bg-blue-100',
    items: [
      {
        id: 1,
        name: 'GMK CYL Rubreehose 橡皮软管',
        image: '🎯',
        price: 1499,
        quantity: 1,
        category: 'GMK',
        style: '现代简约'
      }
    ],
    totalAmount: 1499,
    shippingInfo: {
      carrier: '顺丰快递',
      trackingNumber: 'SF1234567890',
      estimatedDelivery: '2024-03-18'
    }
  },
  {
    id: 'ORD002',
    orderNumber: '2024031402',
    date: '2024-03-14',
    status: '配送中',
    statusColor: 'text-green-600 bg-green-100',
    items: [
      {
        id: 2,
        name: 'SP SA Motorsport Select 海湾赛车',
        image: '🎯',
        price: 1619,
        quantity: 1,
        category: 'SA',
        style: '赛车主题'
      },
      {
        id: 3,
        name: 'GMK CYL Hi Viz 高可视二色',
        image: '🎯',
        price: 1179,
        quantity: 1,
        category: 'GMK',
        style: '高对比'
      }
    ],
    totalAmount: 2798,
    shippingInfo: {
      carrier: '中通快递',
      trackingNumber: 'ZT9876543210',
      estimatedDelivery: '2024-03-17'
    }
  },
  {
    id: 'ORD003',
    orderNumber: '2024031203',
    date: '2024-03-12',
    status: '已完成',
    statusColor: 'text-gray-600 bg-gray-100',
    items: [
      {
        id: 4,
        name: 'Jellykey 玻璃机系列',
        image: '🎯',
        price: 899,
        quantity: 2,
        category: 'Jellykey',
        style: '艺术创意'
      }
    ],
    totalAmount: 1798,
    shippingInfo: {
      carrier: '京东快递',
      trackingNumber: 'JD5555666677',
      estimatedDelivery: '已送达'
    }
  }
];

export default function PaidOrdersPage() {
  const [selectedOrder, setSelectedOrder] = useState<string | null>(null);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-8">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                  L
                </div>
                <span className="text-xl font-bold text-gray-900">灵狐键创</span>
              </Link>
              <div className="hidden md:flex items-center space-x-6">
                <Link href="/shop" className="text-gray-600 hover:text-gray-900 transition-colors">
                  商城
                </Link>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="bg-gradient-to-r from-purple-600 to-cyan-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* 面包屑导航 */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Link href="/shop" className="hover:text-gray-700">商城</Link>
            <span>›</span>
            <Link href="/orders/paid" className="text-purple-600 font-medium">已支付订单</Link>
          </div>
        </div>
      </div>

      {/* 订单标签页 */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <Link href="/orders/paid" className="py-4 border-b-2 border-purple-600 text-purple-600 font-medium">
              ✅ 已支付订单
            </Link>
            <Link href="/orders/pending" className="py-4 text-gray-500 hover:text-gray-700 transition-colors">
              ⏳ 待支付订单
            </Link>
            <Link href="/cart" className="py-4 text-gray-500 hover:text-gray-700 transition-colors">
              🛒 购物车
            </Link>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">已支付订单</h1>
          <p className="text-gray-600 mt-2">查看您已完成支付的订单状态和物流信息</p>
        </div>

        {mockPaidOrders.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📦</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">暂无已支付订单</h3>
            <p className="text-gray-500 mb-6">您还没有任何已支付的订单</p>
            <Link href="/shop" className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
              去购物
            </Link>
          </div>
        ) : (
          <div className="space-y-6">
            {mockPaidOrders.map((order) => (
              <div key={order.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                {/* 订单头部 */}
                <div className="px-6 py-4 bg-gray-50 border-b flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div>
                      <p className="font-medium text-gray-900">订单号: {order.orderNumber}</p>
                      <p className="text-sm text-gray-500">下单时间: {order.date}</p>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${order.statusColor}`}>
                      {order.status}
                    </span>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-lg text-red-600">¥{order.totalAmount}</p>
                    <p className="text-sm text-gray-500">共{order.items.length}件商品</p>
                  </div>
                </div>

                {/* 商品列表 */}
                <div className="px-6 py-4">
                  <div className="space-y-4">
                    {order.items.map((item) => (
                      <div key={item.id} className="flex items-center space-x-4">
                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center text-2xl">
                          {item.image}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900">{item.name}</h3>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                              {item.category}
                            </span>
                            <span className="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded">
                              {item.style}
                            </span>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-gray-900">¥{item.price}</p>
                          <p className="text-sm text-gray-500">x{item.quantity}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 物流信息 */}
                {order.status !== '已完成' && (
                  <div className="px-6 py-4 bg-blue-50 border-t">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-blue-900">
                          🚚 {order.shippingInfo.carrier} - {order.shippingInfo.trackingNumber}
                        </p>
                        <p className="text-sm text-blue-600">
                          预计送达时间: {order.shippingInfo.estimatedDelivery}
                        </p>
                      </div>
                      <button className="text-blue-600 hover:text-blue-800 font-medium text-sm">
                        查看物流
                      </button>
                    </div>
                  </div>
                )}

                {/* 操作按钮 */}
                <div className="px-6 py-4 border-t bg-gray-50">
                  <div className="flex items-center justify-end space-x-3">
                    {order.status === '已完成' && (
                      <>
                        <button className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors">
                          申请售后
                        </button>
                        <button className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors">
                          评价商品
                        </button>
                      </>
                    )}
                    {order.status !== '已完成' && (
                      <button className="px-4 py-2 text-blue-600 border border-blue-300 rounded-lg hover:bg-blue-50 transition-colors">
                        查看物流
                      </button>
                    )}
                    <button className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                      再次购买
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 