// 支持的语言列表
export const locales = ['zh', 'en', 'ja', 'ko'] as const;
export type Locale = (typeof locales)[number];

// 默认语言
export const defaultLocale: Locale = 'zh';

// 语言映射
export const languageNames = {
  zh: '中文',
  en: 'English',
  ja: '日本語',
  ko: '한국어'
} as const;

// IP地址到国家代码的映射
const countryToLocale: Record<string, Locale> = {
  'CN': 'zh', // 中国
  'TW': 'zh', // 台湾
  'HK': 'zh', // 香港
  'MO': 'zh', // 澳门
  'SG': 'zh', // 新加坡
  'US': 'en', // 美国
  'GB': 'en', // 英国
  'CA': 'en', // 加拿大
  'AU': 'en', // 澳大利亚
  'NZ': 'en', // 新西兰
  'JP': 'ja', // 日本
  'KR': 'ko', // 韩国
  'KP': 'ko', // 朝鲜
};

// 检测用户IP并返回对应语言
export async function detectLocaleFromIP(): Promise<Locale> {
  try {
    // 创建一个AbortController来控制请求超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时
    
    // 使用免费的IP地理位置API
    const response = await fetch('https://ipapi.co/json/', {
      cache: 'no-store',
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (response.ok) {
      const data = await response.json();
      const countryCode = data.country_code;
      
      if (countryCode && countryToLocale[countryCode]) {
        return countryToLocale[countryCode];
      }
    }
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      console.warn('IP detection request timed out');
    } else {
      console.warn('Failed to detect locale from IP:', error);
    }
  }
  
  return defaultLocale;
}

// 从浏览器语言设置检测语言
export function detectLocaleFromBrowser(): Locale {
  if (typeof window === 'undefined') return defaultLocale;
  
  const browserLang = navigator.language.toLowerCase();
  
  // 检查完全匹配
  for (const locale of locales) {
    if (browserLang === locale || browserLang.startsWith(`${locale}-`)) {
      return locale;
    }
  }
  
  // 检查语言代码匹配
  if (browserLang.startsWith('zh')) return 'zh';
  if (browserLang.startsWith('en')) return 'en';
  if (browserLang.startsWith('ja')) return 'ja';
  if (browserLang.startsWith('ko')) return 'ko';
  
  return defaultLocale;
}

// 获取存储的语言设置
export function getStoredLocale(): Locale | null {
  if (typeof window === 'undefined') return null;
  
  const stored = localStorage.getItem('locale');
  if (stored && locales.includes(stored as Locale)) {
    return stored as Locale;
  }
  
  return null;
}

// 保存语言设置
export function setStoredLocale(locale: Locale): void {
  if (typeof window === 'undefined') return;
  localStorage.setItem('locale', locale);
}

// 获取当前语言（优先级：存储的设置 > IP检测 > 浏览器设置 > 默认语言）
export async function getCurrentLocale(): Promise<Locale> {
  // 1. 检查存储的设置
  const stored = getStoredLocale();
  if (stored) return stored;
  
  // 2. 检查IP地理位置
  const ipLocale = await detectLocaleFromIP();
  if (ipLocale !== defaultLocale) {
    setStoredLocale(ipLocale);
    return ipLocale;
  }
  
  // 3. 检查浏览器语言
  const browserLocale = detectLocaleFromBrowser();
  if (browserLocale !== defaultLocale) {
    setStoredLocale(browserLocale);
    return browserLocale;
  }
  
  // 4. 返回默认语言
  return defaultLocale;
}

// 验证语言代码是否有效
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
} 