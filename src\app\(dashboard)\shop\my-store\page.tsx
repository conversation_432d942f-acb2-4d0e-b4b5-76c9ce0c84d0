'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import ImageWithFallback from '@/components/ui/ImageWithFallback';

// API配置
const API_BASE_URL = 'http://localhost:8080/api';

interface ShopInfo {
  shopId: string;
  shopName: string;
  shopDescription: string;
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  businessAddress: string;
  shopStyle: string;
  hasDesigner: boolean;
  worksPath: string[];
  designFiles: string[];
  productionFiles: string[];
  status: string;
  statusDescription: string;
  applyTime: string;
  reviewTime?: string;
  reviewComment?: string;
}

interface ShopApplyForm {
  // 基本信息
  shopName: string;
  shopDescription: string;
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  businessAddress: string;
  shopStyle: string;
  hasDesigner: boolean;
  
  // 设计稿文件
  designFiles: File[];
  
  // 生产证明文件
  productionFiles: File[];
  
  // 设计稿规范确认
  designSpecConfirmed: boolean;
  
  // 生产能力确认
  productionCapacityConfirmed: boolean;
}

export default function MyStorePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [hasShop, setHasShop] = useState(false);
  const [shopInfo, setShopInfo] = useState<ShopInfo | null>(null);
  const [showApplyForm, setShowApplyForm] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(1);

  // 调试信息
  const [debugInfo, setDebugInfo] = useState('');

  // 申请表单状态
  const [applyForm, setApplyForm] = useState<ShopApplyForm>({
    shopName: '',
    shopDescription: '',
    contactName: '',
    contactPhone: '',
    contactEmail: '',
    businessAddress: '',
    shopStyle: '',
    hasDesigner: false,
    designFiles: [],
    productionFiles: [],
    designSpecConfirmed: false,
    productionCapacityConfirmed: false
  });

  // 检查店铺状态
  const checkShopStatus = async () => {
    try {
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');
      
      const tokenPreview = token ? `${token.substring(0, 20)}...` : 'null';
      setDebugInfo(`Token: ${token ? '存在' : '不存在'} (${tokenPreview}), User: ${user ? '存在' : '不存在'}`);
      
      if (!token || token.trim() === '') {
        setDebugInfo(prev => prev + ' - Token为空，用户未登录');
        setHasShop(false);
        setLoading(false);
        return;
      }

      const tokenParts = token.split('.');
      if (tokenParts.length !== 3) {
        setDebugInfo(prev => prev + ` - Token格式错误，应该有3部分，实际有${tokenParts.length}部分`);
        setHasShop(false);
        setLoading(false);
        return;
      }

      setDebugInfo(prev => prev + ' - Token格式正确，开始API调用');

      const response = await fetch(`${API_BASE_URL}/shop/my-shop`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      setDebugInfo(prev => prev + ` - API响应状态: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        setDebugInfo(prev => prev + ` - API响应成功: ${result.code === 200 ? '成功' : '失败'}`);
        
        if ((result.code === 200 || result.success) && result.data) {
          const application = result.data;
          setDebugInfo(prev => prev + ` - 找到店铺申请记录，状态: ${application.status}`);
          
          setHasShop(true);
          setShopInfo({
            shopId: application.shopId,
            shopName: application.shopName,
            shopDescription: application.shopDescription || '',
            contactName: application.contactName || '',
            contactPhone: application.contactPhone || '',
            contactEmail: application.contactEmail || '',
            businessAddress: application.businessAddress || '',
            shopStyle: application.shopStyle,
            hasDesigner: application.hasDesigner,
            worksPath: application.worksPath || [],
            designFiles: application.designFiles || [],
            productionFiles: application.productionFiles || [],
            status: application.status,
            statusDescription: application.status === 'APPROVED' ? '已通过审核' : 
                               application.status === 'PENDING' ? '审核中' : '审核未通过',
            applyTime: application.applyTime,
            reviewTime: application.reviewTime,
            reviewComment: application.reviewComment
          });
        } else {
          setDebugInfo(prev => prev + ` - 没有店铺申请记录`);
          setHasShop(false);
        }
      } else if (response.status === 404) {
        setDebugInfo(prev => prev + ' - 没有找到店铺申请记录(404)');
        setHasShop(false);
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('检查店铺状态失败:', error);
      setDebugInfo(prev => prev + ` - 检查店铺状态异常: ${error instanceof Error ? error.message : '未知错误'}`);
      setHasShop(false);
    } finally {
      setLoading(false);
    }
  };

  // 上传文件到服务器
  const uploadFiles = async (files: File[], type: 'design' | 'production'): Promise<string[]> => {
    const uploadedPaths: string[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('uploadType', type === 'design' ? 'WORK' : 'PRODUCTION');
        
        const response = await fetch(`${API_BASE_URL}/upload`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: formData
        });
        
        let result;
        try {
          const responseText = await response.text();
          result = responseText ? JSON.parse(responseText) : {};
        } catch (parseError) {
          throw new Error(`服务器响应格式错误`);
        }
        
        if (response.ok && (result.success || result.code === 200)) {
          const fileName = result.data?.fileName || result.data?.filePath || file.name;
          uploadedPaths.push(fileName);
          setUploadProgress(30 + (i + 1) / files.length * 40);
        } else {
          throw new Error(result.message || '文件上传失败');
        }
      } catch (error) {
        throw new Error(`上传文件 ${file.name} 失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }
    
    return uploadedPaths;
  };

  // 申请开通店铺
  const handleApplyShop = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      // 验证所有步骤是否完成
      if (!validateStep(1) || !validateStep(2) || !validateStep(3) || !validateStep(4)) {
        alert('请完成所有必填步骤');
        return;
      }

      setUploading(true);
      setUploadProgress(10);

      // 上传设计稿文件
      const designPaths = await uploadFiles(applyForm.designFiles, 'design');
      setUploadProgress(50);

      // 上传生产证明文件
      const productionPaths = await uploadFiles(applyForm.productionFiles, 'production');
      setUploadProgress(80);

      // 提交店铺申请
      const requestData = {
        shopName: applyForm.shopName,
        shopDescription: applyForm.shopDescription,
        contactName: applyForm.contactName,
        contactPhone: applyForm.contactPhone,
        contactEmail: applyForm.contactEmail,
        businessAddress: applyForm.businessAddress,
        shopStyle: applyForm.shopStyle,
        hasDesigner: applyForm.hasDesigner,
        designFiles: designPaths,
        productionFiles: productionPaths,
        worksPath: designPaths // 保持兼容性
      };

      const response = await fetch(`${API_BASE_URL}/shop/apply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestData)
      });

      let result;
      try {
        const responseText = await response.text();
        result = responseText ? JSON.parse(responseText) : {};
      } catch (parseError) {
        throw new Error(`服务器响应格式错误，状态码：${response.status}`);
      }

      setUploadProgress(90);

      if (response.ok && (result.code === 200 || result.success)) {
        setUploadProgress(100);
        alert('店铺申请提交成功！我们将在1-3个工作日内完成审核，请耐心等待。');
        setShowApplyForm(false);
        setCurrentStep(1);
        setApplyForm({
          shopName: '',
          shopDescription: '',
          contactName: '',
          contactPhone: '',
          contactEmail: '',
          businessAddress: '',
          shopStyle: '',
          hasDesigner: false,
          designFiles: [],
          productionFiles: [],
          designSpecConfirmed: false,
          productionCapacityConfirmed: false
        });
        
        // 设置状态变更标志并立即更新状态，而不是依赖定时器
        localStorage.setItem('shopStatusChanged', 'true');
        localStorage.setItem('lastStatusCheck', Date.now().toString());
        setTimeout(() => {
          checkShopStatus();
        }, 1000);
      } else {
        throw new Error(result.message || `提交失败，状态码：${response.status}`);
      }
    } catch (error) {
      console.error('申请店铺错误:', error);
      alert(`申请失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  // 处理文件选择 - 设计稿
  const handleDesignFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const fileList = Array.from(files);
      const validFiles = fileList.filter(file => {
        const isValidType = file.type.startsWith('image/');
        const isValidSize = file.size <= 10 * 1024 * 1024;
        return isValidType && isValidSize;
      });

      if (validFiles.length !== fileList.length) {
        alert('只能上传图片文件，且文件大小不能超过10MB');
      }

      setApplyForm(prev => ({
        ...prev,
        designFiles: [...prev.designFiles, ...validFiles]
      }));
    }
  };

  // 处理文件选择 - 生产证明
  const handleProductionFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const fileList = Array.from(files);
      const validFiles = fileList.filter(file => {
        const isValidType = file.type.startsWith('image/') || file.type.includes('pdf');
        const isValidSize = file.size <= 10 * 1024 * 1024;
        return isValidType && isValidSize;
      });

      if (validFiles.length !== fileList.length) {
        alert('只能上传图片或PDF文件，且文件大小不能超过10MB');
      }

      setApplyForm(prev => ({
        ...prev,
        productionFiles: [...prev.productionFiles, ...validFiles]
      }));
    }
  };

  // 删除设计稿文件
  const removeDesignFile = (index: number) => {
    setApplyForm(prev => ({
      ...prev,
      designFiles: prev.designFiles.filter((_, i) => i !== index)
    }));
  };

  // 删除生产证明文件
  const removeProductionFile = (index: number) => {
    setApplyForm(prev => ({
      ...prev,
      productionFiles: prev.productionFiles.filter((_, i) => i !== index)
    }));
  };

  // 步骤验证
  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1: // 设计稿规范
        return applyForm.designSpecConfirmed;
      case 2: // 生产能力
        return applyForm.productionCapacityConfirmed; // 只需要勾选确认复选框
      case 3: // 店铺信息
        return !!(applyForm.shopName && applyForm.shopDescription && applyForm.contactName && 
                  applyForm.contactPhone && applyForm.contactEmail && applyForm.shopStyle);
      case 4: // 文件上传
        return applyForm.designFiles.length > 0 && applyForm.productionFiles.length > 0; // 需要上传设计稿和生产证明
      default:
        return false;
    }
  };

  // 下一步
  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 4));
    } else {
      alert('请完成当前步骤的所有必填项');
    }
  };

  // 上一步
  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  useEffect(() => {
    checkShopStatus();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#2a1f3e] via-[#1e1432] to-[#0f0a1a] flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-purple-300 border-t-cyan-400 rounded-full animate-spin mx-auto"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-8 h-8 bg-cyan-400 rounded-full animate-pulse"></div>
            </div>
          </div>
                                  <p className="mt-6 text-gray-300 text-lg">正在加载店铺信息...</p>
          {debugInfo && (
            <div className="mt-4 p-4 bg-gray-100 rounded-lg text-sm text-left max-w-md mx-auto">
              <h4 className="font-semibold mb-2">调试信息：</h4>
              <pre className="whitespace-pre-wrap">{debugInfo}</pre>
            </div>
          )}
        </div>
      </div>
    );
  }

  // 没有店铺的情况
  if (!hasShop) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#2a1f3e] via-[#1e1432] to-[#0f0a1a] relative overflow-hidden">
        {/* 动态背景装饰 */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-radial from-purple-800/10 via-transparent to-transparent"></div>
          <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-cyan-400/20 to-purple-500/20 rounded-full blur-xl animate-pulse-slow"></div>
          <div className="absolute top-40 right-20 w-48 h-48 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-xl animate-float"></div>
          <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-r from-pink-500/20 to-purple-600/20 rounded-full blur-xl animate-float-reverse"></div>
          <div className="absolute bottom-40 right-10 w-24 h-24 bg-gradient-to-r from-cyan-400/20 to-blue-500/20 rounded-full blur-xl animate-pulse-slow"></div>
        </div>

        {/* 面包屑导航 */}
        <div className="relative z-10 bg-[#2a1f3e]/80 backdrop-blur-lg border-b border-purple-800/30 px-6 py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link href="/dashboard" className="text-gray-300 hover:text-white transition-colors">
              仪表盘
            </Link>
            <span className="text-gray-300">&gt;</span>
            <span className="text-white">我的店铺</span>
          </nav>
        </div>

        <div className="relative z-10 max-w-6xl mx-auto px-6 py-12">
          {/* 主要内容区域 */}
          <div className="text-center mb-12">
            <div className="relative inline-block mb-8">
              <div className="w-32 h-32 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white shadow-2xl">
                <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2z" />
                </svg>
              </div>
              <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center text-yellow-800 shadow-lg">
                <span className="text-xl">🏪</span>
              </div>
            </div>
            
            <h1 className="text-4xl font-bold mb-4 text-white">
              您暂未开通店铺
            </h1>
            <p className="text-xl mb-8 text-gray-300">
              立即开通店铺，开启您的设计变现之旅
            </p>
            
            {/* 开通流程说明 */}
            <div className="bg-[#2a1f3e]/80 backdrop-blur-lg rounded-2xl p-8 max-w-5xl mx-auto shadow-xl border border-purple-800/30 mb-8">
              <h2 className="text-2xl font-semibold mb-6 text-white">开通店铺流程</h2>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl flex items-center justify-center text-white shadow-lg mx-auto mb-4">
                    <span className="text-2xl">📋</span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">1. 设计稿规范</h3>
                  <p className="text-sm text-gray-300">了解并确认设计稿规范要求</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center text-white shadow-lg mx-auto mb-4">
                    <span className="text-2xl">🏭</span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">2. 生产能力</h3>
                  <p className="text-sm text-gray-300">完成生产环节，确保有实际商品</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl flex items-center justify-center text-white shadow-lg mx-auto mb-4">
                    <span className="text-2xl">🏪</span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">3. 店铺信息</h3>
                  <p className="text-sm text-gray-300">填写店铺名称、简介、联系方式</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-red-500 rounded-xl flex items-center justify-center text-white shadow-lg mx-auto mb-4">
                    <span className="text-2xl">📁</span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">4. 文件上传</h3>
                  <p className="text-sm text-gray-300">上传设计稿和生产证明文件</p>
                </div>
              </div>
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-300">审核通过后，即可在商城上架销售</p>
              </div>
            </div>

            <div className="bg-[#2a1f3e]/80 backdrop-blur-lg rounded-2xl p-8 max-w-4xl mx-auto shadow-xl border border-purple-800/30">
              <h2 className="text-2xl font-semibold mb-6 text-white">开通店铺的优势</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-blue-500 rounded-xl flex items-center justify-center text-white shadow-lg mx-auto mb-4">
                    <span className="text-2xl">💰</span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">获得收益</h3>
                  <p className="text-sm text-gray-300">销售您的设计作品，获得持续收益</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl flex items-center justify-center text-white shadow-lg mx-auto mb-4">
                    <span className="text-2xl">🎨</span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">展示才华</h3>
                  <p className="text-sm text-gray-300">展示您的创意设计，建立个人品牌</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-red-500 rounded-xl flex items-center justify-center text-white shadow-lg mx-auto mb-4">
                    <span className="text-2xl">🤝</span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">社区互动</h3>
                  <p className="text-sm text-gray-300">与其他设计师和用户建立联系</p>
                </div>
              </div>
            </div>

            <div className="mt-12 space-x-6">
              <button
                onClick={() => setShowApplyForm(true)}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-lg font-semibold px-8 py-4 rounded-xl shadow-lg transform transition-all duration-200 hover:scale-105 hover:shadow-xl"
              >
                立即开通店铺 ✨
              </button>
              <Link
                href="/dashboard"
                className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-xl transition-all duration-200 block text-center opacity-100 dashboard-return-button"
                onClick={(e) => {
                  e.preventDefault();
                  window.location.href = '/dashboard';  // 使用直接跳转而不是Next.js路由
                }}
              >
                <span className="opacity-100">🏠 返回仪表盘</span>
              </Link>
            </div>
          </div>

          {/* 申请开通店铺表单 */}
          {showApplyForm && (
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
              <div className="bg-white rounded-2xl p-8 w-full max-w-4xl max-h-[90vh] overflow-y-auto shadow-2xl">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold" style={{color: '#111827'}}>申请开通店铺</h3>
                  <button
                    onClick={() => setShowApplyForm(false)}
                    className="text-gray-700 hover:text-gray-900 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                {/* 步骤指示器 */}
                <div className="mb-8">
                  <div className="flex items-center justify-between">
                    {[
                      { step: 1, title: '设计稿规范', icon: '📋' },
                      { step: 2, title: '生产能力', icon: '🏭' },
                      { step: 3, title: '店铺信息', icon: '🏪' },
                      { step: 4, title: '作品上传', icon: '📁' }
                    ].map((item, index) => (
                      <div key={item.step} className="flex items-center">
                        <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all ${
                          currentStep >= item.step 
                            ? 'bg-blue-600 border-blue-600 text-white' 
                            : 'bg-white border-gray-300 text-gray-700'
                        }`}>
                          <span className="text-sm">{item.icon}</span>
                        </div>
                        <div className="ml-3 text-sm">
                          <p className="font-medium" style={{color: currentStep >= item.step ? '#2563eb' : '#374151'}}>
                            步骤 {item.step}
                          </p>
                          <p style={{color: currentStep >= item.step ? '#111827' : '#374151'}}>
                            {item.title}
                          </p>
                        </div>
                        {index < 3 && (
                          <div className={`flex-1 h-0.5 mx-4 ${
                            currentStep > item.step ? 'bg-blue-600' : 'bg-gray-300'
                          }`}></div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* 步骤内容 */}
                <div className="min-h-[400px]">
                  {/* 步骤1: 设计稿规范 */}
                  {currentStep === 1 && (
                    <div className="space-y-6">
                      <div className="text-center mb-6">
                        <h4 className="text-xl font-bold mb-2" style={{color: '#111827'}}>设计稿规范要求</h4>
                        <p style={{color: '#374151'}}>请仔细阅读以下规范，确保您的设计稿符合商城要求</p>
                      </div>

                      <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 border border-blue-200">
                        <h5 className="text-lg font-semibold mb-4" style={{color: '#1e3a8a'}}>📋 设计稿技术规范</h5>
                        <ul className="space-y-3" style={{color: '#1e40af'}}>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#2563eb'}}>•</span>
                            <span style={{color: '#1e40af'}}><strong>分辨率要求：</strong>不低于300DPI，确保打印清晰度</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#2563eb'}}>•</span>
                            <span style={{color: '#1e40af'}}><strong>文件格式：</strong>支持AI、PSD、PNG、JPG格式，建议提供矢量文件</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#2563eb'}}>•</span>
                            <span style={{color: '#1e40af'}}><strong>颜色模式：</strong>RGB模式用于屏幕显示，CMYK模式用于印刷</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#2563eb'}}>•</span>
                            <span style={{color: '#1e40af'}}><strong>尺寸标准：</strong>符合常见键帽规格（如Cherry MX标准）</span>
                          </li>
                        </ul>
                      </div>

                      <div className="bg-gradient-to-br from-yellow-50 to-orange-100 rounded-xl p-6 border border-yellow-200">
                        <h5 className="text-lg font-semibold mb-4" style={{color: '#9a3412'}}>🎨 设计质量要求</h5>
                        <ul className="space-y-3" style={{color: '#c2410c'}}>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#ea580c'}}>•</span>
                            <span style={{color: '#c2410c'}}><strong>原创性：</strong>必须为原创设计，不得侵犯他人知识产权</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#ea580c'}}>•</span>
                            <span style={{color: '#c2410c'}}><strong>实用性：</strong>设计应考虑实际使用场景和用户体验</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#ea580c'}}>•</span>
                            <span style={{color: '#c2410c'}}><strong>美观性：</strong>具有良好的视觉效果和艺术价值</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#ea580c'}}>•</span>
                            <span style={{color: '#c2410c'}}><strong>主题一致：</strong>整套设计保持统一的风格和主题</span>
                          </li>
                        </ul>
                      </div>

                      <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                        <h5 className="font-semibold mb-2" style={{color: '#991b1b'}}>⚠️ 禁止内容</h5>
                        <p className="text-sm" style={{color: '#b91c1c'}}>
                          不得包含违法违规、暴力血腥、色情低俗、政治敏感等内容，
                          不得使用未经授权的版权素材（如影视作品、游戏角色等）
                        </p>
                      </div>

                      <div className="flex items-center p-4 bg-gray-50 rounded-xl">
                        <input
                          type="checkbox"
                          id="designSpecConfirmed"
                          checked={applyForm.designSpecConfirmed}
                          onChange={(e) => setApplyForm(prev => ({ ...prev, designSpecConfirmed: e.target.checked }))}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                        />
                        <label htmlFor="designSpecConfirmed" className="ml-3 text-sm" style={{color: '#374151'}}>
                          我已仔细阅读并同意遵守以上设计稿规范要求 *
                        </label>
                      </div>
                    </div>
                  )}

                  {/* 步骤2: 生产能力验证 */}
                  {currentStep === 2 && (
                    <div className="space-y-6">
                      <div className="text-center mb-6">
                        <h4 className="text-xl font-bold mb-2" style={{color: '#111827'}}>生产能力验证</h4>
                        <p style={{color: '#374151'}}>请确认您具备实际的商品生产和供应能力</p>
                      </div>

                      <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-xl p-6 border border-green-200">
                        <h5 className="text-lg font-semibold mb-4" style={{color: '#14532d'}}>🏭 生产能力要求</h5>
                        <ul className="space-y-3" style={{color: '#047857'}}>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#059669'}}>•</span>
                            <span style={{color: '#047857'}}><strong>生产资质：</strong>具备合法的生产经营资质或与生产商的合作协议</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#059669'}}>•</span>
                            <span style={{color: '#047857'}}><strong>质量保证：</strong>确保产品质量符合行业标准，提供质检报告</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#059669'}}>•</span>
                            <span style={{color: '#047857'}}><strong>供货能力：</strong>具备稳定的供货能力，能够按时交付订单</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#059669'}}>•</span>
                            <span style={{color: '#047857'}}><strong>售后服务：</strong>提供完善的售后服务和质量保障</span>
                          </li>
                        </ul>
                      </div>

                      <div className="bg-blue-50 p-6 rounded-xl border border-blue-100">
                        <div className="flex items-center mb-4">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <span className="text-xl">📝</span>
                          </div>
                          <h5 className="text-lg font-semibold" style={{color: '#1e3a8a'}}>物流要求</h5>
                        </div>
                        <ul className="space-y-3" style={{color: '#1e40af'}}>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#2563eb'}}>•</span>
                            <span style={{color: '#1e40af'}}><strong>国内物流：</strong>需要能够处理国内物流配送</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#2563eb'}}>•</span>
                            <span style={{color: '#1e40af'}}><strong>发货时间：</strong>提供合理的发货时间和物流跟踪</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2" style={{color: '#2563eb'}}>•</span>
                            <span style={{color: '#1e40af'}}><strong>售后问题：</strong>能够处理退换货等售后问题</span>
                          </li>
                        </ul>
                      </div>

                      <div className="flex items-center p-4 bg-gray-50 rounded-xl">
                        <input
                          type="checkbox"
                          id="productionCapacityConfirmed"
                          checked={applyForm.productionCapacityConfirmed}
                          onChange={(e) => setApplyForm(prev => ({ ...prev, productionCapacityConfirmed: e.target.checked }))}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                        />
                        <label htmlFor="productionCapacityConfirmed" className="ml-3 text-sm" style={{color: '#374151'}}>
                          我确认具备实际的商品生产和供应能力，能够保质保量完成订单 *
                        </label>
                      </div>
                    </div>
                  )}

                  {/* 步骤3: 店铺信息 */}
                  {currentStep === 3 && (
                    <div className="space-y-6">
                      <div className="text-center mb-6">
                        <h4 className="text-xl font-bold mb-2" style={{color: '#111827'}}>填写店铺信息</h4>
                        <p style={{color: '#374151'}}>请详细填写您的店铺信息，包括名称、简介、联系方式等</p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-semibold mb-2" style={{color: '#374151'}}>
                            店铺名称 *
                          </label>
                          <input
                            type="text"
                            value={applyForm.shopName}
                            onChange={(e) => setApplyForm(prev => ({ ...prev, shopName: e.target.value }))}
                            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            placeholder="请输入您的店铺名称"
                            maxLength={50}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-semibold mb-2" style={{color: '#374151'}}>
                            店铺风格 *
                          </label>
                          <select
                            value={applyForm.shopStyle}
                            onChange={(e) => setApplyForm(prev => ({ ...prev, shopStyle: e.target.value }))}
                            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-gray-900"
                          >
                            <option value="" disabled>请选择店铺风格</option>
                            <option value="现代简约">🎯 现代简约</option>
                            <option value="复古经典">👑 复古经典</option>
                            <option value="游戏主题">🎮 游戏主题</option>
                            <option value="艺术创意">🎨 艺术创意</option>
                            <option value="商务办公">💼 商务办公</option>
                            <option value="科技未来">🚀 科技未来</option>
                            <option value="自然风光">🌳 自然风光</option>
                            <option value="其他风格">🌈 其他风格</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-semibold mb-2" style={{color: '#374151'}}>
                            联系人姓名 *
                          </label>
                          <input
                            type="text"
                            value={applyForm.contactName}
                            onChange={(e) => setApplyForm(prev => ({ ...prev, contactName: e.target.value }))}
                            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            placeholder="请输入联系人姓名"
                            maxLength={30}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-semibold mb-2" style={{color: '#374151'}}>
                            联系电话 *
                          </label>
                          <input
                            type="tel"
                            value={applyForm.contactPhone}
                            onChange={(e) => setApplyForm(prev => ({ ...prev, contactPhone: e.target.value }))}
                            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            placeholder="请输入联系电话"
                            maxLength={20}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-semibold mb-2" style={{color: '#374151'}}>
                            联系邮箱 *
                          </label>
                          <input
                            type="email"
                            value={applyForm.contactEmail}
                            onChange={(e) => setApplyForm(prev => ({ ...prev, contactEmail: e.target.value }))}
                            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            placeholder="请输入联系邮箱"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-semibold mb-2" style={{color: '#374151'}}>
                            经营地址
                          </label>
                          <input
                            type="text"
                            value={applyForm.businessAddress}
                            onChange={(e) => setApplyForm(prev => ({ ...prev, businessAddress: e.target.value }))}
                            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            placeholder="请输入经营地址（可选）"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-semibold mb-2" style={{color: '#374151'}}>
                          店铺简介 *
                        </label>
                        <textarea
                          value={applyForm.shopDescription}
                          onChange={(e) => setApplyForm(prev => ({ ...prev, shopDescription: e.target.value }))}
                          rows={4}
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none text-gray-900"
                          placeholder="请简要介绍您的店铺特色、经营理念、产品优势等（建议50-200字）"
                          maxLength={500}
                        />
                        <p className="text-xs mt-1" style={{color: '#6b7280'}}>
                          {applyForm.shopDescription.length}/500 字
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-semibold mb-2" style={{color: '#374151'}}>
                          入驻设计师
                        </label>
                        <div className="flex items-center p-4 bg-gray-50 rounded-xl">
                          <input
                            type="checkbox"
                            id="hasDesigner"
                            checked={applyForm.hasDesigner}
                            onChange={(e) => setApplyForm(prev => ({ ...prev, hasDesigner: e.target.checked }))}
                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                          />
                          <label htmlFor="hasDesigner" className="ml-3 text-sm text-gray-800" style={{color: '#374151'}}>
                            我有专业的入驻设计师或设计团队
                          </label>
                        </div>
                        <p className="text-xs mt-1" style={{color: '#6b7280'}}>如果您有专业设计师团队，可以勾选此选项</p>
                      </div>
                    </div>
                  )}

                  {/* 步骤4: 文件上传 */}
                  {currentStep === 4 && (
                    <div className="space-y-6">
                      <div className="text-center mb-6">
                        <h4 className="text-xl font-bold mb-2" style={{color: '#111827'}}>上传必要文件</h4>
                        <p style={{color: '#374151'}}>请上传您的键帽设计作品和生产证明文件，作为审核参考</p>
                      </div>

                      <div>
                        <label className="block text-sm font-semibold mb-2" style={{color: '#374151'}}>
                          设计稿文件 * <span className="text-xs" style={{color: '#6b7280'}}>(最多上传5个文件，每个文件不超过10MB)</span>
                        </label>
                        <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors">
                          <input
                            type="file"
                            multiple
                            accept="image/*"
                            onChange={handleDesignFileChange}
                            className="hidden"
                            id="design-file-upload"
                            disabled={applyForm.designFiles.length >= 5}
                          />
                          <label
                            htmlFor="design-file-upload"
                            className={`cursor-pointer ${applyForm.designFiles.length >= 5 ? 'cursor-not-allowed opacity-50' : ''}`}
                          >
                            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                              </svg>
                            </div>
                            <p className="text-gray-700" style={{color: '#374151'}}>
                              {applyForm.designFiles.length >= 5 ? '已达到最大上传数量' : '点击上传您的设计稿'}
                            </p>
                            <p className="text-xs mt-1" style={{color: '#6b7280'}}>支持 JPG、PNG、GIF 格式</p>
                          </label>
                        </div>
                        
                        {applyForm.designFiles.length > 0 && (
                          <div className="mt-4 grid grid-cols-2 gap-3">
                            {applyForm.designFiles.map((file, index) => (
                              <div key={index} className="relative bg-gray-50 rounded-lg p-3 group">
                                <div className="flex items-center space-x-3">
                                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
                                    <p className="text-xs text-gray-700">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                                  </div>
                                  <button
                                    onClick={() => removeDesignFile(index)}
                                    className="text-red-500 hover:text-red-600 transition-colors opacity-0 group-hover:opacity-100"
                                    type="button"
                                  >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-semibold mb-2" style={{color: '#374151'}}>
                          生产证明文件 * <span className="text-xs" style={{color: '#6b7280'}}>(营业执照、生产许可证、合作协议、质检报告等)</span>
                        </label>
                        <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors">
                          <input
                            type="file"
                            multiple
                            accept="image/*,.pdf"
                            onChange={handleProductionFileChange}
                            className="hidden"
                            id="production-file-upload"
                            disabled={applyForm.productionFiles.length >= 5}
                          />
                          <label
                            htmlFor="production-file-upload"
                            className={`cursor-pointer ${applyForm.productionFiles.length >= 5 ? 'cursor-not-allowed opacity-50' : ''}`}
                          >
                            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                              </svg>
                            </div>
                            <p className="text-gray-700" style={{color: '#374151'}}>
                              {applyForm.productionFiles.length >= 5 ? '已达到最大上传数量' : '点击上传生产证明'}
                            </p>
                            <p className="text-xs mt-1" style={{color: '#6b7280'}}>支持 PDF、DOCX、JPG 格式</p>
                          </label>
                        </div>
                        
                        {applyForm.productionFiles.length > 0 && (
                          <div className="mt-4 grid grid-cols-2 gap-3">
                            {applyForm.productionFiles.map((file, index) => (
                              <div key={index} className="relative bg-gray-50 rounded-lg p-3 group">
                                <div className="flex items-center space-x-3">
                                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                    {file.type.includes('pdf') ? (
                                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                      </svg>
                                    ) : (
                                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                      </svg>
                                    )}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
                                    <p className="text-xs text-gray-700">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                                  </div>
                                  <button
                                    onClick={() => removeProductionFile(index)}
                                    className="text-red-500 hover:text-red-600 transition-colors opacity-0 group-hover:opacity-100"
                                    type="button"
                                  >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>

                      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                        <h4 className="font-semibold mb-2" style={{color: '#1e40af'}}>📋 最终确认</h4>
                        <ul className="text-sm space-y-1" style={{color: '#1d4ed8'}}>
                          <li>• 请确保所有信息填写完整且真实有效</li>
                          <li>• 设计稿和生产证明文件已正确上传</li>
                          <li>• 店铺审核通常需要1-3个工作日</li>
                          <li>• 审核结果将通过站内消息和邮件通知您</li>
                        </ul>
                      </div>

                      {/* 上传进度条 */}
                      {uploading && (
                        <div className="mb-4">
                          <div className="flex justify-between text-sm mb-1" style={{color: '#374151'}}>
                            <span>上传进度</span>
                            <span>{uploadProgress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${uploadProgress}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* 步骤导航按钮 */}
                <div className="flex justify-between mt-8">
                  <button
                    onClick={prevStep}
                    disabled={currentStep === 1}
                    className="px-6 py-3 bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed text-gray-700 font-semibold rounded-xl transition-all duration-200"
                  >
                    上一步
                  </button>
                  
                  {currentStep < 4 ? (
                    <button
                      onClick={nextStep}
                      className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl transition-all duration-200 transform hover:scale-105"
                    >
                      下一步
                    </button>
                  ) : (
                    <button
                      onClick={handleApplyShop}
                      disabled={uploading || !validateStep(1) || !validateStep(2) || !validateStep(3) || !validateStep(4)}
                      className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold rounded-xl transition-all duration-200 transform hover:scale-105"
                    >
                      {uploading ? (
                        <div className="flex items-center justify-center">
                          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                          提交中...
                        </div>
                      ) : (
                        '提交审核 🚀'
                      )}
                    </button>
                  )}
                  
                  <button
                    onClick={() => setShowApplyForm(false)}
                    disabled={uploading}
                    className="px-6 py-3 bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed text-gray-700 font-semibold rounded-xl transition-all duration-200"
                  >
                    取消
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // 有店铺的情况 - 显示店铺信息
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#2a1f3e] via-[#1e1432] to-[#0f0a1a] relative overflow-hidden">
      {/* 动态背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-radial from-purple-800/10 via-transparent to-transparent"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-cyan-400/20 to-purple-500/20 rounded-full blur-xl animate-pulse-slow"></div>
        <div className="absolute top-40 right-20 w-48 h-48 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-xl animate-float"></div>
        <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-r from-pink-500/20 to-purple-600/20 rounded-full blur-xl animate-float-reverse"></div>
        <div className="absolute bottom-40 right-10 w-24 h-24 bg-gradient-to-r from-cyan-400/20 to-blue-500/20 rounded-full blur-xl animate-pulse-slow"></div>
      </div>

      {/* 面包屑导航 */}
      <div className="relative z-10 bg-[#2a1f3e]/80 backdrop-blur-lg border-b border-purple-800/30 px-6 py-4">
        <nav className="flex items-center space-x-2 text-sm">
          <Link href="/dashboard" className="text-gray-300 hover:text-white transition-colors">
            仪表盘
          </Link>
          <span className="text-gray-300">&gt;</span>
          <span className="text-white">我的店铺</span>
        </nav>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">我的店铺</h1>
          <p className="text-gray-300">管理您的店铺信息和设置</p>
        </div>

        {shopInfo && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 店铺基本信息 */}
            <div className="lg:col-span-2 bg-[#2a1f3e]/80 backdrop-blur-lg rounded-2xl shadow-xl p-8 border border-purple-800/30">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white">店铺信息</h2>
                <span className={`px-4 py-2 rounded-full text-sm font-semibold ${
                  shopInfo.status === 'APPROVED' 
                    ? 'bg-green-100 text-green-800 border border-green-200' 
                    : shopInfo.status === 'PENDING'
                    ? 'bg-yellow-100 text-yellow-800 border border-yellow-200'
                    : 'bg-red-100 text-red-800 border border-red-200'
                } opacity-100`}>
                  {shopInfo.statusDescription}
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-cyan-400 uppercase tracking-wide">店铺名称</label>
                  <p className="text-lg font-medium text-white">{shopInfo.shopName}</p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-cyan-400 uppercase tracking-wide">店铺风格</label>
                  <p className="text-lg font-medium text-white">{shopInfo.shopStyle}</p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-cyan-400 uppercase tracking-wide">入驻设计师</label>
                  <p className="text-lg font-medium text-white">{shopInfo.hasDesigner ? '是' : '否'}</p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-cyan-400 uppercase tracking-wide">申请时间</label>
                  <p className="text-lg font-medium text-white">{new Date(shopInfo.applyTime).toLocaleDateString()}</p>
                </div>
                {shopInfo.contactName && (
                  <div className="space-y-2">
                    <label className="text-sm font-semibold text-cyan-400 uppercase tracking-wide">联系人</label>
                    <p className="text-lg font-medium text-white">{shopInfo.contactName}</p>
                  </div>
                )}
                {shopInfo.contactPhone && (
                  <div className="space-y-2">
                    <label className="text-sm font-semibold text-cyan-400 uppercase tracking-wide">联系电话</label>
                    <p className="text-lg font-medium text-white">{shopInfo.contactPhone}</p>
                  </div>
                )}
              </div>

              {shopInfo.shopDescription && (
                <div className="mb-8">
                  <label className="block text-sm font-semibold text-cyan-400 uppercase tracking-wide mb-2">店铺简介</label>
                  <p className="text-gray-300 leading-relaxed">{shopInfo.shopDescription}</p>
                </div>
              )}

              {shopInfo.reviewComment && (
                <div className="bg-gradient-to-r from-blue-900/30 to-indigo-900/30 border border-blue-500/50 rounded-xl p-6 mb-8 backdrop-blur-sm">
                  <label className="block text-sm font-semibold text-blue-300 mb-2">审核意见</label>
                  <p className="text-blue-200 leading-relaxed">{shopInfo.reviewComment}</p>
                </div>
              )}

              {/* 设计稿文件展示 */}
              {((shopInfo.designFiles && shopInfo.designFiles.length > 0) || (shopInfo.worksPath && shopInfo.worksPath.length > 0)) && (
                <div className="mb-8">
                  <label className="block text-sm font-semibold text-cyan-400 uppercase tracking-wide mb-4">设计稿文件</label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {/* 优先使用designFiles，如果不存在则使用worksPath（兼容旧版本） */}
                    {(shopInfo.designFiles && shopInfo.designFiles.length > 0 ? shopInfo.designFiles : shopInfo.worksPath || []).map((work, index) => (
                      <div key={`design-${index}`} className="group relative bg-gradient-to-br from-purple-800/40 to-indigo-800/40 rounded-xl p-4 hover:shadow-xl transition-all duration-200 border border-purple-500/30">
                        <div className="aspect-square w-full bg-black/20 rounded-lg flex items-center justify-center mb-3 shadow-sm overflow-hidden">
                          <ImageWithFallback 
                            src={`http://localhost:8080/api/materials/image/file/${work.split('/').pop()}`}
                            alt={`设计稿 ${index + 1}`}
                            className="w-full h-full object-cover rounded-lg"
                            onClick={() => {
                              window.open(`http://localhost:8080/api/materials/image/file/${work.split('/').pop()}`, '_blank');
                            }}
                          />
                        </div>
                                                    <p className="text-xs text-gray-300 text-center truncate font-medium">{work.split('/').pop()}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* 生产证明文件展示 */}
              {shopInfo.productionFiles && shopInfo.productionFiles.length > 0 && (
                <div>
                  <label className="block text-sm font-semibold text-cyan-400 uppercase tracking-wide mb-4">生产证明材料</label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {shopInfo.productionFiles.map((file, index) => (
                      <div key={`production-${index}`} className="group relative bg-gradient-to-br from-purple-800/40 to-indigo-800/40 rounded-xl p-4 hover:shadow-xl transition-all duration-200 border border-purple-500/30">
                        <div className="aspect-square w-full bg-black/20 rounded-lg flex items-center justify-center mb-3 shadow-sm overflow-hidden">
                          {file.toLowerCase().includes('.pdf') ? (
                            <div className="w-16 h-16 bg-red-900/50 rounded-full flex items-center justify-center">
                              <svg className="w-8 h-8 text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            </div>
                          ) : (
                            <ImageWithFallback
                              src={`http://localhost:8080/api/materials/image/file/${file.split('/').pop()}`}
                              alt={`生产证明 ${index + 1}`}
                              className="w-full h-full object-cover rounded-lg"
                              onClick={() => {
                                window.open(`http://localhost:8080/api/materials/image/file/${file.split('/').pop()}`, '_blank');
                              }}
                            />
                          )}
                        </div>
                                                        <p className="text-xs text-gray-300 text-center truncate font-medium">{file.split('/').pop()}</p>
                        {file.toLowerCase().includes('.pdf') && (
                          <p className="text-xs text-gray-300 text-center">PDF文档</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* 侧边栏 */}
            <div className="space-y-6">
              {/* 快速操作 */}
              <div className="bg-[#2a1f3e]/80 backdrop-blur-lg rounded-2xl shadow-xl p-6 border border-purple-800/30">
                <h3 className="text-lg font-bold text-white mb-4">快速操作</h3>
                <div className="space-y-3 quick-actions-container">
                  {shopInfo.status === 'APPROVED' && (
                    <>
                      <Link href="/shop/products" className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-105 block text-center opacity-100">
                        <span className="opacity-100">📦 管理商品</span>
                      </Link>
                      <Link href="/shop/orders" className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-105 block text-center opacity-100">
                        <span className="opacity-100">📋 查看订单</span>
                      </Link>
                      <Link href="/shop/settings" className="w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-105 block text-center opacity-100">
                        <span className="opacity-100">⚙️ 店铺设置</span>
                      </Link>
                    </>
                  )}
                  
                  {shopInfo.status === 'PENDING' && (
                    <button 
                      onClick={() => setShowApplyForm(true)}
                      className="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-105 opacity-100"
                    >
                      <span className="opacity-100">🗑️ 取消申请</span>
                    </button>
                  )}
                  
                  {shopInfo.status === 'REJECTED' && (
                    <button 
                      type="button"
                      onClick={() => {
                        console.log("重新申请按钮被点击");
                        // 预填充之前的申请信息
                        setApplyForm({
                          shopName: shopInfo.shopName || '',
                          shopDescription: shopInfo.shopDescription || '',
                          contactName: shopInfo.contactName || '',
                          contactPhone: shopInfo.contactPhone || '',
                          contactEmail: shopInfo.contactEmail || '',
                          businessAddress: shopInfo.businessAddress || '',
                          shopStyle: shopInfo.shopStyle || '',
                          hasDesigner: shopInfo.hasDesigner || false,
                          designFiles: [],
                          productionFiles: [],
                          designSpecConfirmed: false,
                          productionCapacityConfirmed: false
                        });
                        setCurrentStep(1);
                        setShowApplyForm(true);
                      }}
                      className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-105 opacity-100"
                    >
                      <span className="opacity-100">🔄 重新申请</span>
                    </button>
                  )}
                  
                  <Link
                    href="/dashboard"
                    className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-xl transition-all duration-200 block text-center opacity-100 dashboard-return-button"
                    onClick={(e) => {
                      e.preventDefault();
                      window.location.href = '/dashboard';  // 使用直接跳转而不是Next.js路由
                    }}
                  >
                    <span className="opacity-100">🏠 返回仪表盘</span>
                  </Link>
                </div>
              </div>

              {/* 店铺统计 */}
              {shopInfo.status === 'APPROVED' && (
                <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">店铺统计</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl">
                      <span className="text-gray-700 font-medium">商品数量</span>
                      <span className="text-2xl font-bold text-blue-600">0</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-xl">
                      <span className="text-gray-700 font-medium">总订单</span>
                      <span className="text-2xl font-bold text-green-600">0</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl">
                      <span className="text-gray-700 font-medium">本月销售</span>
                      <span className="text-2xl font-bold text-purple-600">¥0</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 申请开通店铺表单 - 在有店铺的情况下也需要显示 */}
      {showApplyForm && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl p-8 w-full max-w-4xl max-h-[90vh] overflow-y-auto shadow-2xl">
            <div className="flex items-center justify-between mb-6">
                                <h3 className="text-2xl font-bold text-gray-900">重新申请店铺</h3>
                  <button
                    onClick={() => setShowApplyForm(false)}
                    className="text-gray-700 hover:text-gray-900 transition-colors"
                  >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 步骤指示器 */}
            <div className="mb-8">
              <div className="flex items-center justify-between">
                {[
                  { step: 1, title: '设计稿规范', icon: '📋' },
                  { step: 2, title: '生产能力', icon: '🏭' },
                  { step: 3, title: '店铺信息', icon: '🏪' },
                  { step: 4, title: '作品上传', icon: '📁' }
                ].map((item, index) => (
                  <div key={item.step} className="flex items-center">
                    <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all ${
                      currentStep >= item.step 
                        ? 'bg-blue-600 border-blue-600 text-white' 
                        : 'bg-white border-gray-300 text-gray-700'
                    }`}>
                      <span className="text-sm">{item.icon}</span>
                    </div>
                    <div className="ml-3 text-sm">
                      <p className={`font-medium ${currentStep >= item.step ? 'text-blue-600' : 'text-gray-700'}`}>
                        步骤 {item.step}
                      </p>
                      <p className={`${currentStep >= item.step ? 'text-gray-900' : 'text-gray-700'}`}>
                        {item.title}
                      </p>
                    </div>
                    {index < 3 && (
                      <div className={`flex-1 h-0.5 mx-4 ${
                        currentStep > item.step ? 'bg-blue-600' : 'bg-gray-300'
                      }`}></div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* 步骤内容 */}
            <div className="min-h-[400px]">
              {/* 步骤1: 设计稿规范 */}
              {currentStep === 1 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <h4 className="text-xl font-bold text-gray-900 mb-2">设计稿规范要求</h4>
                    <p className="text-gray-700">请仔细阅读以下规范，确保您的设计稿符合商城要求</p>
                  </div>

                  <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 border border-blue-200">
                    <h5 className="text-lg font-semibold text-blue-900 mb-4">📋 设计稿技术规范</h5>
                    <ul className="space-y-3 text-blue-800">
                      <li className="flex items-start">
                        <span className="text-blue-600 mr-2">•</span>
                        <span><strong>分辨率要求：</strong>不低于300DPI，确保打印清晰度</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-600 mr-2">•</span>
                        <span><strong>文件格式：</strong>支持AI、PSD、PNG、JPG格式，建议提供矢量文件</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-600 mr-2">•</span>
                        <span><strong>颜色模式：</strong>RGB模式用于屏幕显示，CMYK模式用于印刷</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-600 mr-2">•</span>
                        <span><strong>尺寸标准：</strong>符合常见键帽规格（如Cherry MX标准）</span>
                      </li>
                    </ul>
                  </div>

                  <div className="bg-gradient-to-br from-yellow-50 to-orange-100 rounded-xl p-6 border border-yellow-200">
                    <h5 className="text-lg font-semibold text-orange-900 mb-4">🎨 设计质量要求</h5>
                    <ul className="space-y-3 text-orange-800">
                      <li className="flex items-start">
                        <span className="text-orange-600 mr-2">•</span>
                        <span><strong>原创性：</strong>必须为原创设计，不得侵犯他人知识产权</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-orange-600 mr-2">•</span>
                        <span><strong>实用性：</strong>设计应考虑实际使用场景和用户体验</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-orange-600 mr-2">•</span>
                        <span><strong>美观性：</strong>具有良好的视觉效果和艺术价值</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-orange-600 mr-2">•</span>
                        <span><strong>主题一致：</strong>整套设计保持统一的风格和主题</span>
                      </li>
                    </ul>
                  </div>

                  <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                    <h5 className="text-red-800 font-semibold mb-2">⚠️ 禁止内容</h5>
                    <p className="text-red-700 text-sm">
                      不得包含违法违规、暴力血腥、色情低俗、政治敏感等内容，
                      不得使用未经授权的版权素材（如影视作品、游戏角色等）
                    </p>
                  </div>

                  <div className="flex items-center p-4 bg-gray-50 rounded-xl">
                    <input
                      type="checkbox"
                      id="designSpecConfirmed"
                      checked={applyForm.designSpecConfirmed}
                      onChange={(e) => setApplyForm(prev => ({ ...prev, designSpecConfirmed: e.target.checked }))}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <label htmlFor="designSpecConfirmed" className="ml-3 text-sm text-gray-700">
                      我已仔细阅读并同意遵守以上设计稿规范要求 *
                    </label>
                  </div>
                </div>
              )}

              {/* 步骤2: 生产能力验证 */}
              {currentStep === 2 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <h4 className="text-xl font-bold text-gray-900 mb-2">生产能力验证</h4>
                    <p className="text-gray-700">请确认您具备实际的商品生产和供应能力</p>
                  </div>

                  <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-xl p-6 border border-green-200">
                    <h5 className="text-lg font-semibold text-green-900 mb-4">🏭 生产能力要求</h5>
                    <ul className="space-y-3 text-green-800">
                      <li className="flex items-start">
                        <span className="text-green-600 mr-2">•</span>
                        <span><strong>生产资质：</strong>具备合法的生产经营资质或与生产商的合作协议</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-green-600 mr-2">•</span>
                        <span><strong>质量保证：</strong>确保产品质量符合行业标准，提供质检报告</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-green-600 mr-2">•</span>
                        <span><strong>供货能力：</strong>具备稳定的供货能力，能够按时交付订单</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-green-600 mr-2">•</span>
                        <span><strong>售后服务：</strong>提供完善的售后服务和质量保障</span>
                      </li>
                    </ul>
                  </div>

                  <div className="bg-blue-50 p-6 rounded-xl border border-blue-100">
                    <div className="flex items-center mb-4">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <span className="text-xl">📝</span>
                      </div>
                      <h5 className="text-lg font-semibold" style={{color: '#1e3a8a'}}>物流要求</h5>
                    </div>
                    <ul className="space-y-3" style={{color: '#1e40af'}}>
                      <li className="flex items-start">
                        <span className="mr-2" style={{color: '#2563eb'}}>•</span>
                        <span style={{color: '#1e40af'}}><strong>国内物流：</strong>需要能够处理国内物流配送</span>
                      </li>
                      <li className="flex items-start">
                        <span className="mr-2" style={{color: '#2563eb'}}>•</span>
                        <span style={{color: '#1e40af'}}><strong>发货时间：</strong>提供合理的发货时间和物流跟踪</span>
                      </li>
                      <li className="flex items-start">
                        <span className="mr-2" style={{color: '#2563eb'}}>•</span>
                        <span style={{color: '#1e40af'}}><strong>售后问题：</strong>能够处理退换货等售后问题</span>
                      </li>
                    </ul>
                  </div>

                  <div className="flex items-center p-4 bg-gray-50 rounded-xl">
                    <input
                      type="checkbox"
                      id="productionCapacityConfirmed"
                      checked={applyForm.productionCapacityConfirmed}
                      onChange={(e) => setApplyForm(prev => ({ ...prev, productionCapacityConfirmed: e.target.checked }))}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <label htmlFor="productionCapacityConfirmed" className="ml-3 text-sm" style={{color: '#374151'}}>
                      我确认具备实际的商品生产和供应能力，能够保质保量完成订单 *
                    </label>
                  </div>
                </div>
              )}

              {/* 步骤3: 店铺信息 */}
              {currentStep === 3 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <h4 className="text-xl font-bold mb-2" style={{color: '#111827'}}>填写店铺信息</h4>
                    <p style={{color: '#374151'}}>请详细填写您的店铺信息，包括名称、简介、联系方式等</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="shopName" className="block text-sm font-medium mb-2" style={{color: '#374151'}}>
                        店铺名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="shopName"
                        value={applyForm.shopName}
                        onChange={(e) => setApplyForm({...applyForm, shopName: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="输入店铺名称（20字以内）"
                        maxLength={20}
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="shopStyle" className="block text-sm font-medium mb-2" style={{color: '#374151'}}>
                        店铺风格 <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="shopStyle"
                        value={applyForm.shopStyle}
                        onChange={(e) => setApplyForm({...applyForm, shopStyle: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                        required
                      >
                        <option value="" disabled>请选择店铺风格</option>
                        <option value="现代简约">🎯 现代简约</option>
                        <option value="复古经典">👑 复古经典</option>
                        <option value="游戏主题">🎮 游戏主题</option>
                        <option value="艺术创意">🎨 艺术创意</option>
                        <option value="商务办公">💼 商务办公</option>
                        <option value="科技未来">🚀 科技未来</option>
                        <option value="自然风光">🌳 自然风光</option>
                        <option value="其他风格">🌈 其他风格</option>
                      </select>
                    </div>
                    <div className="md:col-span-2">
                      <label htmlFor="shopDescription" className="block text-sm font-medium mb-2" style={{color: '#374151'}}>
                        店铺简介 <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        id="shopDescription"
                        value={applyForm.shopDescription}
                        onChange={(e) => setApplyForm({...applyForm, shopDescription: e.target.value})}
                        rows={4}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-gray-900"
                        placeholder="请简要介绍您的店铺特色、经营理念、产品优势等（建议50-200字）"
                        maxLength={500}
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="contactName" className="block text-sm font-medium mb-2" style={{color: '#374151'}}>
                        联系人姓名 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="contactName"
                        value={applyForm.contactName}
                        onChange={(e) => setApplyForm({...applyForm, contactName: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="输入联系人姓名"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="contactPhone" className="block text-sm font-medium mb-2" style={{color: '#374151'}}>
                        联系电话 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="tel"
                        id="contactPhone"
                        value={applyForm.contactPhone}
                        onChange={(e) => setApplyForm({...applyForm, contactPhone: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="输入联系电话"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700 mb-2">
                        联系邮箱 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="email"
                        id="contactEmail"
                        value={applyForm.contactEmail}
                        onChange={(e) => setApplyForm({...applyForm, contactEmail: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="输入联系邮箱"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="businessAddress" className="block text-sm font-medium text-gray-700 mb-2">
                        经营地址 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="businessAddress"
                        value={applyForm.businessAddress}
                        onChange={(e) => setApplyForm({...applyForm, businessAddress: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="输入经营地址"
                        required
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={applyForm.hasDesigner}
                          onChange={(e) => setApplyForm({...applyForm, hasDesigner: e.target.checked})}
                          className="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <span className="ml-3 text-gray-700">店铺拥有专职设计师</span>
                      </label>
                    </div>
                  </div>
                </div>
              )}

              {/* 步骤4: 作品上传 */}
              {currentStep === 4 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <h4 className="text-xl font-bold text-gray-900 mb-2">作品上传</h4>
                    <p className="text-gray-700">请上传您的设计稿和生产证明材料</p>
                  </div>

                  {/* 设计稿上传 */}
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 border border-blue-200">
                    <h5 className="text-lg font-semibold text-blue-900 mb-4">设计稿文件</h5>
                    <p className="text-blue-800 mb-4">上传2-5个您的设计作品，支持JPG、PNG格式，单个文件不超过5MB</p>
                    
                    <div className="mb-4">
                      <label htmlFor="designFiles" className="block w-full px-4 py-3 bg-white border border-blue-300 rounded-lg text-center cursor-pointer hover:bg-blue-50 transition-colors">
                        <span className="text-blue-600 font-medium">选择设计稿文件</span>
                        <input
                          type="file"
                          id="designFiles"
                          accept="image/jpeg,image/png"
                          multiple
                          onChange={handleDesignFileChange}
                          className="hidden"
                        />
                      </label>
                    </div>

                    {/* 已选择的设计稿文件 */}
                    {applyForm.designFiles.length > 0 && (
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {Array.from(applyForm.designFiles).map((file, index) => (
                          <div key={index} className="relative bg-white rounded-lg p-2 border border-blue-200">
                            <div className="aspect-square w-full bg-gray-100 rounded flex items-center justify-center mb-2 overflow-hidden">
                              <img
                                src={URL.createObjectURL(file)}
                                alt={`设计稿 ${index + 1}`}
                                className="max-w-full max-h-full object-contain"
                              />
                            </div>
                            <p className="text-xs text-gray-700 truncate">{file.name}</p>
                            <button
                              type="button"
                              onClick={() => removeDesignFile(index)}
                              className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full text-white flex items-center justify-center"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* 生产证明上传 */}
                  <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-xl p-6 border border-green-200">
                    <h5 className="text-lg font-semibold text-green-900 mb-4">生产证明材料</h5>
                    <p className="text-green-800 mb-4">上传1-3个生产证明材料，如工厂合作协议、样品照片等，支持JPG、PNG、PDF格式</p>
                    
                    <div className="mb-4">
                      <label htmlFor="productionFiles" className="block w-full px-4 py-3 bg-white border border-green-300 rounded-lg text-center cursor-pointer hover:bg-green-50 transition-colors">
                        <span className="text-green-600 font-medium">选择生产证明文件</span>
                        <input
                          type="file"
                          id="productionFiles"
                          accept="image/jpeg,image/png,application/pdf"
                          multiple
                          onChange={handleProductionFileChange}
                          className="hidden"
                        />
                      </label>
                    </div>

                    {/* 已选择的生产证明文件 */}
                    {applyForm.productionFiles.length > 0 && (
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {Array.from(applyForm.productionFiles).map((file, index) => (
                          <div key={index} className="relative bg-white rounded-lg p-2 border border-green-200">
                            <div className="aspect-square w-full bg-gray-100 rounded flex items-center justify-center mb-2 overflow-hidden">
                              {file.type === 'application/pdf' ? (
                                <svg className="w-12 h-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              ) : (
                                <img
                                  src={URL.createObjectURL(file)}
                                  alt={`生产证明 ${index + 1}`}
                                  className="max-w-full max-h-full object-contain"
                                />
                                                              )}
                            </div>
                            <p className="text-xs text-gray-700 truncate">{file.name}</p>
                            <button
                              type="button"
                              onClick={() => removeProductionFile(index)}
                              className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full text-white flex items-center justify-center"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* 步骤导航按钮 */}
            <div className="flex justify-between mt-8">
              <button
                type="button"
                onClick={prevStep}
                disabled={currentStep === 1}
                className="px-6 py-3 bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed text-gray-700 font-semibold rounded-xl transition-all duration-200"
              >
                上一步
              </button>
              
              {currentStep < 4 ? (
                <button
                  type="button"
                  onClick={nextStep}
                  className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl transition-all duration-200 transform hover:scale-105"
                >
                  下一步
                </button>
              ) : (
                <button
                  type="button"
                  onClick={handleApplyShop}
                  disabled={uploading || !validateStep(1) || !validateStep(2) || !validateStep(3) || !validateStep(4)}
                  className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold rounded-xl transition-all duration-200 transform hover:scale-105"
                >
                  {uploading ? (
                    <div className="flex items-center justify-center">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      提交中...
                    </div>
                  ) : (
                    '提交审核 🚀'
                  )}
                </button>
              )}
              
              <button
                type="button"
                onClick={() => setShowApplyForm(false)}
                disabled={uploading}
                className="px-6 py-3 bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed text-gray-700 font-semibold rounded-xl transition-all duration-200"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}