'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import Link from 'next/link';

export default function LoginPage() {
  const [formData, setFormData] = useState({
    account: '',
    password: ''
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { login } = useAuth();
  const router = useRouter();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // 清除对应字段的错误
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.account.trim()) {
      newErrors.account = '请输入手机号或邮箱';
    }

    if (!formData.password.trim()) {
      newErrors.password = '请输入密码';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // 检查是否为管理员账号
      if (formData.account.trim() === 'admin') {
        console.log('检测到管理员账号，使用管理员API登录');
        
        // 直接调用管理员API
        const response = await fetch('http://localhost:8080/api/admin/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username: formData.account.trim(),
            password: formData.password
          })
        });

        if (response.ok) {
          const data = await response.json();
          
          if (data.code === 200) {
            console.log('管理员登录成功:', data.data);
            const adminData = data.data;
            
            // 保存管理员信息到localStorage
            localStorage.setItem('admin', JSON.stringify({
              id: 'admin_' + Date.now(),
              username: adminData.username,
              role: adminData.role || 'super_admin',
              permissions: adminData.permissions || []
            }));
            localStorage.setItem('admin_token', adminData.token);
            localStorage.setItem('user_type', 'admin');
            
            // 跳转到管理员后台
            router.push('/admin/dashboard');
            return;
          } else {
            setErrors({ general: data.message || '管理员登录失败' });
          }
        } else {
          const errorData = await response.json();
          setErrors({ general: errorData.message || '管理员账号或密码错误' });
        }
        
        setIsSubmitting(false);
        return;
      }

      // 普通用户登录流程
      console.log('普通用户登录流程');
      const result = await login(formData.account, formData.password);
      
      if (result) {
        // 普通用户登录成功，强制跳转到深色主题仪表盘
        console.log('登录成功，强制跳转到深色主题仪表盘');
        setTimeout(() => {
          window.location.href = '/dashboard';  // 使用直接跳转而不是Next.js路由
        }, 100);
      } else {
        setErrors({ general: '用户名或密码错误' });
      }
    } catch (error) {
      console.error('登录错误:', error);
      setErrors({ general: '登录失败，请稍后重试' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#1a1b26] relative overflow-hidden">
      {/* 背景渐变效果 */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-[#1a1b26] via-[#1e1b3a] to-[#1a1b26]"></div>
        <div className="absolute top-1/4 left-1/4 w-[600px] h-[600px] bg-cyan-400 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-blob"></div>
        <div className="absolute top-1/3 right-1/4 w-[500px] h-[500px] bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl opacity-15 animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-[550px] h-[550px] bg-pink-500 rounded-full mix-blend-multiply filter blur-3xl opacity-12 animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative z-10 flex items-center justify-center min-h-screen px-4">
        {/* 返回首页按钮 */}
        <Link 
          href="/" 
          className="absolute top-8 left-8 text-gray-400 hover:text-white transition-colors flex items-center space-x-2"
        >
          <span>←</span>
          <span>返回首页</span>
        </Link>

        <div className="w-full max-w-md">
          <div className="bg-gray-900/50 backdrop-blur-lg rounded-2xl p-8 border border-gray-700">
            {/* Logo和标题 */}
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4">
                灵
              </div>
              <h1 className="text-2xl font-bold text-white mb-2">欢迎回来 (支持管理员)</h1>
              <p className="text-white">登录您的灵狐键创账户</p>
              <p className="text-sm text-yellow-400 mt-2">管理员请使用: admin / admin123</p>
            </div>

            {/* 错误提示 */}
            {errors.general && (
              <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                <p className="text-red-400 text-sm">{errors.general}</p>
              </div>
            )}

            {/* 登录表单 */}
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="account" className="block text-sm font-medium text-white mb-2">
                  邮箱/手机号/用户名
                </label>
                <input
                  type="text"
                  id="account"
                  name="account"
                  value={formData.account}
                  onChange={handleInputChange}
                  placeholder="请输入您的手机号或邮箱"
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                />
                {errors.account && (
                  <p className="text-red-400 text-sm mt-1">{errors.account}</p>
                )}
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-white mb-2">
                  密码
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="请输入您的密码"
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                />
                {errors.password && (
                  <p className="text-red-400 text-sm mt-1">{errors.password}</p>
                )}
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full py-3 bg-gradient-to-r from-cyan-500 to-purple-600 text-white rounded-lg font-medium hover:shadow-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? '登录中...' : '登录'}
              </button>
            </form>

            <div className="mt-6 text-center">
              <div className="text-gray-400 text-sm mb-4">或</div>
              <p className="text-gray-400 text-sm">
                还没有账户？
                <Link href="/register" className="text-purple-400 hover:text-purple-300 ml-1">
                  立即注册
                </Link>
              </p>
              <p className="text-gray-400 text-sm mt-2">
                <Link href="/forgot-password" className="text-purple-400 hover:text-purple-300">
                  忘记密码？
                </Link>
              </p>
            </div>

            <div className="mt-8 pt-6 border-t border-gray-700 text-center">
              <p className="text-gray-500 text-xs">
                登录即表示您同意我们的
                <Link href="/terms" className="text-purple-400 hover:text-purple-300 mx-1">
                  服务条款
                </Link>
                和
                <Link href="/privacy" className="text-purple-400 hover:text-purple-300 mx-1">
                  隐私政策
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes blob {
          0% { transform: translate(0px, 0px) scale(1); }
          33% { transform: translate(30px, -50px) scale(1.1); }
          66% { transform: translate(-20px, 20px) scale(0.9); }
          100% { transform: translate(0px, 0px) scale(1); }
        }
        .animate-blob {
          animation: blob 7s infinite;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
      `}</style>
    </div>
  );
} 