﻿'use client';

import React from 'react';
import Link from 'next/link';

export default function HelpPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <nav className="flex items-center space-x-2 text-sm">
          <Link href="/dashboard" className="text-gray-500 hover:text-gray-700">
            仪表盘
          </Link>
          <span className="text-gray-400">&gt;</span>
          <span className="text-gray-900">帮助中心</span>
        </nav>
      </div>
      <div className="max-w-4xl mx-auto py-8 px-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">帮助中心</h1>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-bold text-blue-900">如何开始设计？</h3>
              <p className="text-blue-700">点击"设计器"进入键帽设计界面，选择模板开始创作。</p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <h3 className="font-bold text-green-900">如何保存设计？</h3>
              <p className="text-green-700">在设计界面点击保存按钮，设计将自动保存到您的作品库。</p>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <h3 className="font-bold text-purple-900">如何分享作品？</h3>
              <p className="text-purple-700">在作品库中点击分享按钮，可以分享到社区或生成链接。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
