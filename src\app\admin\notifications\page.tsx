'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdmin } from '../../../contexts/AdminContext';
import Link from 'next/link';

// API配置
const API_BASE_URL = 'http://localhost:8080/api';

interface AdminNotification {
  id: string;
  type: string;
  title: string;
  message: string;
  read: boolean;
  createdAt: string;
  relatedId?: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
}

export default function AdminNotifications() {
  const router = useRouter();
  const { admin, isLoading: adminLoading } = useAdmin();
  const [loading, setLoading] = useState(true);
  const [notifications, setNotifications] = useState<AdminNotification[]>([]);
  const [activeFilter, setActiveFilter] = useState<'all' | 'unread' | 'read' | 'urgent'>('all');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);

  // 获取管理员通知列表
  const fetchNotifications = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/admin/notifications`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // 转换后端数据格式到前端格式
          const formattedNotifications = (result.data || []).map((notification: any) => ({
            id: notification.notification_id,
            type: notification.type,
            title: notification.title,
            message: notification.message,
            read: notification.is_read,
            createdAt: notification.created_at,
            relatedId: notification.related_id,
            priority: notification.priority || 'MEDIUM'
          }));
          setNotifications(formattedNotifications);
        } else {
          throw new Error(result.message || '获取通知失败');
        }
      } else {
        const errorResult = await response.json();
        throw new Error(errorResult.message || `获取通知失败，状态码：${response.status}`);
      }
    } catch (error) {
      console.error('获取通知失败:', error);
      // 设置空数组，避免页面崩溃
      setNotifications([]);
    } finally {
      setLoading(false);
    }
  };

  // 标记消息为已读
  const markAsRead = async (notificationIds: string[]) => {
    try {
      const token = localStorage.getItem('admin_token');
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/admin/notifications/mark-read`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ notificationIds })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // 更新本地状态
          setNotifications(prev => prev.map(notification => 
            notificationIds.includes(notification.id) 
              ? { ...notification, read: true }
              : notification
          ));
        } else {
          throw new Error(result.message || '标记已读失败');
        }
      } else {
        const errorResult = await response.json();
        throw new Error(errorResult.message || `标记已读失败，状态码：${response.status}`);
      }
    } catch (error) {
      console.error('标记已读失败:', error);
      // 显示错误但不更新本地状态，保持数据一致性
    }
  };

  // 删除消息
  const deleteNotifications = async (notificationIds: string[]) => {
    try {
      const token = localStorage.getItem('admin_token');
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/admin/notifications`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ notificationIds })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // 更新本地状态
          setNotifications(prev => prev.filter(notification => 
            !notificationIds.includes(notification.id)
          ));
          setSelectedNotifications([]);
        } else {
          throw new Error(result.message || '删除消息失败');
        }
      } else {
        const errorResult = await response.json();
        throw new Error(errorResult.message || `删除消息失败，状态码：${response.status}`);
      }
    } catch (error) {
      console.error('删除消息失败:', error);
      alert(`删除消息失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    switch (activeFilter) {
      case 'unread':
        return !notification.read;
      case 'read':
        return notification.read;
      case 'urgent':
        return notification.priority === 'URGENT' || notification.priority === 'HIGH';
      default:
        return true;
    }
  });

  const unreadCount = notifications.filter(n => !n.read).length;
  const urgentCount = notifications.filter(n => (n.priority === 'URGENT' || n.priority === 'HIGH') && !n.read).length;

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'SHOP_APPLY':
        return '🏪';
      case 'USER_REGISTER':
        return '👤';
      case 'ORDER':
        return '🛒';
      case 'SYSTEM':
        return '⚙️';
      case 'SECURITY':
        return '🔒';
      default:
        return '📋';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'from-red-500 to-red-600';
      case 'HIGH':
        return 'from-orange-500 to-red-500';
      case 'MEDIUM':
        return 'from-yellow-500 to-orange-500';
      case 'LOW':
        return 'from-blue-500 to-purple-500';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return { text: '紧急', color: 'bg-red-100 text-red-800 border-red-200' };
      case 'HIGH':
        return { text: '重要', color: 'bg-orange-100 text-orange-800 border-orange-200' };
      case 'MEDIUM':
        return { text: '普通', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' };
      case 'LOW':
        return { text: '低优先级', color: 'bg-blue-100 text-blue-800 border-blue-200' };
      default:
        return { text: '未知', color: 'bg-gray-100 text-gray-800 border-gray-200' };
    }
  };

  const handleNotificationClick = (notification: AdminNotification) => {
    // 标记为已读
    if (!notification.read) {
      markAsRead([notification.id]);
    }

    // 根据类型跳转到相关页面
    switch (notification.type) {
      case 'SHOP_APPLY':
        router.push('/admin/stores');
        break;
      case 'ORDER':
        router.push('/admin/orders');
        break;
      case 'USER_REGISTER':
        router.push('/admin/users');
        break;
      default:
        break;
    }
  };

  const handleSelectAll = () => {
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(filteredNotifications.map(n => n.id));
    }
  };

  const handleBatchRead = () => {
    if (selectedNotifications.length > 0) {
      markAsRead(selectedNotifications);
      setSelectedNotifications([]);
    }
  };

  const handleBatchDelete = () => {
    if (selectedNotifications.length > 0 && confirm('确定要删除选中的消息吗？')) {
      deleteNotifications(selectedNotifications);
    }
  };

  useEffect(() => {
    if (!adminLoading) {
      if (admin) {
        fetchNotifications();
      } else {
        router.push('/admin/login');
      }
    }
  }, [admin, adminLoading, router]);

  if (adminLoading) {
    return <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
    </div>;
  }

  if (!admin) {
    return null; // 会被useEffect重定向到登录页
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto"></div>
          <p className="mt-4 text-gray-300">加载消息中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* 顶部导航 */}
      <nav className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-600 rounded-lg flex items-center justify-center text-white font-bold text-sm mr-3">
                管
              </Link>
              <h1 className="text-xl font-bold text-white">消息中心</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-300">管理员：{admin.username}</span>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* 侧边栏 */}
        <aside className="w-64 bg-gray-800 min-h-screen pt-4">
          <nav className="px-4 space-y-2">
            <Link href="/admin/dashboard" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📊</span>
              仪表盘
            </Link>
            <Link href="/admin/users" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">👥</span>
              用户管理
            </Link>

            <Link href="/admin/products" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📦</span>
              商品管理
            </Link>
            <Link href="/admin/orders" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🛒</span>
              订单管理
            </Link>
            <Link href="/admin/customer-service" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">💬</span>
              客服中心
            </Link>
            <Link href="/admin/stores" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🏪</span>
              店铺管理
            </Link>
            <Link href="/admin/logistics" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🚚</span>
              物流管理
            </Link>
            <Link href="/admin/notifications" className="flex items-center px-4 py-2 text-white bg-red-600 rounded-lg">
              <span className="mr-3">🔔</span>
              消息中心
              {unreadCount > 0 && (
                <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1">
                  {unreadCount}
                </span>
              )}
            </Link>
            <Link href="/admin/settings" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">⚙️</span>
              系统设置
            </Link>
          </nav>
        </aside>

        {/* 主内容区 */}
        <main className="flex-1 p-6">
          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm font-medium">全部消息</p>
                  <p className="text-3xl font-bold">{notifications.length}</p>
                </div>
                <div className="text-4xl opacity-80">📬</div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-red-500 to-pink-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-100 text-sm font-medium">未读消息</p>
                  <p className="text-3xl font-bold">{unreadCount}</p>
                </div>
                <div className="text-4xl opacity-80">🔔</div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm font-medium">紧急消息</p>
                  <p className="text-3xl font-bold">{urgentCount}</p>
                </div>
                <div className="text-4xl opacity-80">🚨</div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm font-medium">今日消息</p>
                  <p className="text-3xl font-bold">
                    {notifications.filter(n => 
                      new Date(n.createdAt).toDateString() === new Date().toDateString()
                    ).length}
                  </p>
                </div>
                <div className="text-4xl opacity-80">📅</div>
              </div>
            </div>
          </div>

          {/* 筛选和操作栏 */}
          <div className="bg-gray-800 rounded-2xl p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-white">消息管理</h2>
              <button
                onClick={() => fetchNotifications()}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                🔄 刷新
              </button>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex space-x-2">
                {[
                  { key: 'all', label: '全部', count: notifications.length },
                  { key: 'unread', label: '未读', count: unreadCount },
                  { key: 'urgent', label: '紧急', count: urgentCount },
                  { key: 'read', label: '已读', count: notifications.length - unreadCount }
                ].map(filter => (
                  <button
                    key={filter.key}
                    onClick={() => setActiveFilter(filter.key as any)}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                      activeFilter === filter.key
                        ? 'bg-red-600 text-white'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    {filter.label} ({filter.count})
                  </button>
                ))}
              </div>

              {selectedNotifications.length > 0 && (
                <div className="flex space-x-2">
                  <button
                    onClick={handleBatchRead}
                    className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors text-sm"
                  >
                    标记已读
                  </button>
                  <button
                    onClick={handleBatchDelete}
                    className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors text-sm"
                  >
                    删除选中
                  </button>
                </div>
              )}
            </div>

            {filteredNotifications.length > 0 && (
              <div className="flex items-center space-x-4 mt-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedNotifications.length === filteredNotifications.length && filteredNotifications.length > 0}
                    onChange={handleSelectAll}
                    className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500"
                  />
                  <span className="ml-2 text-sm text-gray-300">全选</span>
                </label>
                <span className="text-sm text-gray-400">
                  已选择 {selectedNotifications.length} 条消息
                </span>
              </div>
            )}
          </div>

          {/* 消息列表 */}
          <div className="bg-gray-800 rounded-2xl overflow-hidden">
            {filteredNotifications.length === 0 ? (
              <div className="text-center py-16">
                <div className="text-6xl mb-4">📪</div>
                <h3 className="text-xl font-bold text-white mb-2">暂无消息</h3>
                <p className="text-gray-400">
                  {activeFilter === 'unread' ? '您已读完所有消息' : 
                   activeFilter === 'urgent' ? '暂无紧急消息' :
                   activeFilter === 'read' ? '暂无已读消息' : '暂无消息'}
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-700">
                {filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-6 hover:bg-gray-700/50 transition-colors cursor-pointer ${
                      !notification.read ? 'bg-gray-700/30' : ''
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex items-start space-x-4">
                      <input
                        type="checkbox"
                        checked={selectedNotifications.includes(notification.id)}
                        onChange={(e) => {
                          e.stopPropagation();
                          if (e.target.checked) {
                            setSelectedNotifications([...selectedNotifications, notification.id]);
                          } else {
                            setSelectedNotifications(selectedNotifications.filter(id => id !== notification.id));
                          }
                        }}
                        className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500 mt-1"
                      />
                      
                      <div className={`w-12 h-12 bg-gradient-to-br ${getPriorityColor(notification.priority)} rounded-xl flex items-center justify-center text-white text-xl shadow-lg`}>
                        {getNotificationIcon(notification.type)}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-3">
                            <h3 className={`text-lg font-semibold ${!notification.read ? 'text-white' : 'text-gray-300'}`}>
                              {notification.title}
                            </h3>
                            <span className={`inline-flex px-2 py-1 rounded-full text-xs font-semibold border ${getPriorityBadge(notification.priority).color}`}>
                              {getPriorityBadge(notification.priority).text}
                            </span>
                            {!notification.read && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                新
                              </span>
                            )}
                          </div>
                          <span className="text-sm text-gray-400">
                            {new Date(notification.createdAt).toLocaleString()}
                          </span>
                        </div>
                        
                        <p className="text-gray-300 leading-relaxed mb-3">
                          {notification.message}
                        </p>

                        <div className="flex items-center space-x-3">
                          {!notification.read && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                markAsRead([notification.id]);
                              }}
                              className="text-blue-400 hover:text-blue-300 text-sm font-medium"
                            >
                              标记已读
                            </button>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteNotifications([notification.id]);
                            }}
                            className="text-red-400 hover:text-red-300 text-sm font-medium"
                          >
                            删除
                          </button>
                          {notification.relatedId && (
                            <span className="text-gray-400 text-sm">
                              点击查看详情
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
} 
} 