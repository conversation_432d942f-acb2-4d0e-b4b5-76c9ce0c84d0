'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTranslations } from '@/contexts/LocaleContext';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import ReadingNotice from '@/components/ReadingNotice';
import ImageWithFallback from '@/components/ui/ImageWithFallback';

export default function Dashboard() {
  const t = useTranslations();
  
  // 仪表盘数据状态
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [myRecentDesigns, setMyRecentDesigns] = useState<any[]>([]);
  
  // 示例数据（作为备用）
  const defaultRecentDesigns = [
    { id: 1, title: '我的RGB键盘设计', date: `2${t('dashboard.hoursAgo')}`, preview: 'from-purple-400 to-pink-400', category: '个人作品' },
          { id: 2, title: '赛博朋克风格键帽', date: t('dashboard.yesterday'), preview: 'from-cyan-400 to-blue-500', category: '科幻风格' },
      { id: 3, title: '极简黑白设计', date: `3${t('dashboard.daysAgo')}`, preview: 'from-gray-400 to-gray-600', category: '极简风格' },
  ];

  // 侧边栏菜单项 - Linear 风格图标
  const menu = [
    { 
      name: t('navigation.dashboard'), 
      href: '/dashboard', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
        </svg>
      ), 
      active: true, 
      gradient: 'from-blue-500/20 to-cyan-500/20',
      borderColor: 'border-blue-500/30'
    },
    { 
      name: t('navigation.designer'), 
      href: '/designer', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z" />
        </svg>
      ), 
      gradient: 'from-purple-500/20 to-pink-500/20',
      borderColor: 'border-purple-500/30'
    },
    { 
      name: t('navigation.myDesigns'), 
      href: '/designs', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ), 
      gradient: 'from-emerald-500/20 to-teal-500/20',
      borderColor: 'border-emerald-500/30'
    },
    { 
      name: t('navigation.myMaterials'), 
      href: '/materials/my', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 5H6a2 2 0 00-2 2v6a2 2 0 002 2h2m0-10h8a2 2 0 012 2v6a2 2 0 01-2 2h-8m0-10v10" />
        </svg>
      ), 
      gradient: 'from-orange-500/20 to-red-500/20',
      borderColor: 'border-orange-500/30'
    },
    { 
      name: t('navigation.materialLibrary'), 
      href: '/materials', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ), 
      gradient: 'from-indigo-500/20 to-purple-500/20',
      borderColor: 'border-indigo-500/30'
    },
  ];

  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [currentProductIndex, setCurrentProductIndex] = useState(0);
  const [showOrderMenu, setShowOrderMenu] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showReadingNotice, setShowReadingNotice] = useState(false);

  // API配置
  const API_BASE_URL = 'http://localhost:8080/api';

  // 获取用户信息（从localStorage或使用默认值）
  const getUserInfo = () => {
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem('user');
      if (userData) {
        const user = JSON.parse(userData);
        return {
          username: user.username || '创意设计师',
          nickname: user.nickname || user.username || '创意设计师',
          email: user.email || '未绑定邮箱',
          avatar: (user.nickname || user.username) ? (user.nickname || user.username).charAt(0).toUpperCase() : 'U',
          avatarPath: user.avatarPath || '',
          userTitle: '初级设计师',
          worksCount: 0,
          shopStatus: 'CHECKING',
          followingCount: 0
        };
      }
    }
    // 默认用户信息
    return {
      username: '创意设计师',
      nickname: '创意设计师',
      email: '未绑定邮箱',
      avatar: 'U',
      avatarPath: '',
      userTitle: '初级设计师',
      worksCount: 0,
      shopStatus: 'CHECKING',
      followingCount: 0
    };
  };

  const [user, setUser] = useState(getUserInfo());
  const [unreadCount, setUnreadCount] = useState(0);

  // 获取店铺状态的显示文本
  const getShopStatusText = (status: string) => {
    switch (status) {
      case 'PENDING':
        return t('dashboard.shopPending');
      case 'APPROVED':
        return t('dashboard.shopApproved');
      case 'REJECTED':
        return t('dashboard.shopRejected');
      case 'NOT_LOGGED_IN':
        return t('dashboard.notLoggedIn');
      case 'CHECKING':
        return '检查中...';
      default:
        return t('dashboard.shopNotOpened');
    }
  };

  // 检查店铺状态
  const checkShopStatus = async () => {
    try {
      const token = localStorage.getItem('token');
          if (!token) {
      setUser(prev => ({
        ...prev,
        shopStatus: 'NOT_LOGGED_IN'
      }));
      return;
    }

      console.log('开始检查店铺状态...');
      
      const response = await fetch(`${API_BASE_URL}/shop/status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('店铺状态API响应状态:', response.status);
      
      if (!response.ok) {
        console.log('检查店铺状态失败，HTTP状态:', response.status);
        setUser(prev => ({
          ...prev,
          shopStatus: '未开通'
        }));
        return;
      }

      const result = await response.json();
      console.log('店铺状态API响应结果:', result);

      // 修改判断逻辑：兼容 result.success 和 result.code === 200 两种格式
      if (result.success || result.code === 200) {
        const status = result.data;
        let statusText = '未开通';
        
        if (status) {
          switch (status) {
            case 'PENDING':
              statusText = 'PENDING';
              break;
            case 'APPROVED':
              statusText = 'APPROVED';
              break;
            case 'REJECTED':
              statusText = 'REJECTED';
              break;
            default:
              statusText = 'NOT_OPENED';
          }
        }

        console.log('店铺状态更新为:', statusText);
        setUser(prev => ({
          ...prev,
          shopStatus: statusText
        }));
              } else {
        console.log('API调用失败或状态为空，设置为未开通');
        setUser(prev => ({
          ...prev,
          shopStatus: 'NOT_OPENED'
        }));
      }
    } catch (error) {
      console.error('检查店铺状态失败:', error);
      setUser(prev => ({
        ...prev,
        shopStatus: 'NOT_OPENED'
      }));
    }
  };

  // 获取未读消息数量
  const getUnreadCount = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setUnreadCount(0);
        return;
      }

      const response = await fetch(`${API_BASE_URL}/notifications/unread-count`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success || result.code === 200) {
          setUnreadCount(result.data || 0);
        }
      }
    } catch (error) {
      console.error('获取未读消息数量失败:', error);
    }
  };

  // 获取用户统计信息（包括关注数据和设计数量）
  const getUserStats = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      // 获取用户基本信息和关注数据
      const profileResponse = await fetch(`${API_BASE_URL}/users/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (profileResponse.ok) {
        const profileResult = await profileResponse.json();
        if (profileResult.code === 200) {
          const profile = profileResult.data;
          
          // 获取用户设计数量
          const designsResponse = await fetch(`${API_BASE_URL}/designs/my`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          
          let designsCount = 0;
          if (designsResponse.ok) {
            const designsResult = await designsResponse.json();
            if (designsResult.code === 200) {
              // 过滤掉已删除的设计
              const activeDesigns = designsResult.data.filter((design: any) => design.status !== 'DELETED');
              designsCount = activeDesigns.length;
            }
          }
          
          setUser(prev => ({
            ...prev,
            followingCount: profile.followingCount || 0,
            followersCount: profile.followersCount || 0,
            worksCount: designsCount // 使用实际的设计数量
          }));
        }
      }
    } catch (error) {
      console.error('获取用户统计信息失败:', error);
    }
  };

  // 获取仪表盘数据
  const getDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      const headers: any = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE_URL}/dashboard/data`, {
        headers
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success || result.code === 200) {
          setDashboardData(result.data);
          console.log('🎯 仪表盘数据获取成功:', result.data);
          console.log('⭐ 精品推荐数据:', result.data?.featuredProducts);
          console.log('🔥 社区热门数据:', result.data?.hotCommunity);
          console.log('💰 热销产品数据:', result.data?.hotSales);
          
          // 详细分析热销产品数据结构
          if (result.data?.hotSales && result.data.hotSales.length > 0) {
            console.log('🔍 热销产品详细分析:');
            result.data.hotSales.forEach((product: any, index: number) => {
              console.log(`📦 产品 ${index + 1}:`, {
                id: product.id,
                itemId: product.itemId,
                title: product.title,
                coverImage: product.coverImage,
                stats: product.stats,
                完整数据: product
              });
            });
          }
        } else {
          console.log('仪表盘数据获取失败:', result.message);
          setError(result.message || '获取数据失败');
        }
      } else {
        console.log('仪表盘API响应失败:', response.status);
        setError('服务器响应失败');
      }
    } catch (error) {
      console.error('获取仪表盘数据失败:', error);
      setError('网络请求失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取我的最近设计
  const getMyRecentDesigns = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        return;
      }

      const response = await fetch(`${API_BASE_URL}/designs/my`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success || result.code === 200) {
          const designs = result.data?.slice(0, 3) || []; // 只取前3个
          setMyRecentDesigns(designs);
          console.log('我的最近设计获取成功:', designs);
        }
      }
    } catch (error) {
      console.error('获取我的最近设计失败:', error);
    }
  };

  // 记录公告查看
  const recordAnnouncementView = async (announcementId: string) => {
    try {
      const token = localStorage.getItem('token');
      const headers: any = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      await fetch(`${API_BASE_URL}/dashboard/announcements/${announcementId}/view`, {
        method: 'POST',
        headers
      });
    } catch (error) {
      console.error('记录公告查看失败:', error);
    }
  };

  // 记录公告点击
  const recordAnnouncementClick = async (announcementId: string) => {
    try {
      const token = localStorage.getItem('token');
      const headers: any = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      await fetch(`${API_BASE_URL}/dashboard/announcements/${announcementId}/click`, {
        method: 'POST',
        headers
      });
    } catch (error) {
      console.error('记录公告点击失败:', error);
    }
  };

  // 产品卡片组件
  const ProductCard = ({ product, onRecordClick, getImageUrl }: {
    product: any;
    onRecordClick: (id: string) => void;
    getImageUrl: (path: string) => string;
  }) => {
    // 产品图片轮播状态
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    
    // 模拟多张图片（实际应从API获取）
    const productImages = product.images || (product.coverImage ? [product.coverImage] : ['/images/placeholders/image-placeholder.png']);
    
    // 自动轮播
    useEffect(() => {
      if (productImages.length > 1) {
        const interval = setInterval(() => {
          setCurrentImageIndex((prevIndex) => 
            (prevIndex + 1) % productImages.length
          );
        }, 3000);
        return () => clearInterval(interval);
      }
    }, [productImages.length]);

    return (
      <Link
        href="/shop"
        onClick={() => product.id && onRecordClick(product.id)}
        className="group relative bg-white rounded-2xl overflow-hidden hover:shadow-xl transition-all duration-500 hover:scale-105 border border-gray-200 hover:border-gray-300 h-[200px]"
      >
        {/* 热销标签 */}
        <div className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg z-20">
          -{product.discount || '25'}%
        </div>
        
        {/* 产品标签 */}
        <div className="absolute top-4 left-4 flex flex-wrap gap-1 z-20">
          {(product.tags || ['热销']).map((tag: string, tagIndex: number) => (
            <span key={tagIndex} className="bg-black/50 text-white px-2 py-1 rounded text-xs">
              {tag}
            </span>
          ))}
        </div>
        
        {/* 背景图片轮播区域 */}
        <div className="relative w-full h-full overflow-hidden">
          <div 
            className="flex transition-transform duration-500 ease-in-out h-full"
            style={{ transform: `translateX(-${currentImageIndex * 100}%)` }}
          >
            {productImages.map((image: string, imageIndex: number) => (
              <div key={imageIndex} className="w-full h-full flex-shrink-0">
                <ImageWithFallback
                  src={image.startsWith('/') ? image : getImageUrl(image)}
                  alt={`${product.title || product.name} 图片 ${imageIndex + 1}`}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  fallbackSrc="/images/placeholders/image-placeholder.png"
                  transparent={true}
                />
              </div>
            ))}
          </div>
          
          {/* 轮播指示器 */}
          {productImages.length > 1 && (
            <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 flex space-x-1 z-20">
              {productImages.map((_: string, imageIndex: number) => (
                <button
                  key={imageIndex}
                  onClick={(e) => {
                    e.preventDefault();
                    setCurrentImageIndex(imageIndex);
                  }}
                  className={`w-2 h-2 rounded-full transition-all duration-200 ${
                    imageIndex === currentImageIndex ? 'bg-white' : 'bg-white/50'
                  }`}
                />
              ))}
            </div>
          )}
          
          {/* 左右切换按钮 */}
          {productImages.length > 1 && (
            <>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  setCurrentImageIndex((prevIndex) => 
                    prevIndex === 0 ? productImages.length - 1 : prevIndex - 1
                  );
                }}
                className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white w-8 h-8 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity z-20"
              >
                ←
              </button>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  setCurrentImageIndex((prevIndex) => 
                    (prevIndex + 1) % productImages.length
                  );
                }}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white w-8 h-8 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity z-20"
              >
                →
              </button>
            </>
          )}
          
          {/* 透明叠加层和产品信息 */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-4 z-10">
            {/* 产品标题 */}
            <h3 className="text-lg font-bold text-white mb-1 line-clamp-1 group-hover:text-cyan-400 transition-colors">
              {product.title || product.name}
            </h3>
            
            <p className="text-gray-200 text-sm mb-2 line-clamp-1">{product.description || '高品质键帽，专业设计'}</p>
            
            {/* 评分、销量和价格 - 一行显示 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {/* 评分 */}
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <span key={i} className={`text-xs ${i < Math.floor(product.rating || 4.8) ? 'text-yellow-400' : 'text-gray-400'}`}>
                      ★
                    </span>
                  ))}
                  <span className="text-gray-300 text-xs">({product.rating || 4.8})</span>
                </div>
                
                {/* 销量 */}
                <span className="text-gray-300 text-xs">销量 {product.stats?.sales || product.sales || 0}</span>
              </div>
              
              {/* 价格和购买按钮 */}
              <div className="flex items-center space-x-3">
                <div>
                  <span className="text-lg font-bold text-cyan-400">
                    ¥{product.stats?.price?.replace('¥', '') || product.price || '199'}
                  </span>
                  {product.originalPrice && (
                    <span className="text-gray-400 text-sm line-through ml-2">¥{product.originalPrice}</span>
                  )}
                </div>
                <button className="bg-blue-600 text-white px-3 py-1 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                  购买
                </button>
              </div>
            </div>
          </div>
        </div>
      </Link>
    );
  };

  // 在组件挂载时更新用户信息和店铺状态
  useEffect(() => {
    setUser(getUserInfo());
    checkShopStatus();
    getUnreadCount();
    getDashboardData(); // 获取仪表盘数据
    getMyRecentDesigns(); // 获取我的最近设计数据
    getUserStats(); // 获取用户统计信息
    
    // 检查是否是首次注册用户，需要显示阅读须知
    const token = localStorage.getItem('token');
    const isFirstTimeUser = localStorage.getItem('isFirstTimeUser');
    
    if (token && isFirstTimeUser === 'true') {
      // 延迟1秒显示，让页面先加载完成
      setTimeout(() => {
        setShowReadingNotice(true);
      }, 1000);
    }
    
    // 监听店铺状态变化标志 - 修改为只检查一次
    const checkShopStatusFlag = () => {
      const shopStatusChanged = localStorage.getItem('shopStatusChanged');
      if (shopStatusChanged === 'true') {
        console.log('检测到店铺状态变化，刷新状态...');
        localStorage.removeItem('shopStatusChanged');
        // 设置一个标记防止短时间内重复检查
        localStorage.setItem('lastStatusCheck', Date.now().toString());
        checkShopStatus();
        getUnreadCount(); // 也刷新消息数量
      }
    };
    
    // 立即检查一次
    checkShopStatusFlag();
    
    // 只在页面首次加载时检查，不设置定时器以避免闪烁
    // 移除定时器，改为只在特定事件时检查
    // const statusCheckInterval = setInterval(() => {
    //   // 检查上次更新时间，如果小于10秒则跳过，防止频繁刷新
    //   const lastCheck = localStorage.getItem('lastStatusCheck');
    //   const now = Date.now();
    //   if (lastCheck && (now - parseInt(lastCheck)) < 10000) {
    //     console.log('跳过状态检查，上次检查时间过近');
    //     return;
    //   }
    //   
    //   checkShopStatusFlag();
    //   getUnreadCount(); // 定期刷新消息数量
    //   getUserStats(); // 定期刷新用户统计信息
    // }, 15000); // 增加检查间隔到15秒
    
    // 监听窗口焦点事件，当用户从其他页面回来时自动刷新
    const handleWindowFocus = () => {
      console.log('窗口获得焦点，检查店铺状态...');
      // 检查上次更新时间，如果小于30秒则跳过，进一步减少频率
      const lastCheck = localStorage.getItem('lastStatusCheck');
      const now = Date.now();
      if (lastCheck && (now - parseInt(lastCheck)) < 30000) {
        console.log('跳过状态检查，上次检查时间过近');
        return;
      }
      
      // 移除导航状态检查，简化逻辑
      
      localStorage.setItem('lastStatusCheck', now.toString());
      checkShopStatusFlag();
      getUnreadCount();
      getUserStats(); // 也刷新用户统计信息，包括关注数
    };
    
    window.addEventListener('focus', handleWindowFocus);
    
    return () => {
      // clearInterval(statusCheckInterval); // 已移除定时器
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, []);

  // 自动轮播商品 - 优化加载体验
  useEffect(() => {
    const featuredProducts = dashboardData?.featuredProducts || [];
    
    if (featuredProducts.length > 0) {
      // 确保初始索引有效
      if (currentProductIndex >= featuredProducts.length) {
        setCurrentProductIndex(0);
      }
      
      // 预加载所有图片
      const preloadAllImages = () => {
        console.log("预加载所有图片，共", featuredProducts.length, "个产品");
        featuredProducts.forEach((product: any, index: number) => {
          if (product?.coverImage) {
            const img = new Image();
            img.src = getProductImageUrl(product.coverImage);
            console.log("预加载产品图片", index, ":", getProductImageUrl(product.coverImage));
            img.onerror = () => console.log("图片加载失败:", getProductImageUrl(product.coverImage));
          } else {
            console.log("产品", index, "没有封面图片");
          }
        });
      };
      
      // 预加载单张图片，确保每次轮播都能显示图片
      const preloadNextImage = (index: number) => {
        const nextIndex = (index + 1) % featuredProducts.length;
        const nextProduct = featuredProducts[nextIndex];
        if (nextProduct?.coverImage) {
          const img = new Image();
          const imageUrl = getProductImageUrl(nextProduct.coverImage);
          img.src = imageUrl;
          console.log("预加载下一张图片:", imageUrl);
          return new Promise<void>((resolve) => {
            img.onload = () => resolve();
            img.onerror = () => {
              console.log("下一张图片加载失败:", imageUrl);
              resolve();
            };
            // 如果加载时间过长，也继续执行
            setTimeout(resolve, 300);
          });
        }
        return Promise.resolve();
      };
      
      // 先预加载所有图片
      preloadAllImages();
      
      // 轮播定时器
      const interval = setInterval(() => {
        // 简化轮播逻辑，避免复杂的异步操作阻塞轮播
        const nextIndex = (currentProductIndex + 1) % featuredProducts.length;
        console.log("轮播至下一张:", nextIndex, "/ 总数:", featuredProducts.length);
        setCurrentProductIndex(nextIndex);
        
        // 预加载下一张图片，但不阻塞轮播
        preloadNextImage(nextIndex);
      }, 4000); // 轮播间隔4秒，确保用户有足够时间查看
      
      return () => clearInterval(interval);
    }
  }, [dashboardData, currentProductIndex]);

  // 手动切换到指定商品
  const goToProduct = (index: number) => {
    setCurrentProductIndex(index);
  };

  // 热销产品自动轮播 - 每3秒切换
  useEffect(() => {
    const hotSalesProducts = dashboardData?.hotSales || [];
    
    // 默认产品数据
    const defaultProducts = [
      { id: '1', title: 'RGB背光键帽' },
      { id: '2', title: '机械轴测试器' },
      { id: '3', title: '定制键帽套装' },
      { id: '4', title: 'Artisan手工键帽' }
    ];

    const products = hotSalesProducts.length > 0 ? hotSalesProducts : defaultProducts;
    
    if (products.length > 1) {
      // 确保当前索引在有效范围内
      if (currentProductIndex >= products.length) {
        setCurrentProductIndex(0);
      }

      // 设置3秒自动轮播
      const interval = setInterval(() => {
        setCurrentProductIndex(prevIndex => (prevIndex + 1) % products.length);
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [dashboardData, currentProductIndex]);

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showOrderMenu && !target.closest('.order-menu-container')) {
        setShowOrderMenu(false);
      }
      if (showUserMenu && !target.closest('.user-menu-container')) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showOrderMenu, showUserMenu]);

  const handleLogout = () => {
    // 清除用户信息
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    localStorage.removeItem('admin');
    localStorage.removeItem('admin_token');
    localStorage.removeItem('user_type');
    
    // 跳转到首页
    window.location.href = '/';
  };

  // 关闭阅读须知
  const handleCloseReadingNotice = () => {
    setShowReadingNotice(false);
    // 清除首次用户标志，下次登录不再显示
    localStorage.removeItem('isFirstTimeUser');
  };

  // 添加全局样式
  const styles = {
    textShadow: {
      textShadow: '0 2px 4px rgba(0,0,0,0.5)'
    },
    textShadowLg: {
      textShadow: '0 4px 8px rgba(0,0,0,0.5)'
    }
  };

  // 处理产品图片URL的函数 - 增强版
  const getProductImageUrl = (imagePath: string): string => {
    console.log('🖼️ 处理仪表盘图片路径:', imagePath);
    
    // 如果没有提供图片路径，返回默认占位图
    if (!imagePath) {
      console.log('❌ 图片路径为空，使用默认图片');
      return '/images/placeholders/image-placeholder.png';
    }
    
    // 如果已经是完整URL，直接返回
    if (imagePath.startsWith('http')) {
      console.log('✅ 完整URL，直接返回:', imagePath);
      return imagePath;
    }
    
    // 去除开头多余的斜杠
    let cleanPath = imagePath;
    while (cleanPath.startsWith('/')) {
      cleanPath = cleanPath.substring(1);
    }
    
    const baseUrl = 'http://localhost:8080';
    let finalUrl = '';
    
    try {
      // 1. 如果路径包含uploads，则直接使用后端基地址
      if (cleanPath.includes('uploads/')) {
        finalUrl = `${baseUrl}/${cleanPath}`;
        console.log('📁 处理uploads路径:', imagePath, '->', finalUrl);
        return finalUrl;
      }
      
      // 2. 如果路径是api/uploads格式
      if (cleanPath.includes('api/uploads/')) {
        finalUrl = `${baseUrl}/${cleanPath}`;
        console.log('📁 处理api/uploads路径:', imagePath, '->', finalUrl);
        return finalUrl;
      }
      
      // 3. 如果路径是products/格式(直接指向产品目录)
      if (cleanPath.includes('products/')) {
        finalUrl = cleanPath.includes('uploads/') 
          ? `${baseUrl}/${cleanPath}` 
          : `${baseUrl}/uploads/${cleanPath}`;
        console.log('📦 处理products路径:', imagePath, '->', finalUrl);
        return finalUrl;
      }
      
      // 4. 如果以特定的UUID格式开头（可能是文件名）
      const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i;
      if (uuidPattern.test(cleanPath)) {
        finalUrl = `${baseUrl}/uploads/products/${cleanPath}`;
        console.log('🔑 处理UUID文件名:', imagePath, '->', finalUrl);
        return finalUrl;
      }
      
      // 5. 默认情况，尝试作为产品路径处理
      finalUrl = `${baseUrl}/uploads/products/${cleanPath}`;
      console.log('🛠️ 默认处理:', imagePath, '->', finalUrl);
      return finalUrl;
    } catch (e) {
      console.error("❌ 处理图片URL时出错:", e);
      return '/images/placeholders/image-placeholder.png';
    }
  };

  // 预设流星和星星位置，避免每次渲染重新计算
  const [meteorPositions] = useState(() => 
    Array.from({ length: 8 }, (_, i) => ({
      left: Math.random() * 100,
      delay: Math.random() * 15,
    }))
  );

  const [starPositions] = useState(() => 
    Array.from({ length: 15 }, (_, i) => ({
      left: Math.random() * 100,
      top: Math.random() * 100,
      delay: Math.random() * 8,
      size: Math.random() * 2 + 1,
    }))
  );

  const [glowPositions] = useState(() => 
    Array.from({ length: 20 }, (_, i) => ({
      left: Math.random() * 100,
      top: Math.random() * 100,
      delay: Math.random() * 10,
    }))
  );

  return (
          <div className="flex min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900">
        {/* 现代化侧边栏 - 深色主题 */}
        <aside className={`${sidebarOpen ? 'w-72' : 'w-20'} bg-gray-800/95 backdrop-blur-xl border-r border-gray-700/50 transition-all duration-300 ease-in-out fixed left-0 top-0 h-screen overflow-y-auto scrollbar-thin shadow-xl z-50`}>
        <div className="p-6 flex justify-between items-center">
          {sidebarOpen && (
            <Link href="/" className="flex items-center space-x-3 hover-float">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center text-white font-bold text-xs shadow-md">
                Keycap
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-medium text-white">
                  Keycap
                </span>
                <span className="text-xs text-gray-400 font-normal -mt-1">
                  Keycap Design Platform
                </span>
              </div>
            </Link>
          )}
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 rounded-lg bg-gray-700/30 text-gray-300 hover:text-white hover:bg-gray-600/40 transition-all duration-200"
          >
            {sidebarOpen ? '◀' : '▶'}
          </button>
        </div>
        
        <nav className="mt-8 px-4">
          <ul className="space-y-3">
            {menu.map((item) => (
              <li key={item.name}>
                <Link 
                  href={item.href}
                  className={`group flex items-center ${sidebarOpen ? 'px-4' : 'px-3'} py-3 rounded-xl transition-all duration-300 border backdrop-blur-sm ${
                    item.active 
                      ? `bg-gradient-to-r ${item.gradient} text-white shadow-lg border-white/10 hover:shadow-xl` 
                      : 'text-gray-400 hover:text-white hover:bg-gray-700/20 border-transparent hover:border-gray-600/30'
                  }`}
                >
                  <div className={`flex items-center justify-center w-6 h-6 ${
                    item.active ? 'text-white' : 'text-gray-400 group-hover:text-white'
                  } transition-colors duration-300`}>
                    {item.icon}
                  </div>
                  {sidebarOpen && (
                    <>
                      <span className="ml-3 font-medium tracking-wide">{item.name}</span>
                      {item.active && (
                        <div className="ml-auto">
                          <div className="w-2 h-2 rounded-full bg-white/80 shadow-sm"></div>
                        </div>
                      )}
                    </>
                  )}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </aside>
      
      {/* 主内容区 */}
      <div className={`flex-1 overflow-auto ${sidebarOpen ? 'ml-72' : 'ml-20'} transition-all duration-300 ease-in-out`}>
        {/* 现代化顶部导航 - 深色主题 */}
        <header className="bg-gray-800/90 backdrop-blur-lg border-b border-gray-700/40 px-6 py-4 sticky top-0 z-40 shadow-lg">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <div>
                <h1 className="text-3xl font-medium text-white">{t('dashboard.title')}</h1>
                <p className="text-gray-300 mt-1 font-normal text-sm">{t('dashboard.welcomeBack')}</p>
              </div>
              {/* 语言切换器 */}
              <div className="ml-6">
                <LanguageSwitcher darkMode={true} />
              </div>
              {/* 调试按钮 */}
              <button
                onClick={() => getDashboardData()}
                className="flex items-center space-x-2 px-3 py-2 bg-blue-600/30 text-blue-200 rounded-lg text-xs hover:bg-blue-500/40 transition-colors border border-blue-500/30 hover:border-blue-400/50 backdrop-blur-sm"
                title="刷新数据"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span className="font-medium">刷新</span>
              </button>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* 消息中心 */}
              <div className="relative">
                <Link
                  href="/notifications"
                  className="flex items-center space-x-2 px-4 py-2 rounded-full bg-gray-700/30 text-gray-200 hover:text-white hover:bg-gray-600/40 transition-all duration-200 border border-gray-600/40 hover:border-gray-500/60 backdrop-blur-sm"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 17h5l-5 5v-5zM11 17H7a2 2 0 01-2-2V5a2 2 0 012-2h10a2 2 0 012 2v4.5" />
                  </svg>
                  <span className="hidden sm:inline font-medium">{t('dashboard.messageCenter')}</span>
                  {/* 未读消息数量徽章 */}
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center shadow-lg border-2 border-gray-800">
                      {unreadCount > 99 ? '99+' : unreadCount}
                    </span>
                  )}
                </Link>
              </div>

              {/* 客服中心 */}
              <div className="relative">
                <Link
                  href="/customer-service"
                  className="flex items-center space-x-2 px-4 py-2 rounded-full bg-green-600/30 text-gray-200 hover:text-white hover:bg-green-600/50 transition-all duration-200 border border-green-600/40 hover:border-green-500/60 backdrop-blur-sm"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  <span className="hidden sm:inline font-medium">客服中心</span>
                </Link>
              </div>

              <div className="relative order-menu-container">
                <button 
                  onClick={() => setShowOrderMenu(!showOrderMenu)}
                  className="flex items-center space-x-2 px-4 py-2 rounded-full bg-gray-700/30 text-gray-200 hover:text-white hover:bg-gray-600/40 transition-all duration-200 border border-gray-600/40 hover:border-gray-500/60 backdrop-blur-sm"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                  </svg>
                  <span className="hidden sm:inline font-medium">{t('dashboard.myOrders')}</span>
                  <svg className={`w-4 h-4 transform transition-transform ${showOrderMenu ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                
                {showOrderMenu && (
                  <div className="absolute top-full right-0 mt-2 w-48 bg-gray-800/95 backdrop-blur-xl rounded-lg shadow-xl border border-gray-700/40 py-2 z-50">
                    <Link href="/orders/paid" className="flex items-center space-x-3 px-4 py-3 text-gray-200 hover:bg-gray-700/30 transition-colors rounded-lg mx-1">
                      <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="font-medium">{t('dashboard.paidOrders')}</span>
                    </Link>
                    <Link href="/orders/pending" className="flex items-center space-x-3 px-4 py-3 text-gray-200 hover:bg-gray-700/30 transition-colors rounded-lg mx-1">
                      <svg className="w-4 h-4 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="font-medium">{t('dashboard.pendingOrders')}</span>
                    </Link>
                    <Link href="/cart" className="flex items-center space-x-3 px-4 py-3 text-gray-200 hover:bg-gray-700/30 transition-colors rounded-lg mx-1">
                      <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6.28" />
                      </svg>
                      <span className="font-medium">{t('navigation.cart')}</span>
                    </Link>
                  </div>
                )}
              </div>
              

              
              <Link 
                href="/community" 
                className="flex items-center space-x-2 px-4 py-2 rounded-full bg-gray-700/30 text-gray-200 hover:text-white hover:bg-gray-600/40 transition-all duration-200 border border-gray-600/40 hover:border-gray-500/60 backdrop-blur-sm"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span className="hidden sm:inline font-medium">{t('navigation.community')}</span>
              </Link>
              
              <Link 
                href="/shop" 
                className="flex items-center space-x-2 px-4 py-2 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 transition-all duration-200 shadow-lg border border-white/20 hover:border-white/30 backdrop-blur-sm"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                <span className="hidden sm:inline font-medium">{t('navigation.shop')}</span>
              </Link>
              
              {/* 创作者下拉菜单 */}
              <div className="relative user-menu-container">
                <button 
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center space-x-3 px-4 py-2 rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all duration-200"
                >
                  <span className="text-gray-700 text-sm hidden sm:inline font-normal">{t('dashboard.creator')}</span>
                  <div className="w-8 h-8 rounded-full overflow-hidden bg-gradient-to-r from-blue-600 to-blue-500 flex items-center justify-center shadow-md">
                    {user.avatarPath ? (
                      <img 
                        src={`http://localhost:8080${user.avatarPath}`} 
                        alt="用户头像" 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const fallback = target.parentElement?.querySelector('.fallback-avatar') as HTMLElement;
                          if (fallback) fallback.style.display = 'flex';
                        }}
                      />
                    ) : null}
                    <div className={`w-full h-full text-white font-bold text-sm flex items-center justify-center fallback-avatar ${user.avatarPath ? 'hidden' : ''}`}>
                      {user.avatar}
                    </div>
                  </div>
                  <span className={`text-gray-700 transform transition-transform ${showUserMenu ? 'rotate-180' : ''}`}>▼</span>
                </button>
                
                {showUserMenu && (
                  <div className="absolute top-full right-0 mt-2 w-72 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                    {/* 用户信息头部 */}
                    <div className="px-4 py-3 border-b border-gray-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-r from-blue-600 to-blue-500 flex items-center justify-center shadow-md">
                          {user.avatarPath ? (
                            <img 
                              src={`http://localhost:8080${user.avatarPath}`} 
                              alt="用户头像" 
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                const fallback = target.parentElement?.querySelector('.fallback-avatar') as HTMLElement;
                                if (fallback) fallback.style.display = 'flex';
                              }}
                            />
                          ) : null}
                          <div className={`w-full h-full text-white font-bold text-lg flex items-center justify-center fallback-avatar ${user.avatarPath ? 'hidden' : ''}`}>
                            {user.avatar}
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{user.nickname || user.username}</div>
                          <div className="text-sm text-gray-600">{user.email}</div>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className="text-xs bg-gradient-to-r from-blue-600 to-blue-500 text-white px-2 py-0.5 rounded-full shadow-md">
                              {user.userTitle}
                            </span>
                            <span className="text-xs text-gray-500">
                              {user.worksCount} {t('dashboard.works')}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* 菜单项 */}
                    <div className="py-2">
                      <Link 
                        href="/designs" 
                        className="flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors rounded-lg mx-2"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z" />
                        </svg>
                        <span className="font-medium">{t('dashboard.myWorks')}</span>
                        <span className="ml-auto text-xs bg-blue-500 text-white px-2 py-0.5 rounded-full">
                          {user.worksCount}
                        </span>
                      </Link>
                      
                      <Link 
                        href="/following" 
                        className="flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors rounded-lg mx-2"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <svg className="w-5 h-5 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span className="font-medium">我的关注</span>
                        <span className="ml-auto text-xs bg-pink-500 text-white px-2 py-0.5 rounded-full">
                          {user.followingCount || 0}
                        </span>
                      </Link>
                      
                      <Link 
                        href="/shop/my-store" 
                        className="flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors rounded-lg mx-2"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        <span className="font-medium">{t('navigation.myStore')}</span>
                        <span className={`ml-auto text-xs px-2 py-0.5 rounded-full ${
                          user.shopStatus === 'APPROVED' 
                            ? 'bg-green-500 text-white'
                            : user.shopStatus === 'PENDING'
                            ? 'bg-yellow-500 text-white'
                            : user.shopStatus === 'REJECTED'
                            ? 'bg-red-500 text-white'
                            : 'bg-gray-500 text-white'
                        }`}>
                          {getShopStatusText(user.shopStatus)}
                        </span>
                      </Link>
                      
                      <div className="border-t border-gray-200 my-2"></div>
                      
                      <Link 
                        href="/settings" 
                        className="flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors rounded-lg mx-2"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <span className="font-medium">{t('common.settings')}</span>
                      </Link>
                      
                      <Link 
                        href="/help" 
                        className="flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors rounded-lg mx-2"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="font-medium">{t('navigation.help')}</span>
                      </Link>
                      
                      <div className="border-t border-gray-200 my-2"></div>
                      
                      <button 
                        onClick={handleLogout}
                        className="w-full flex items-center space-x-3 px-4 py-2 text-red-600 hover:bg-red-50 transition-colors rounded-lg mx-2"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        <span className="font-medium">{t('common.logout')}</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>
        
        {/* 主要内容 */}
        <main className="p-6 space-y-8 bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 min-h-screen">
          {/* 快速行动区域 - 三个整齐的卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Link href="/designer" className="group">
              <div className="relative bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 rounded-2xl p-6 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105 h-48">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-600/20"></div>
                <div className="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
                <div className="absolute bottom-4 left-4 w-12 h-12 bg-white/5 rounded-full blur-lg"></div>
                <div className="relative z-10 h-full flex flex-col justify-between">
                  <div className="w-12 h-12 text-white/90">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2">{t('dashboard.startDesigning')}</h3>
                    <p className="text-white/90 text-sm mb-4">释放你的创造力</p>
                    <div className="inline-flex items-center space-x-2 bg-white/20 rounded-full px-4 py-2 text-white text-sm">
                      <span>开始创作</span>
                      <span>→</span>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
            
            <Link href="/community" className="group">
              <div className="relative bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl p-6 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105 h-48">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-400/20 to-purple-600/20"></div>
                <div className="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
                <div className="absolute bottom-4 left-4 w-12 h-12 bg-white/5 rounded-full blur-lg"></div>
                <div className="relative z-10 h-full flex flex-col justify-between">
                  <div className="w-12 h-12 text-white/90">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2">{t('dashboard.browseCommunity')}</h3>
                    <p className="text-white/90 text-sm mb-4">与社区互动</p>
                    <div className="inline-flex items-center space-x-2 bg-white/20 rounded-full px-4 py-2 text-white text-sm">
                      <span>浏览社区</span>
                      <span>→</span>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
            
            <Link href="/materials" className="group">
              <div className="relative bg-gradient-to-br from-green-500 to-green-600 rounded-2xl p-6 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105 h-48">
                <div className="absolute inset-0 bg-gradient-to-br from-green-400/20 to-green-600/20"></div>
                <div className="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
                <div className="absolute bottom-4 left-4 w-12 h-12 bg-white/5 rounded-full blur-lg"></div>
                <div className="relative z-10 h-full flex flex-col justify-between">
                  <div className="w-12 h-12 text-white/90">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2">浏览素材库</h3>
                    <p className="text-white/90 text-sm mb-4">发现设计灵感</p>
                    <div className="inline-flex items-center space-x-2 bg-white/20 rounded-full px-4 py-2 text-white text-sm">
                      <span>素材库</span>
                      <span>→</span>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          </div>
          
          {/* 热销产品 - 轮播展示 */}
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-white">热销产品</h2>
                  <p className="text-gray-300 text-sm">精选优质键帽作品</p>
                </div>
              </div>
              <Link 
                href="/shop" 
                className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-cyan-500 to-purple-600 text-white rounded-full hover:shadow-lg transition-all duration-300 text-sm font-medium"
              >
                <span>查看全部</span>
                <span>→</span>
              </Link>
            </div>
            
            {/* 单卡片轮播展示 */}
            <div className="relative w-full max-w-5xl mx-auto">
              {(() => {
                // 从API获取真实的热销产品数据
                const apiProducts = dashboardData?.hotSales || [];
                console.log('仪表盘热销产品数据:', apiProducts);

                // 处理API产品数据格式 - 仪表盘API返回的是RecommendationDto格式
                const processedProducts = apiProducts.map((product: any) => {
                  console.log('🔍 处理仪表盘产品数据:', product);
                  
                  // 仪表盘API返回的是已经转换的RecommendationDto格式
                  // 直接使用coverImage字段，这是后端已经处理好的第一张图片
                  let images = [];
                  if (product.coverImage) {
                    images = [product.coverImage];
                    console.log('✅ 使用后端提供的coverImage:', product.coverImage);
                  } else {
                    images = ['/images/placeholders/image-placeholder.png'];
                    console.log('⚠️ 没有coverImage，使用默认图片');
                  }

                  // 提取价格数字
                  let price = 199;
                  if (product.stats?.price) {
                    // 从"¥199"中提取数字
                    const priceMatch = product.stats.price.match(/[\d.]+/);
                    if (priceMatch) {
                      price = parseFloat(priceMatch[0]);
                    }
                    console.log('💰 价格处理:', product.stats.price, '->', price);
                  }

                  const processedProduct = {
                    id: product.id || product.itemId,
                    title: product.title,
                    name: product.title,
                    description: product.description || '高品质键帽，专业设计',
                    price: Math.floor(price),
                    originalPrice: Math.floor(price * 1.2),
                    discount: '20',
                    rating: 4.8,
                    sales: product.stats?.sales || 0,
                    tags: ['热销'],
                    images: images,
                    coverImage: images[0]
                  };
                  
                  console.log('✨ 最终处理结果:', processedProduct);
                  return processedProduct;
                });

                // 默认产品数据（当API没有数据时使用）
                const defaultProducts = [
                  { 
                    id: '1', 
                    title: 'RGB背光键帽', 
                    name: 'RGB背光键帽',
                    description: '支持RGB背光的透明键帽', 
                    price: 159,
                    originalPrice: 199,
                    discount: '20',
                    rating: 4.8,
                    sales: 856,
                    tags: ['热销', '炫酷'],
                    images: ['/images/placeholders/image-placeholder.png'],
                    coverImage: '/images/placeholders/image-placeholder.png'
                  },
                  { 
                    id: '2', 
                    title: '机械轴测试器', 
                    name: '机械轴测试器',
                    description: '测试不同机械轴手感的工具', 
                    price: 89,
                    originalPrice: 118,
                    discount: '25',
                    rating: 4.6,
                    sales: 642,
                    tags: ['实用', '测试'],
                    images: ['/images/placeholders/image-placeholder.png'],
                    coverImage: '/images/placeholders/image-placeholder.png'
                  },
                  { 
                    id: '3', 
                    title: '定制键帽套装', 
                    name: '定制键帽套装',
                    description: '个性化定制，展示你的创意想法', 
                    price: 199,
                    originalPrice: 299,
                    discount: '33',
                    rating: 4.9,
                    sales: 456,
                    tags: ['定制', '个性'],
                    images: ['/images/placeholders/image-placeholder.png'],
                    coverImage: '/images/placeholders/image-placeholder.png'
                  }
                ];

                const products = processedProducts.length > 0 ? processedProducts : defaultProducts;
                console.log('最终使用的热销产品数据:', products);

                if (products.length === 0) {
                  return (
                    <div className="flex items-center justify-center py-20 text-gray-400">
                      <div className="text-center">
                        <div className="text-4xl mb-2">🛒</div>
                        <p>暂无热销产品</p>
                      </div>
                    </div>
                  );
                }

                const currentProduct = products[currentProductIndex];

                return (
                  <div className="group bg-gradient-to-b from-purple-800/20 to-cyan-800/20 backdrop-blur-lg rounded-2xl overflow-hidden hover:shadow-2xl hover:shadow-purple-500/25 transition-all duration-500">
                    {/* 折扣标签 */}
                    <div className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg z-10">
                      -{currentProduct.discount || '25'}%
                    </div>
                    
                    {/* 产品标签 */}
                    <div className="absolute top-4 left-4 flex flex-wrap gap-1 z-10">
                      {(currentProduct.tags || ['热销']).map((tag: string, tagIndex: number) => (
                        <span key={tagIndex} className="bg-black/50 text-white px-2 py-1 rounded text-xs">
                          {tag}
                        </span>
                      ))}
                    </div>

                    <Link href="/shop" className="block">
                      {/* 背景图片 */}
                      <div className="relative h-96 bg-gradient-to-br from-gray-800 to-gray-900 overflow-hidden min-h-[384px]">
                        {(() => {
                          const imageSource = currentProduct.images?.[0] || currentProduct.coverImage || '/images/placeholders/image-placeholder.png';
                          const finalImageUrl = getProductImageUrl(imageSource);
                          console.log('🖼️ 当前产品图片信息:', {
                            产品标题: currentProduct.title,
                            原始图片数组: currentProduct.images,
                            封面图片: currentProduct.coverImage,
                            选择的图片源: imageSource,
                            最终图片URL: finalImageUrl
                          });
                          
                          return (
                            <div className="w-full h-full relative">
                              {/* 直接使用img标签，完全填充 */}
                              <img
                                src={finalImageUrl}
                                alt={currentProduct.title || currentProduct.name}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                                style={{
                                  opacity: 1,
                                  minHeight: '100%',
                                  minWidth: '100%',
                                  display: 'block'
                                }}
                                onLoad={() => console.log('✅ 直接img加载成功:', finalImageUrl)}
                                onError={(e) => {
                                  console.log('❌ 直接img加载失败:', finalImageUrl);
                                  // 尝试设置备用图片
                                  (e.target as HTMLImageElement).src = '/images/placeholders/image-placeholder.png';
                                }}
                              />
                            </div>
                          );
                        })()}
                        
                        {/* 透明叠加层和产品信息 */}
                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-6">
                          {/* 产品标题 */}
                          <h3 className="text-xl font-bold text-white mb-1 group-hover:text-cyan-400 transition-colors">
                            {currentProduct.title || currentProduct.name}
                          </h3>
                          
                          <p className="text-gray-200 text-sm mb-3">{currentProduct.description || '高品质键帽，专业设计'}</p>
                          
                          {/* 评分、销量和价格 - 一行显示 */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              {/* 评分 */}
                              <div className="flex items-center space-x-1">
                                {[...Array(5)].map((_, i) => (
                                  <span key={i} className={`text-xs ${i < Math.floor(currentProduct.rating || 4.8) ? 'text-yellow-400' : 'text-gray-400'}`}>
                                    ★
                                  </span>
                                ))}
                                <span className="text-gray-300 text-xs">({currentProduct.rating || 4.8})</span>
                              </div>
                              
                              {/* 销量 */}
                              <span className="text-gray-300 text-xs">销量 {currentProduct.sales || currentProduct.salesCount || 0}</span>
                            </div>
                            
                            {/* 价格和购买按钮 */}
                            <div className="flex items-center space-x-3">
                              <div>
                                <span className="text-xl font-bold text-cyan-400">
                                  ¥{Math.floor(typeof currentProduct.price === 'string' ? 
                                    parseFloat(currentProduct.price.replace('¥', '').replace(',', '')) : 
                                    (currentProduct.price || 199))}
                                </span>
                                {currentProduct.originalPrice && (
                                  <span className="text-gray-400 text-sm line-through ml-2">
                                    ¥{Math.floor(typeof currentProduct.originalPrice === 'string' ? 
                                      parseFloat(currentProduct.originalPrice.replace('¥', '').replace(',', '')) : 
                                      currentProduct.originalPrice)}
                                  </span>
                                )}
                              </div>
                              <button className="bg-gradient-to-r from-cyan-500 to-purple-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300 hover:scale-105 text-sm font-medium">
                                立即购买
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Link>

                    {/* 轮播指示器 */}
                    {products.length > 1 && (
                      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
                        {products.map((_: any, index: number) => (
                          <button
                            key={index}
                            onClick={() => setCurrentProductIndex(index)}
                            className={`w-2 h-2 rounded-full transition-all duration-200 ${
                              index === currentProductIndex ? 'bg-white' : 'bg-white/50'
                            }`}
                          />
                        ))}
                      </div>
                    )}

                    {/* 左右切换按钮 */}
                    {products.length > 1 && (
                      <>
                        <button
                          onClick={() => goToProduct((currentProductIndex - 1 + products.length) % products.length)}
                          className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white w-10 h-10 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity z-20"
                        >
                          ←
                        </button>
                        <button
                          onClick={() => goToProduct((currentProductIndex + 1) % products.length)}
                          className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white w-10 h-10 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity z-20"
                        >
                          →
                        </button>
                      </>
                    )}
                  </div>
                );
              })()}
            </div>
          </div>

          {/* 社区热门 - 重新设计 */}
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <svg className="w-6 h-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z" />
                </svg>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-white">社区热门</h2>
                  <p className="text-gray-300 text-sm">社区精选设计作品</p>
                </div>
              </div>
              <Link 
                href="/community" 
                className="flex items-center space-x-2 px-4 py-2 bg-purple-800/30 text-gray-200 hover:text-white hover:bg-purple-700/40 transition-all duration-200"
              >
                <span>👥</span>
                <span className="hidden sm:inline">{t('navigation.community')}</span>
              </Link>
            </div>
            
            <div className="bg-gradient-to-b from-purple-800/20 to-cyan-800/20 backdrop-blur-lg rounded-xl border border-purple-500/30 overflow-hidden shadow-sm">
              {(() => {
                // 从API获取数据或使用默认数据
                const apiDesigns = dashboardData?.hotCommunity || [];

                // 默认社区数据
                const defaultDesigns = [
                  { id: '1', title: '我和我的动物朋友们 Pt.2', author: '和谐', timeAgo: '21天前', likes: 39, comments: 14, avatar: 'H' },
                                      { id: '2', title: '关西关东一周目｜闲庭信步/走马观花', author: '吹吹吹风', timeAgo: '2025.5.22', likes: 40, comments: 20, avatar: '吹' },
                    { id: '3', title: '黑子哥立体异次元茶茶汤羊羊风世界的龙珠z', author: '从前有座山', timeAgo: '2025.5.20', likes: 88, comments: 32, avatar: '从' },
                                      { id: '4', title: '樱花季粉色键帽设计', author: '设计师小王', timeAgo: '2天前', likes: 156, comments: 45, avatar: '王' }
                ];

                const designs = apiDesigns.length > 0 ? apiDesigns : defaultDesigns;

                return designs.map((design: any, index: number) => (
                  <Link
                    key={design.id}
                    href={design.targetUrl || '/community'}
                    onClick={() => design.id && recordAnnouncementView(design.id)}
                    className={`group flex items-center p-3 hover:bg-purple-800/20 transition-colors duration-200 ${index !== designs.length - 1 ? 'border-b border-purple-500/20' : ''}`}
                  >
                    {/* 作者头像 */}
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs mr-3 flex-shrink-0 overflow-hidden">
                      {design.authorAvatar ? (
                        <img 
                          src={`http://localhost:8080${design.authorAvatar}`} 
                          alt={design.author || '用户头像'} 
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            if (target.nextSibling) {
                              (target.nextSibling as HTMLElement).style.display = 'flex';
                            }
                          }}
                        />
                      ) : null}
                      <div 
                        className={`w-full h-full flex items-center justify-center ${design.authorAvatar ? 'hidden' : 'flex'}`}
                        style={{ display: design.authorAvatar ? 'none' : 'flex' }}
                      >
                        {design.avatar || design.author?.charAt(0) || (
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      )}
                      </div>
                    </div>
                      
                    {/* 帖子内容 */}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-medium text-white group-hover:text-cyan-400 transition-colors truncate mb-0.5">
                        {design.title}
                      </h3>
                      <div className="flex items-center space-x-3 text-xs text-gray-300">
                        <span>{design.author || '匿名用户'}</span>
                        <span>{design.timeAgo || '最近'}</span>
                        <div className="flex items-center space-x-1">
                          <span>👍</span>
                          <span>{design.likes || design.stats?.likes || 0}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <span>💬</span>
                          <span>{design.comments || design.stats?.comments || 0}</span>
                        </div>
                      </div>
                    </div>
                    
                    {/* 箭头指示 */}
                    <div className="text-gray-400 group-hover:text-cyan-400 transition-colors ml-3">
                      →
                    </div>
                  </Link>
                ));
              })()}
            </div>
          </div>

          {/* 我的最近设计 - 重新设计 */}
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <span className="text-xl">🎨</span>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-white">我的最近设计</h2>
                  <p className="text-gray-300 text-sm">你的创作足迹</p>
                </div>
              </div>
              <Link 
                href="/designs" 
                className="flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-full hover:bg-green-600 transition-all duration-300 text-sm font-medium"
              >
                <span>查看全部</span>
                <span>→</span>
              </Link>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {(() => {
                // 使用真实数据或默认数据
                const designs = myRecentDesigns.length > 0 ? myRecentDesigns : defaultRecentDesigns;
                
                return designs.map((design: any) => (
                  <Link
                    key={design.id || design.designId}
                    href="/designs"
                    className="group bg-white rounded-xl overflow-hidden border border-gray-200 hover:border-gray-300 transition-all duration-300 hover:shadow-lg transform hover:scale-105"
                  >
                    <div className={`relative h-40 bg-gradient-to-br ${design.preview || 'from-green-500 to-emerald-600'} flex items-center justify-center`}>
                      <div className="absolute inset-0 bg-black/20"></div>
                      <div className="relative z-10 text-center">
                        <div className="text-4xl mb-2">⌨️</div>
                        <div className="text-white font-medium text-sm">{design.title || design.designName}</div>
                      </div>
                      
                      <div className="absolute top-2 left-2 bg-black/30 backdrop-blur-sm rounded-full px-2 py-1">
                        <span className="text-white text-xs">{design.category || '个人作品'}</span>
                      </div>
                    </div>
                    
                    <div className="p-4">
                      <h3 className="text-base font-medium text-gray-900 mb-1 group-hover:text-green-600 transition-colors truncate">
                        {design.title || design.designName}
                      </h3>
                      <p className="text-gray-600 text-sm">
                        {design.date || (design.createTime ? 
                          new Date(design.createTime).toLocaleDateString('zh-CN', {
                            month: 'long',
                            day: 'numeric'
                          })
                        : '最近')}
                      </p>
                    </div>
                  </Link>
                ));
              })()}
            </div>
          </div>

        </main>
      </div>
      
      {/* 阅读须知弹出框 */}
      <ReadingNotice 
        isOpen={showReadingNotice}
        onClose={handleCloseReadingNotice}
      />
    </div>
  );
}