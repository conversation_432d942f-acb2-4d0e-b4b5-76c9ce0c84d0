'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Locale, getCurrentLocale, getStoredLocale, setStoredLocale } from '@/lib/locale';

// 加载语言资源的函数
async function loadMessages(locale: Locale) {
  try {
    const messages = await import(`../../messages/${locale}.json`);
    return messages.default;
  } catch (error) {
    console.error(`Failed to load messages for locale ${locale}:`, error);
    // 如果加载失败，回退到中文
    const fallbackMessages = await import(`../../messages/zh.json`);
    return fallbackMessages.default;
  }
}

// 上下文类型定义
interface LocaleContextType {
  locale: Locale;
  messages: any;
  isLoading: boolean;
  setLocale: (locale: Locale) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
}

// 创建上下文
const LocaleContext = createContext<LocaleContextType | null>(null);

// Hook 用于使用上下文
export function useLocale() {
  const context = useContext(LocaleContext);
  if (!context) {
    throw new Error('useLocale must be used within a LocaleProvider');
  }
  return context;
}

// 简单的字符串插值函数
function interpolate(template: string, params: Record<string, string | number> = {}): string {
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return key in params ? String(params[key]) : match;
  });
}

// 获取嵌套对象的值
function getNestedValue(obj: any, path: string): string {
  const keys = path.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return path; // 如果找不到，返回原始路径作为回退
    }
  }
  
  return typeof current === 'string' ? current : path;
}

// 提供者组件
interface LocaleProviderProps {
  children: ReactNode;
  initialLocale?: Locale;
}

export function LocaleProvider({ children, initialLocale }: LocaleProviderProps) {
  const [locale, setLocaleState] = useState<Locale>('zh'); // 始终从默认语言开始，避免SSR不匹配
  const [messages, setMessages] = useState<any>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isClient, setIsClient] = useState(false);

  // 翻译函数
  const t = (key: string, params?: Record<string, string | number>): string => {
    const value = getNestedValue(messages, key);
    return params ? interpolate(value, params) : value;
  };

  // 设置新语言
  const setLocale = async (newLocale: Locale) => {
    setIsLoading(true);
    try {
      const newMessages = await loadMessages(newLocale);
      setMessages(newMessages);
      setLocaleState(newLocale);
      setStoredLocale(newLocale);
    } catch (error) {
      console.error('Failed to set locale:', error);
      // 如果设置失败，至少确保loading状态正确
      setIsLoading(false);
    } finally {
      setIsLoading(false);
    }
  };

  // 检测客户端环境
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 初始化语言设置
  useEffect(() => {
    if (!isClient) return; // 只在客户端执行
    
    let isMounted = true; // 防止组件卸载后的状态更新
    
    const initializeLocale = async () => {
      try {
        let currentLocale = 'zh';
        
        // 先加载默认语言，确保有基础的翻译内容
        const defaultMessages = await loadMessages('zh');
        if (isMounted) {
          setMessages(defaultMessages);
          setIsLoading(false); // 先设置为不加载，提供基础功能
        }
        
        // 然后尝试获取用户偏好语言
        if (!initialLocale) {
          try {
            currentLocale = await getCurrentLocale();
          } catch (error) {
            console.warn('Failed to get current locale, using default:', error);
            currentLocale = 'zh';
          }
        } else {
          currentLocale = initialLocale;
        }
        
        if (!isMounted) return; // 组件已卸载，不更新状态
        
                 // 如果语言不是中文，加载对应的语言文件
         if (currentLocale !== 'zh') {
           const localeMessages = await loadMessages(currentLocale as Locale);
           if (isMounted) {
             setMessages(localeMessages);
             setLocaleState(currentLocale as Locale);
           }
         }
      } catch (error) {
        console.error('Failed to initialize locale:', error);
        if (!isMounted) return;
        
        // 确保至少有中文翻译可用
        try {
          const fallbackMessages = await loadMessages('zh');
          if (isMounted) {
            setMessages(fallbackMessages);
            setLocaleState('zh');
          }
        } catch (fallbackError) {
          console.error('Failed to load fallback messages:', fallbackError);
        }
      }
    };

    initializeLocale();
    
    // 清理函数
    return () => {
      isMounted = false;
    };
  }, [isClient, initialLocale]);

  const contextValue: LocaleContextType = {
    locale,
    messages,
    isLoading,
    setLocale,
    t
  };

  // 如果还在加载或不在客户端，显示加载状态
  if (!isClient || (isLoading && Object.keys(messages).length === 0)) {
    return (
      <LocaleContext.Provider value={contextValue}>
        <div className="min-h-screen bg-gray-100 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4 animate-pulse">
              灵
            </div>
            <p className="text-gray-600">加载中...</p>
          </div>
        </div>
      </LocaleContext.Provider>
    );
  }

  return (
    <LocaleContext.Provider value={contextValue}>
      {children}
    </LocaleContext.Provider>
  );
}

// 简化的翻译 Hook，直接返回翻译函数
export function useTranslations() {
  const { t } = useLocale();
  return t;
} 