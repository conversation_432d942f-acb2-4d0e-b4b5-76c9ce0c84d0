'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

// 订单接口定义
interface OrderItem {
  productId: string;
  productName: string;
  quantity: number;
  price: number;
  totalPrice: number;
}

interface Order {
  id: string;
  orderId: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  items: OrderItem[];
  totalAmount: number;
  status: 'PENDING' | 'PAID' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED';
  paymentStatus: 'UNPAID' | 'PAID' | 'REFUNDED';
  shippingAddress: string;
  createTime: string;
  updateTime: string;
  notes?: string;
}

interface OrderStats {
  total: number;
  pending: number;
  processing: number;
  shipped: number;
  delivered: number;
}

export default function ShopOrders() {
  const router = useRouter();
  const { user } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [stats, setStats] = useState<OrderStats>({ total: 0, pending: 0, processing: 0, shipped: 0, delivered: 0 });
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'processing' | 'shipped' | 'delivered'>('all');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  // 模拟订单数据
  const mockOrders: Order[] = [
    {
      id: '1',
      orderId: 'ORD-20250621-001',
      customerId: 'user001',
      customerName: '张三',
      customerEmail: '<EMAIL>',
      items: [
        {
          productId: 'PRD-001',
          productName: '樱花粉键帽套装',
          quantity: 1,
          price: 299.00,
          totalPrice: 299.00
        }
      ],
      totalAmount: 299.00,
      status: 'PAID',
      paymentStatus: 'PAID',
      shippingAddress: '北京市朝阳区某某街道123号',
      createTime: '2025-06-21T10:30:00Z',
      updateTime: '2025-06-21T10:35:00Z',
      notes: '客户要求加快发货'
    },
    {
      id: '2',
      orderId: 'ORD-20250621-002',
      customerId: 'user002',
      customerName: '李四',
      customerEmail: '<EMAIL>',
      items: [
        {
          productId: 'PRD-002',
          productName: '机械感工业风键帽',
          quantity: 2,
          price: 199.00,
          totalPrice: 398.00
        }
      ],
      totalAmount: 398.00,
      status: 'PROCESSING',
      paymentStatus: 'PAID',
      shippingAddress: '上海市浦东新区某某路456号',
      createTime: '2025-06-20T15:20:00Z',
      updateTime: '2025-06-21T09:15:00Z'
    },
    {
      id: '3',
      orderId: 'ORD-20250620-003',
      customerId: 'user003',
      customerName: '王五',
      customerEmail: '<EMAIL>',
      items: [
        {
          productId: 'PRD-003',
          productName: '复古打字机键帽',
          quantity: 1,
          price: 350.00,
          totalPrice: 350.00
        }
      ],
      totalAmount: 350.00,
      status: 'SHIPPED',
      paymentStatus: 'PAID',
      shippingAddress: '广州市天河区某某大道789号',
      createTime: '2025-06-20T08:45:00Z',
      updateTime: '2025-06-21T11:20:00Z'
    }
  ];

  // 获取订单列表
  const fetchOrders = async () => {
    try {
      setLoading(true);
      // TODO: 替换为实际API调用
      // const response = await fetch('/api/shop/orders', {
      //   headers: { 'Authorization': `Bearer ${token}` }
      // });
      
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      setOrders(mockOrders);
      
      // 计算统计数据
      const newStats = {
        total: mockOrders.length,
        pending: mockOrders.filter(o => o.status === 'PENDING').length,
        processing: mockOrders.filter(o => o.status === 'PROCESSING').length,
        shipped: mockOrders.filter(o => o.status === 'SHIPPED').length,
        delivered: mockOrders.filter(o => o.status === 'DELIVERED').length
      };
      setStats(newStats);
    } catch (error) {
      console.error('获取订单列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 更新订单状态
  const handleUpdateStatus = async (orderId: string, newStatus: Order['status']) => {
    try {
      // TODO: 实际API调用
      console.log('更新订单状态:', orderId, newStatus);
      
      setOrders(prev => prev.map(order => 
        order.id === orderId 
          ? { ...order, status: newStatus, updateTime: new Date().toISOString() }
          : order
      ));
      
      alert('订单状态更新成功！');
    } catch (error) {
      console.error('更新订单状态失败:', error);
      alert('更新失败，请重试');
    }
  };

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }
    fetchOrders();
  }, [user, router]);

  // 筛选订单
  const filteredOrders = orders.filter(order => {
    if (activeTab === 'all') return true;
    if (activeTab === 'pending') return order.status === 'PENDING';
    if (activeTab === 'processing') return order.status === 'PROCESSING';
    if (activeTab === 'shipped') return order.status === 'SHIPPED';
    if (activeTab === 'delivered') return order.status === 'DELIVERED';
    return true;
  });

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'PAID':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'PROCESSING':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'SHIPPED':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'DELIVERED':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'PENDING':
        return '待支付';
      case 'PAID':
        return '已支付';
      case 'PROCESSING':
        return '处理中';
      case 'SHIPPED':
        return '已发货';
      case 'DELIVERED':
        return '已送达';
      case 'CANCELLED':
        return '已取消';
      default:
        return '未知';
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* 顶部导航 */}
      <nav className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-sm mr-3">
                K
              </Link>
              <h1 className="text-xl font-bold text-white">订单管理</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/notifications"
                className="text-gray-300 hover:text-white transition-colors"
              >
                <span className="text-xl">🔔</span>
              </Link>
              <span className="text-gray-300">店主：{user.username}</span>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* 侧边栏 */}
        <aside className="w-64 bg-gray-800 min-h-screen pt-4">
          <nav className="px-4 space-y-2">
            <Link href="/dashboard" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🏠</span>
              返回首页
            </Link>
            <Link href="/shop/my-store" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🏪</span>
              我的店铺
            </Link>
            <Link href="/shop/products" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📦</span>
              商品管理
            </Link>
            <Link href="/shop/orders" className="flex items-center px-4 py-2 text-white bg-blue-600 rounded-lg">
              <span className="mr-3">📋</span>
              订单管理
            </Link>
            <Link href="/shop/settings" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">⚙️</span>
              店铺设置
            </Link>
          </nav>
        </aside>

        {/* 主内容区 */}
        <main className="flex-1 p-6">
          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <div className="bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm font-medium">订单总数</p>
                  <p className="text-3xl font-bold">{stats.total}</p>
                </div>
                <div className="text-4xl opacity-80">📋</div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-100 text-sm font-medium">待处理</p>
                  <p className="text-3xl font-bold">{stats.pending}</p>
                </div>
                <div className="text-4xl opacity-80">⏳</div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm font-medium">处理中</p>
                  <p className="text-3xl font-bold">{stats.processing}</p>
                </div>
                <div className="text-4xl opacity-80">🔄</div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm font-medium">已发货</p>
                  <p className="text-3xl font-bold">{stats.shipped}</p>
                </div>
                <div className="text-4xl opacity-80">🚚</div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm font-medium">已完成</p>
                  <p className="text-3xl font-bold">{stats.delivered}</p>
                </div>
                <div className="text-4xl opacity-80">✅</div>
              </div>
            </div>
          </div>

          {/* 筛选和操作栏 */}
          <div className="bg-gray-800 rounded-2xl p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-white">订单列表</h2>
              <button
                onClick={() => fetchOrders()}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                🔄 刷新
              </button>
            </div>
            
            <div className="flex space-x-2">
              {[
                { key: 'all', label: '全部', count: stats.total },
                { key: 'pending', label: '待处理', count: stats.pending },
                { key: 'processing', label: '处理中', count: stats.processing },
                { key: 'shipped', label: '已发货', count: stats.shipped },
                { key: 'delivered', label: '已完成', count: stats.delivered }
              ].map(tab => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    activeTab === tab.key
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  {tab.label} ({tab.count})
                </button>
              ))}
            </div>
          </div>

          {/* 订单列表 */}
          <div className="bg-gray-800 rounded-2xl overflow-hidden">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className="ml-3 text-gray-300">加载中...</span>
              </div>
            ) : filteredOrders.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📋</div>
                <h3 className="text-xl font-bold text-white mb-2">暂无订单</h3>
                <p className="text-gray-400">等待客户下单...</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-700">
                    <tr>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">订单信息</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">客户</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">商品</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">金额</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">状态</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">下单时间</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">操作</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700">
                    {filteredOrders.map((order) => (
                      <tr key={order.id} className="hover:bg-gray-700/50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-white">{order.orderId}</div>
                            <div className="text-xs text-gray-400">ID: {order.id}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-white">{order.customerName}</div>
                            <div className="text-sm text-gray-400">{order.customerEmail}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            {order.items.map((item, index) => (
                              <div key={index} className="text-sm text-gray-300">
                                {item.productName} × {item.quantity}
                              </div>
                            ))}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-lg font-semibold text-white">¥{order.totalAmount.toFixed(2)}</span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(order.status)}`}>
                            {getStatusText(order.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {new Date(order.createTime).toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => {
                                setSelectedOrder(order);
                                setShowDetailModal(true);
                              }}
                              className="text-blue-400 hover:text-blue-300 transition-colors"
                            >
                              详情
                            </button>
                            {order.status === 'PAID' && (
                              <button
                                onClick={() => handleUpdateStatus(order.id, 'PROCESSING')}
                                className="text-orange-400 hover:text-orange-300 transition-colors"
                              >
                                处理
                              </button>
                            )}
                            {order.status === 'PROCESSING' && (
                              <button
                                onClick={() => handleUpdateStatus(order.id, 'SHIPPED')}
                                className="text-purple-400 hover:text-purple-300 transition-colors"
                              >
                                发货
                              </button>
                            )}
                            {order.status === 'SHIPPED' && (
                              <button
                                onClick={() => handleUpdateStatus(order.id, 'DELIVERED')}
                                className="text-green-400 hover:text-green-300 transition-colors"
                              >
                                确认送达
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </main>
      </div>

      {/* 订单详情模态框 */}
      {showDetailModal && selectedOrder && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-2xl p-8 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-white">订单详情 - {selectedOrder.orderId}</h3>
              <button
                onClick={() => {
                  setShowDetailModal(false);
                  setSelectedOrder(null);
                }}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 客户信息 */}
              <div className="bg-gray-700 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">客户信息</h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm text-gray-400">客户姓名</label>
                    <p className="text-white font-medium">{selectedOrder.customerName}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">联系邮箱</label>
                    <p className="text-white font-medium">{selectedOrder.customerEmail}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">收货地址</label>
                    <p className="text-white font-medium">{selectedOrder.shippingAddress}</p>
                  </div>
                </div>
              </div>

              {/* 订单信息 */}
              <div className="bg-gray-700 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">订单信息</h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm text-gray-400">订单状态</label>
                    <p className="text-white font-medium">
                      <span className={`inline-flex px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(selectedOrder.status)}`}>
                        {getStatusText(selectedOrder.status)}
                      </span>
                    </p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">支付状态</label>
                    <p className="text-white font-medium">{selectedOrder.paymentStatus === 'PAID' ? '已支付' : '未支付'}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">下单时间</label>
                    <p className="text-white font-medium">{new Date(selectedOrder.createTime).toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">最后更新</label>
                    <p className="text-white font-medium">{new Date(selectedOrder.updateTime).toLocaleString()}</p>
                  </div>
                </div>
              </div>

              {/* 商品列表 */}
              <div className="lg:col-span-2 bg-gray-700 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">商品清单</h4>
                <div className="space-y-3">
                  {selectedOrder.items.map((item, index) => (
                    <div key={index} className="flex justify-between items-center bg-gray-600 rounded-lg p-4">
                      <div>
                        <h5 className="text-white font-medium">{item.productName}</h5>
                        <p className="text-gray-400 text-sm">单价: ¥{item.price.toFixed(2)} × {item.quantity}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-white font-semibold">¥{item.totalPrice.toFixed(2)}</p>
                      </div>
                    </div>
                  ))}
                  <div className="border-t border-gray-600 pt-4">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-semibold text-white">订单总额</span>
                      <span className="text-2xl font-bold text-white">¥{selectedOrder.totalAmount.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 备注信息 */}
              {selectedOrder.notes && (
                <div className="lg:col-span-2 bg-gray-700 rounded-xl p-6">
                  <h4 className="text-lg font-semibold text-white mb-4">订单备注</h4>
                  <p className="text-gray-300">{selectedOrder.notes}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 