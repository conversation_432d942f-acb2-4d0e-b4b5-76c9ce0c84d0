'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useTranslations } from '@/contexts/LocaleContext';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'assistant' | 'agent';
  timestamp: Date;
  type?: 'text' | 'image' | 'link';
}

interface QuickOption {
  id: string;
  text: string;
  response: string;
}

export default function CustomerService() {
  const t = useTranslations();
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [isConnectedToAgent, setIsConnectedToAgent] = useState(false);
  const [isWaitingForAgent, setIsWaitingForAgent] = useState(false);
  const [showQuickOptions, setShowQuickOptions] = useState(true);
  const [agentName, setAgentName] = useState('');
  const [connectionId, setConnectionId] = useState<string | null>(null);
  const [lastActivity, setLastActivity] = useState(Date.now());
  const [isPolling, setIsPolling] = useState(false);
  const [lastMessageCount, setLastMessageCount] = useState(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const disconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const API_BASE_URL = 'http://localhost:8080/api';

  // 快捷咨询选项
  const quickOptions: QuickOption[] = useMemo(() => [
    {
      id: 'logistics',
      text: '物流问题',
      response: '省内2-3天到货，省外3-4天到货，具体详情请查看物流信息。'
    },
    {
      id: 'product',
      text: '产品问题',
      response: '产品问题请联系店铺商家，如有异议，请联系商家或者联系人工客服。'
    },
    {
      id: 'aftersale',
      text: '售后服务',
      response: '质量问题或其他售后问题，请联系人工客服。'
    }
  ], []);

  // 快捷功能选项
  const quickActions = [
    { id: 'design', text: '设计咨询', icon: '🎨' },
    { id: 'return', text: '退换货', icon: '↩️' },
    { id: 'custom', text: '定制服务', icon: '⚙️' },
    { id: 'technical', text: '技术支持', icon: '🔧' }
  ];

  // 初始化客服会话
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        content: '您好，小灵助手为您服务，请问有什么需要帮助？',
        sender: 'assistant',
        timestamp: new Date(),
        type: 'text'
      };
      setMessages([welcomeMessage]);
      setShowQuickOptions(true);
    }
  }, [isOpen, messages.length]);

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  useEffect(() => {
    if (messages.length > lastMessageCount) {
      scrollToBottom();
      setLastMessageCount(messages.length);
    }
  }, [messages.length, lastMessageCount, scrollToBottom]);

  // 更新活动时间
  const updateActivity = useCallback(() => {
    setLastActivity(Date.now());
  }, []);

  // 检查新消息的函数
  const checkMessages = useCallback(async () => {
    if (!connectionId || isPolling) return;
    
    setIsPolling(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/customer-service/sessions/${connectionId}/messages`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        
        if (result.code === 200 && result.data && result.data.length > 0) {
          const allMessages = result.data.map((msg: any) => ({
            id: msg.messageId,
            content: msg.content,
            sender: msg.senderType === 'USER' ? 'user' : 
                   msg.senderType === 'AGENT' ? 'agent' : 'assistant',
            timestamp: new Date(msg.timestamp),
            type: 'text' as const
          }));
          
          const timeoutMessage = result.data.find((msg: any) => 
            msg.senderType === 'SYSTEM' && 
            msg.content.includes('长时间没有回应') && 
            msg.content.includes('人工客服服务已结束')
          );
          
          setMessages(prevMessages => {
            if (prevMessages.length !== allMessages.length || 
                JSON.stringify(prevMessages) !== JSON.stringify(allMessages)) {
              return allMessages;
            }
            return prevMessages;
          });
          
          if (timeoutMessage) {
            setTimeout(() => {
              setIsConnectedToAgent(false);
              setConnectionId(null);
              setShowQuickOptions(true);
              if (disconnectTimeoutRef.current) {
                clearTimeout(disconnectTimeoutRef.current);
              }
            }, 3000);
          }
        }
      }
    } catch (error) {
      console.error('检查新消息失败:', error);
    } finally {
      setIsPolling(false);
    }
  }, [connectionId, isPolling, API_BASE_URL]);

  useEffect(() => {
    if (isConnectedToAgent && connectionId) {
      checkMessages();
      pollingIntervalRef.current = setInterval(checkMessages, 2000);
    }

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [isConnectedToAgent, connectionId, checkMessages]);

  // 发送消息 - 修复版本
  const sendMessage = useCallback(async () => {
    if (!currentMessage.trim()) return;

    const messageContent = currentMessage;
    setCurrentMessage('');
    updateActivity();

    if (isConnectedToAgent && connectionId) {
      try {
        const token = localStorage.getItem('token');
        
        const response = await fetch(`${API_BASE_URL}/customer-service/send-message`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            connectionId,
            content: messageContent,
            type: 'text'
          })
        });

        const result = await response.json();
        
        if (result.code === 200) {
          // 发送成功后，触发一次消息检查
          setTimeout(() => {
            checkMessages();
          }, 200);
        } else {
          console.error('发送消息失败:', result.message);
          const errorMessage: Message = {
            id: Date.now().toString(),
            content: '消息发送失败，请重试',
            sender: 'assistant',
            timestamp: new Date(),
            type: 'text'
          };
          setMessages(prev => [...prev, errorMessage]);
        }
      } catch (error) {
        console.error('发送消息失败:', error);
        const errorMessage: Message = {
          id: Date.now().toString(),
          content: '网络错误，消息发送失败',
          sender: 'assistant',
          timestamp: new Date(),
          type: 'text'
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } else {
      // 如果没有连接客服，添加本地消息
      const userMessage: Message = {
        id: Date.now().toString(),
        content: messageContent,
        sender: 'user',
        timestamp: new Date(),
        type: 'text'
      };
      setMessages(prev => [...prev, userMessage]);
    }
  }, [currentMessage, isConnectedToAgent, connectionId, updateActivity, checkMessages, API_BASE_URL]);

  // 处理快捷选项点击
  const handleQuickOptionClick = useCallback((option: QuickOption) => {
    const userMessage: Message = {
      id: Date.now().toString(),
      content: option.text,
      sender: 'user',
      timestamp: new Date(),
      type: 'text'
    };

    const systemMessage: Message = {
      id: (Date.now() + 1).toString(),
      content: option.response,
      sender: 'assistant',
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, userMessage, systemMessage]);
    setShowQuickOptions(false);
    updateActivity();
  }, [updateActivity]);

  // 连接人工客服
  const connectToAgent = async () => {
    setIsWaitingForAgent(true);
    setShowQuickOptions(false);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('请先登录');
      }

      const response = await fetch(`${API_BASE_URL}/customer-service/request-agent`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
      });

      if (!response.ok) {
        throw new Error('连接客服失败');
      }

      const result = await response.json();
      console.log('客服连接响应:', result);
      
      if (result.code === 200 && result.data) {
        setConnectionId(result.data.connectionId);
        
        if (result.data.status === 'CONNECTED') {
          setIsWaitingForAgent(false);
          setIsConnectedToAgent(true);
          setAgentName(result.data.agentName || '在线客服');
          
          const agentMessage: Message = {
            id: Date.now().toString(),
            content: `您好，我是${result.data.agentName || '在线客服'}，很高兴为您服务！请问有什么可以帮助您的？`,
            sender: 'agent',
            timestamp: new Date(),
            type: 'text'
          };
          setMessages(prev => [...prev, agentMessage]);
        } else if (result.data.status === 'WAITING') {
          const waitingMessage: Message = {
            id: Date.now().toString(),
            content: '正在为您接入人工客服，请稍等...',
            sender: 'assistant',
            timestamp: new Date(),
            type: 'text'
          };
          setMessages(prev => [...prev, waitingMessage]);
          
          // 轮询检查连接状态
          const checkConnection = setInterval(async () => {
            try {
              const statusResponse = await fetch(`${API_BASE_URL}/customer-service/sessions/${result.data.sessionId}/messages`, {
                headers: {
                  'Authorization': `Bearer ${token}`
                }
              });
              
              if (statusResponse.ok) {
                const statusResult = await statusResponse.json();
                if (statusResult.code === 200 && statusResult.data.length > 0) {
                  // 检查是否有客服消息
                  const agentMessages = statusResult.data.filter((msg: any) => msg.senderType === 'AGENT');
                  if (agentMessages.length > 0) {
                    clearInterval(checkConnection);
                    setIsWaitingForAgent(false);
                    setIsConnectedToAgent(true);
                    setAgentName(agentMessages[0].senderName || '在线客服');
                    
                    // 添加客服消息
                    const newMessages = agentMessages.map((msg: any) => ({
                      id: msg.messageId,
                      content: msg.content,
                      sender: 'agent' as const,
                      timestamp: new Date(msg.timestamp),
                      type: 'text' as const
                    }));
                    setMessages(prev => [...prev, ...newMessages]);
                  }
                }
              }
            } catch (error) {
              console.error('检查连接状态失败:', error);
            }
          }, 2000);
          
          // 30秒后停止轮询
          setTimeout(() => {
            clearInterval(checkConnection);
            if (isWaitingForAgent) {
              setIsWaitingForAgent(false);
              const timeoutMessage: Message = {
                id: Date.now().toString(),
                content: '暂时没有客服在线，请稍后再试。您也可以继续使用智能助手服务。',
                sender: 'assistant',
                timestamp: new Date(),
                type: 'text'
              };
              setMessages(prev => [...prev, timeoutMessage]);
              setShowQuickOptions(true);
            }
          }, 30000);
        } else {
          throw new Error(result.message || '连接客服失败');
        }
      }
    } catch (error) {
      console.error('连接客服失败:', error);
      setIsWaitingForAgent(false);
      const errorMessage: Message = {
        id: Date.now().toString(),
        content: '连接客服失败，请稍后重试',
        sender: 'assistant',
        timestamp: new Date(),
        type: 'text'
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  // 关闭客服
  const closeService = () => {
    setIsOpen(false);
    setMessages([]);
    setIsConnectedToAgent(false);
    setIsWaitingForAgent(false);
    setShowQuickOptions(true);
    setConnectionId(null);
    
    if (disconnectTimeoutRef.current) {
      clearTimeout(disconnectTimeoutRef.current);
    }
  };

  // 格式化时间
  const formatTime = useMemo(() => {
    return (timestamp: Date) => {
      const now = new Date();
      const time = new Date(timestamp);
      const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
      
      if (diffInMinutes < 1) {
        return '刚刚';
      } else if (diffInMinutes < 60) {
        return `${diffInMinutes}分钟前`;
      } else if (diffInMinutes < 1440) {
        return `${Math.floor(diffInMinutes / 60)}小时前`;
      } else {
        return time.toLocaleString('zh-CN', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
    };
  }, []);

  // 处理键盘事件
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  }, [sendMessage]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 如果聊天窗口打开，显示全屏聊天界面 */}
      {isOpen ? (
        <div className="h-screen flex flex-col bg-white">
          {/* 顶部客服信息栏 */}
          <div className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">客</span>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">
                  {isConnectedToAgent ? agentName || '京东客服' : '智能客服'}
                </h3>
                <p className="text-xs text-gray-500">
                  {isConnectedToAgent ? '人工服务' : '智能客服为您服务'}
                </p>
            </div>
            </div>
            <div className="text-xs text-gray-500">
              {isConnectedToAgent ? '在线' : '24小时服务'}
          </div>
        </div>

          {/* 聊天消息区域 */}
          <div className="flex-1 overflow-y-auto bg-gray-50 px-4 py-4">
            <div className="max-w-3xl mx-auto space-y-4">
              {/* 欢迎消息 */}
              <div className="text-center">
                <div className="inline-block bg-white rounded-lg px-4 py-2 shadow-sm">
                  <p className="text-sm text-gray-600">
                    🎨 欢迎来到键帽设计器客服中心，我是您的专属客服助手，请问有什么可以帮您的？😊
                  </p>
                </div>
              </div>

              {/* 消息列表 */}
              {messages.map((message, index) => (
                <div key={message.id} className="flex items-start space-x-3">
                  {message.sender !== 'user' && (
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                      message.sender === 'agent' ? 'bg-green-500' : 'bg-red-500'
                    }`}>
                      <span className="text-white text-xs">
                        {message.sender === 'agent' ? '人' : '客'}
                      </span>
                  </div>
                )}
                  <div className={`flex-1 ${message.sender === 'user' ? 'flex justify-end' : ''}`}>
                    <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      message.sender === 'user' 
                        ? 'bg-blue-500 text-white' 
                        : message.sender === 'agent'
                        ? 'bg-green-100 text-gray-900 shadow-sm'
                        : message.content.includes('长时间没有回应') && message.content.includes('人工客服服务已结束')
                        ? 'bg-yellow-100 text-yellow-800 border border-yellow-300'
                        : 'bg-white text-gray-900 shadow-sm'
                    }`}>
                      <p className="text-sm">{message.content}</p>
                      <p className={`text-xs mt-1 ${
                        message.sender === 'user' ? 'text-blue-100' : 
                        message.content.includes('长时间没有回应') ? 'text-yellow-600' :
                        'text-gray-500'
                      }`}>
                        {formatTime(message.timestamp)}
                      </p>
          </div>
                  </div>
                  {message.sender === 'user' && (
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-xs">我</span>
                  </div>
                  )}
                </div>
              ))}

              {/* 常见问题快捷选项 - 只在智能客服模式下显示 */}
              {!isConnectedToAgent && showQuickOptions && (
                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">💡 常见问题</h4>
                  <div className="space-y-2">
                    {quickOptions.map((option) => (
                      <button
                        key={option.id}
                        onClick={() => handleQuickOptionClick(option)}
                        className="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-sm text-gray-700"
                      >
                        {option.text}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* 用户最近订单信息 */}
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h4 className="text-sm font-medium text-gray-900 mb-3">📦 最近订单</h4>
                <div className="text-center text-gray-500 text-sm py-4">
                  <p>暂无订单信息</p>
                  <p className="text-xs mt-1">完成订单后将在此处显示</p>
                </div>
              </div>



              <div ref={messagesEndRef} />
            </div>
          </div>

          {/* 快捷选项和人工客服按钮 - 只在智能客服模式下显示 */}
                {!isConnectedToAgent && !isWaitingForAgent && (
            <div className="bg-white border-t border-gray-200 p-4">
              <div className="max-w-3xl mx-auto space-y-2">
                    {/* 快捷选项 */}
                    {showQuickOptions && (
                      <>
                    <div className="text-center text-gray-500 text-xs mb-3">
                          请选择您需要咨询的问题：
                        </div>
                        <div className="max-h-32 overflow-y-auto space-y-2 mb-3">
                          {quickOptions.map((option, index) => (
                            <button
                              key={option.id}
                              onClick={() => handleQuickOptionClick(option)}
                          className="w-full p-2 text-left bg-gray-100 rounded-lg border border-gray-200 text-gray-700 hover:bg-gray-200 transition-colors text-sm"
                            >
                              {option.text}
                            </button>
                          ))}
                        </div>
                      </>
                    )}
                    
                {/* 人工客服按钮 */}
                    <div className="flex space-x-2">
                      <button
                        onClick={connectToAgent}
                    className="flex-1 p-3 text-center bg-green-500 rounded-lg text-white font-semibold hover:bg-green-600 transition-colors text-sm"
                      >
                        💼 人工客服
                      </button>
                      {!showQuickOptions && (
                        <button
                          onClick={() => setShowQuickOptions(true)}
                      className="px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm"
                        >
                          ⚡ 快捷咨询
                        </button>
                      )}
                </div>
                    </div>
                  </div>
                )}

                {/* 等待客服连接 */}
                {isWaitingForAgent && (
            <div className="bg-white border-t border-gray-200 p-4">
              <div className="max-w-3xl mx-auto text-center">
                <div className="inline-flex items-center space-x-2 text-blue-500">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                      <span className="text-sm">正在连接人工客服...</span>
                </div>
                    </div>
                  </div>
                )}

          {/* 底部输入区域 */}
          <div className="bg-white border-t border-gray-200 p-4">
            <div className="max-w-3xl mx-auto">


              {/* 输入框 */}
              <div className="flex items-center space-x-3">
                <div className="flex-1 flex items-center space-x-2 bg-gray-100 rounded-lg px-3 py-2">
                    <input
                      ref={inputRef}
                      type="text"
                      value={currentMessage}
                      onChange={(e) => setCurrentMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder={isConnectedToAgent ? '输入消息与客服交流...' : '输入消息...'}
                    className="flex-1 bg-transparent border-none outline-none text-gray-900 placeholder-gray-500"
                      disabled={isWaitingForAgent}
                    />
                  <button className="p-1 hover:bg-gray-200 rounded">
                    <span className="text-gray-500">😊</span>
                  </button>
                </div>
                    <button
                      onClick={sendMessage}
                      disabled={!currentMessage.trim() || isWaitingForAgent}
                  className="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                    >
                      发送
                    </button>
                  </div>
                  
              {/* 底部功能按钮 */}
              <div className="flex justify-between items-center mt-3">
                    <div className="flex space-x-2">
                      {!isConnectedToAgent && !isWaitingForAgent && (
                        <>
                          <button
                            onClick={connectToAgent}
                            className="px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors font-medium"
                          >
                            💼 转人工
                          </button>
                          <button
                            onClick={() => setShowQuickOptions(!showQuickOptions)}
                            className="px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors"
                          >
                            {showQuickOptions ? '隐藏选项' : '快捷咨询'}
                          </button>
                        </>
                      )}
                    </div>
                    <button
                      onClick={closeService}
                      className="px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600 transition-colors"
                    >
                      关闭
                    </button>
                  </div>
                </div>
              </div>
            </div>
      ) : (
        /* 客服入口页面 */
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* 页面标题 */}
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">客服中心</h1>
              <p className="text-gray-600">专业的客服团队为您提供贴心服务</p>
        </div>

            {/* 客服选项卡片 */}
            <div className="grid md:grid-cols-2 gap-6 mb-8">
              {/* 智能客服 */}
              <div 
                onClick={() => setIsOpen(true)}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md transition-shadow"
              >
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-lg">🤖</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">智能客服</h3>
                    <p className="text-sm text-blue-600">24小时在线</p>
                  </div>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  快速响应常见问题，提供专业的自助服务
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-blue-600 text-sm font-medium">点击开始对话</span>
                  <span className="text-blue-600">→</span>
                </div>
              </div>

              {/* 人工客服 */}
              <div 
                onClick={() => {
                  setIsOpen(true);
                  setTimeout(() => connectToAgent(), 500);
                }}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer hover:shadow-md transition-shadow"
              >
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-lg">👨‍💼</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">人工客服</h3>
                    <p className="text-sm text-green-600">实时响应</p>
                  </div>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  专业客服团队，一对一个性化服务
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-green-600 text-sm font-medium">点击连接客服</span>
                  <span className="text-green-600">→</span>
                </div>
              </div>
            </div>

            {/* 快捷服务 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">快捷服务</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {quickActions.map((action) => (
                  <button
                    key={action.id}
                    onClick={() => {
                      setIsOpen(true);
                      // 可以在这里添加对应的快捷服务逻辑
                    }}
                    className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <span className="text-2xl mb-2">{action.icon}</span>
                    <span className="text-sm text-gray-700">{action.text}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 