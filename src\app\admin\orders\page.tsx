'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdmin } from '../../../contexts/AdminContext';
import Link from 'next/link';

// 订单类型定义
interface Order {
  id: string;
  customer: string;
  customerEmail: string;
  phone: string;
  products: string[];
  amount: number;
  status: string;
  paymentStatus: string;
  shippingInfo: {
    address: string;
    courier: string;
    trackingNumber: string;
  };
  orderTime: string;
  paymentTime: string | null;
  shippingTime: string | null;
}

// 订单接口数据类型
interface ApiOrderData {
  orderId: string;
  username: string;
  nickname: string;
  email: string;
  phone: string;
  productName: string;
  productImage: string;
  totalAmount: number;
  shippingFee: number;
  actualAmount: number;
  status: string;
  statusText: string;
  receiverName: string;
  receiverPhone: string;
  receiverAddress: string;
  createTime: string;
  paymentTime: string | null;
  updateTime: string;
  cancelReason: string | null;
}

interface ApiResponse {
  code: number;
  message: string;
  data: {
    pageInfo: {
      currentPage: number;
      totalPages: number;
      totalElements: number;
      pageSize: number;
    };
    orders: ApiOrderData[];
    stats: {
      totalOrders: number;
      pendingPayment: number;
      pendingShipment: number;
      completed: number;
      cancelled: number;
      refunded: number;
    };
  };
  timestamp: number;
}

const statusOptions = ['全部', '待支付', '已支付', '待发货', '已发货', '已完成', '已取消'];

// 状态映射
const statusMapping: { [key: string]: string } = {
  '全部': '',
  '待支付': 'PENDING_PAYMENT',
  '已支付': 'PAID',
  '待发货': 'PENDING_SHIPMENT',
  '已发货': 'SHIPPED',
  '已完成': 'COMPLETED',
  '已取消': 'CANCELLED'
};


export default function AdminOrders() {
  const router = useRouter();
  const { admin, isLoading: adminLoading } = useAdmin();
  const [orders, setOrders] = useState<Order[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('全部');

  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [showShippingModal, setShowShippingModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const [stats, setStats] = useState({
    totalOrders: 0,
    pendingPayment: 0,
    pendingShipment: 0,
    completed: 0,
    cancelled: 0,
    refunded: 0
  });

  useEffect(() => {
    if (!adminLoading) {
      if (admin) {
        loadOrders();
      } else {
      router.push('/admin/login');
    }
    }
  }, [admin, adminLoading, router, currentPage, statusFilter, searchTerm]);

  // 加载订单数据
  const loadOrders = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        page: currentPage.toString(),
        size: '20'
      });
      
      if (statusFilter && statusFilter !== '全部') {
        const mappedStatus = statusMapping[statusFilter];
        if (mappedStatus) {
          params.append('status', mappedStatus);
        }
      }
      
      if (searchTerm && searchTerm.trim()) {
        params.append('search', searchTerm.trim());
      }
      
      const response = await fetch(`http://localhost:8080/api/admin/orders?${params}`);
      const result: ApiResponse = await response.json();
      
      if (result.code === 200) {
        // 转换API数据为组件所需格式
        const convertedOrders: Order[] = result.data.orders.map(apiOrder => ({
          id: apiOrder.orderId,
          customer: apiOrder.username,
          customerEmail: apiOrder.email,
          phone: apiOrder.receiverPhone,
          products: [apiOrder.productName],
          amount: apiOrder.actualAmount,
          status: apiOrder.statusText,
          paymentStatus: apiOrder.status === 'PENDING_PAYMENT' ? '未支付' : '已支付',
          shippingInfo: {
            address: apiOrder.receiverAddress,
            courier: '',
            trackingNumber: ''
          },
          orderTime: new Date(apiOrder.createTime).toLocaleString('zh-CN'),
          paymentTime: apiOrder.paymentTime ? new Date(apiOrder.paymentTime).toLocaleString('zh-CN') : null,
          shippingTime: null
        }));
        
        setOrders(convertedOrders);
        setTotalPages(result.data.pageInfo.totalPages);
        setTotalElements(result.data.pageInfo.totalElements);
        setStats(result.data.stats);
      } else {
        setError(result.message || '获取订单数据失败');
      }
    } catch (err) {
      console.error('加载订单失败:', err);
      setError('网络错误，请检查后端服务是否正常运行');
    } finally {
      setLoading(false);
    }
  };

  // 筛选逻辑现在在后端API中处理

  const handleOrderAction = (orderId: string, action: string) => {
    setOrders(prev => prev.map(order => {
      if (order.id === orderId) {
        switch (action) {
          case 'ship':
            return { 
              ...order, 
              status: '已发货',
              shippingTime: new Date().toLocaleString('zh-CN') as string
            };
          case 'complete':
            return { ...order, status: '已完成' };
          case 'cancel':
            return { ...order, status: '已取消' };
          default:
            return order;
        }
      }
      return order;
    }));
  };

  const handleUpdateShipping = (orderId: string, shippingInfo: any) => {
    setOrders(prev => prev.map(order => {
      if (order.id === orderId) {
        return {
          ...order,
          shippingInfo: { ...order.shippingInfo, ...shippingInfo },
          status: '已发货',
          shippingTime: new Date().toLocaleString('zh-CN')
        };
      }
      return order;
    }));
  };

  const totalOrders = stats.totalOrders;
  const pendingPayment = stats.pendingPayment;
  const pendingShipment = stats.pendingShipment;
  const completedOrders = stats.completed;

  if (adminLoading) {
    return <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
    </div>;
  }

  if (!admin) {
    return null; // 会被useEffect重定向到登录页
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* 顶部导航栏 */}
      <nav className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-600 rounded-lg flex items-center justify-center text-white font-bold text-sm mr-3">
                管
              </Link>
              <h1 className="text-xl font-bold text-white">订单管理</h1>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* 侧边栏 */}
        <aside className="w-64 bg-gray-800 min-h-screen pt-4">
          <nav className="px-4 space-y-2">
            <Link href="/admin/dashboard" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📊</span>
              仪表盘
            </Link>
            <Link href="/admin/users" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">👥</span>
              用户管理
            </Link>
            <Link href="/admin/products" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📦</span>
              商品管理
            </Link>
            <Link href="/admin/orders" className="flex items-center px-4 py-2 text-white bg-red-600 rounded-lg">
              <span className="mr-3">🛒</span>
              订单管理
            </Link>
            <Link href="/admin/customer-service" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">💬</span>
              客服中心
            </Link>
            <Link href="/admin/stores" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🏪</span>
              店铺管理
            </Link>
            <Link href="/admin/logistics" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🚚</span>
              物流管理
            </Link>
            <Link href="/admin/settings" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">⚙️</span>
              系统设置
            </Link>
          </nav>
        </aside>

        {/* 主要内容区 */}
        <main className="flex-1 p-6">
          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">总订单数</p>
                  <p className="text-2xl font-bold text-white">{totalOrders}</p>
                </div>
                <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-blue-400 text-2xl">📋</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">待付款</p>
                  <p className="text-2xl font-bold text-white">{pendingPayment}</p>
                </div>
                <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-yellow-400 text-2xl">💰</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">待发货</p>
                  <p className="text-2xl font-bold text-white">{pendingShipment}</p>
                </div>
                <div className="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-orange-400 text-2xl">📦</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">已完成</p>
                  <p className="text-2xl font-bold text-white">{completedOrders}</p>
                </div>
                <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-green-400 text-2xl">✅</span>
                </div>
              </div>
            </div>
          </div>

          {/* 搜索和过滤 */}
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1 flex gap-2">
                <input
                  type="text"
                  placeholder="搜索订单号、客户姓名、邮箱或商品名称..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      setCurrentPage(0);
                      loadOrders();
                    }
                  }}
                  className="flex-1 px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500"
                />
                <button
                  onClick={() => {
                    setCurrentPage(0);
                    loadOrders();
                  }}
                  className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                >
                  搜索
                </button>
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('全部');

                    setCurrentPage(0);
                    loadOrders();
                  }}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  重置
                </button>
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                {statusOptions.map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>

            </div>
          </div>

          {/* 订单列表 */}
          <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
            {error && (
              <div className="p-4 bg-red-500/20 border-l-4 border-red-500">
                <p className="text-red-400">{error}</p>
                <button 
                  onClick={loadOrders}
                  className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                >
                  重试
                </button>
              </div>
            )}
            
            {loading ? (
              <div className="p-8 text-center">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
                <p className="mt-2 text-gray-400">加载中...</p>
              </div>
            ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">订单号</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">客户</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">商品</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">金额</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">订单状态</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">支付状态</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">下单时间</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                    {orders.length === 0 ? (
                      <tr>
                        <td colSpan={8} className="px-6 py-8 text-center text-gray-400">
                          暂无订单数据
                        </td>
                      </tr>
                    ) : (
                      orders.map((order) => (
                    <tr key={order.id} className="hover:bg-gray-700/50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-white font-medium">{order.id}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-white">{order.customer}</div>
                        <div className="text-gray-400 text-sm">{order.customerEmail}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-gray-300">
                          {order.products.join(', ')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-white font-medium">¥{order.amount}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          order.status === '待付款' ? 'bg-red-500/20 text-red-400' :
                          order.status === '待发货' ? 'bg-yellow-500/20 text-yellow-400' :
                          order.status === '已发货' ? 'bg-blue-500/20 text-blue-400' :
                          order.status === '已完成' ? 'bg-green-500/20 text-green-400' :
                          'bg-gray-500/20 text-gray-400'
                        }`}>
                          {order.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          order.paymentStatus === '未支付' ? 'bg-red-500/20 text-red-400' :
                          order.paymentStatus === '已支付' ? 'bg-green-500/20 text-green-400' :
                          'bg-gray-500/20 text-gray-400'
                        }`}>
                          {order.paymentStatus}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-300">
                        {order.orderTime}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              setSelectedOrder(order);
                              setShowOrderModal(true);
                            }}
                            className="text-blue-400 hover:text-blue-300 text-sm"
                          >
                            详情
                          </button>
                          {order.status === '待发货' && (
                            <button
                              onClick={() => {
                                setSelectedOrder(order);
                                setShowShippingModal(true);
                              }}
                              className="text-green-400 hover:text-green-300 text-sm"
                            >
                              发货
                            </button>
                          )}
                          {order.status === '已发货' && (
                            <button
                              onClick={() => handleOrderAction(order.id, 'complete')}
                              className="text-purple-400 hover:text-purple-300 text-sm"
                            >
                              完成
                            </button>
                          )}
                          {(order.status === '待付款' || order.status === '待发货') && (
                            <button
                              onClick={() => handleOrderAction(order.id, 'cancel')}
                              className="text-red-400 hover:text-red-300 text-sm"
                            >
                              取消
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                      ))
                    )}
                </tbody>
              </table>
            </div>
            )}
          </div>

          {/* 分页控件 */}
          {!loading && !error && totalPages > 1 && (
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 mt-6">
              <div className="flex items-center justify-between">
                <div className="text-gray-400">
                  共 {totalElements} 条记录，第 {currentPage + 1} 页，共 {totalPages} 页
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                    disabled={currentPage === 0}
                    className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    上一页
                  </button>
                  
                  {/* 页码按钮 */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i;
                    } else if (currentPage < 3) {
                      pageNum = i;
                    } else if (currentPage > totalPages - 3) {
                      pageNum = totalPages - 5 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }
                    
                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-3 py-2 rounded ${
                          currentPage === pageNum
                            ? 'bg-red-500 text-white'
                            : 'bg-gray-700 text-white hover:bg-gray-600'
                        }`}
                      >
                        {pageNum + 1}
                      </button>
                    );
                  })}
                  
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
                    disabled={currentPage >= totalPages - 1}
                    className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    下一页
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 订单详情模态框 */}
          {showOrderModal && selectedOrder && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-gray-800 rounded-lg p-6 w-full max-w-3xl border border-gray-700 max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-white">订单详情 - {selectedOrder.id}</h3>
                  <button
                    onClick={() => setShowOrderModal(false)}
                    className="text-gray-400 hover:text-white"
                  >
                    ✕
                  </button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* 基本信息 */}
                  <div className="space-y-4">
                    <h4 className="text-white font-medium">基本信息</h4>
                    <div className="space-y-2">
                      <div>
                        <span className="text-gray-400">订单号：</span>
                        <span className="text-white">{selectedOrder.id}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">客户姓名：</span>
                        <span className="text-white">{selectedOrder.customer}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">联系邮箱：</span>
                        <span className="text-white">{selectedOrder.customerEmail}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">联系电话：</span>
                        <span className="text-white">{selectedOrder.phone}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">订单金额：</span>
                        <span className="text-white font-medium">¥{selectedOrder.amount}</span>
                      </div>
                    </div>
                  </div>

                  {/* 商品信息 */}
                  <div className="space-y-4">
                    <h4 className="text-white font-medium">商品信息</h4>
                    <div className="space-y-2">
                      {selectedOrder.products.map((product: string, index: number) => (
                        <div key={index} className="text-white">• {product}</div>
                      ))}
                    </div>
                  </div>

                  {/* 物流信息 */}
                  <div className="space-y-4 md:col-span-2">
                    <h4 className="text-white font-medium">物流信息</h4>
                    <div className="space-y-2">
                      <div>
                        <span className="text-gray-400">收货地址：</span>
                        <span className="text-white">{selectedOrder.shippingInfo.address}</span>
                      </div>
                      {selectedOrder.shippingInfo.courier && (
                        <div>
                          <span className="text-gray-400">快递公司：</span>
                          <span className="text-white">{selectedOrder.shippingInfo.courier}</span>
                        </div>
                      )}
                      {selectedOrder.shippingInfo.trackingNumber && (
                        <div>
                          <span className="text-gray-400">快递单号：</span>
                          <span className="text-white">{selectedOrder.shippingInfo.trackingNumber}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 时间信息 */}
                  <div className="space-y-4 md:col-span-2">
                    <h4 className="text-white font-medium">时间信息</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <span className="text-gray-400 block">下单时间</span>
                        <span className="text-white">{selectedOrder.orderTime}</span>
                      </div>
                      {selectedOrder.paymentTime && (
                        <div>
                          <span className="text-gray-400 block">支付时间</span>
                          <span className="text-white">{selectedOrder.paymentTime}</span>
                        </div>
                      )}
                      {selectedOrder.shippingTime && (
                        <div>
                          <span className="text-gray-400 block">发货时间</span>
                          <span className="text-white">{selectedOrder.shippingTime}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-end">
                  <button
                    onClick={() => setShowOrderModal(false)}
                    className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    关闭
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 发货模态框 */}
          {showShippingModal && selectedOrder && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-white">填写物流信息</h3>
                  <button
                    onClick={() => setShowShippingModal(false)}
                    className="text-gray-400 hover:text-white"
                  >
                    ✕
                  </button>
                </div>
                
                <form onSubmit={(e) => {
                  e.preventDefault();
                  const formData = new FormData(e.currentTarget);
                  const shippingInfo = {
                    courier: formData.get('courier') as string,
                    trackingNumber: formData.get('trackingNumber') as string
                  };
                  handleUpdateShipping(selectedOrder.id, shippingInfo);
                  setShowShippingModal(false);
                }}>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-gray-300 mb-2">快递公司</label>
                      <select
                        name="courier"
                        className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500"
                        required
                      >
                        <option value="">请选择快递公司</option>
                        <option value="顺丰速运">顺丰速运</option>
                        <option value="中通快递">中通快递</option>
                        <option value="圆通速递">圆通速递</option>
                        <option value="申通快递">申通快递</option>
                        <option value="韵达快递">韵达快递</option>
                        <option value="邮政EMS">邮政EMS</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-gray-300 mb-2">快递单号</label>
                      <input
                        type="text"
                        name="trackingNumber"
                        className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500"
                        placeholder="请输入快递单号"
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => setShowShippingModal(false)}
                      className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                    >
                      取消
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                    >
                      确认发货
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
} 