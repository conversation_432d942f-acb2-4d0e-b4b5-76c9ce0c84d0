'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from '@/contexts/LocaleContext';

interface ReadingNoticeProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ReadingNotice({ isOpen, onClose }: ReadingNoticeProps) {
  const [countdown, setCountdown] = useState(10);
  const t = useTranslations();

  useEffect(() => {
    if (!isOpen) return;

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          onClose();
          return 10; // 重置倒计时
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isOpen, onClose]);

  // 重置倒计时当模态框打开时
  useEffect(() => {
    if (isOpen) {
      setCountdown(10);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const features = [
    {
      icon: '🎨',
      title: t('readingNotice.features.aiDesign.title'),
      description: t('readingNotice.features.aiDesign.description')
    },
    {
      icon: '🔧',
      title: t('readingNotice.features.diyCustom.title'),
      description: t('readingNotice.features.diyCustom.description')
    },
    {
      icon: '🌈',
      title: t('readingNotice.features.unlimitedStyle.title'),
      description: t('readingNotice.features.unlimitedStyle.description')
    },
    {
      icon: '🚀',
      title: t('readingNotice.features.realtime3d.title'),
      description: t('readingNotice.features.realtime3d.description')
    }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="relative bg-gradient-to-r from-cyan-500 via-purple-500 to-pink-500 text-white p-8 rounded-t-3xl">
          <div className="absolute inset-0 bg-black/10 rounded-t-3xl"></div>
          <div className="relative z-10 text-center">
            <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center text-4xl mx-auto mb-4">
              ✨
            </div>
            <h1 className="text-4xl font-bold mb-3">
              {t('readingNotice.title')}
            </h1>
            <p className="text-xl text-white/90">
              {t('readingNotice.subtitle')}
            </p>
          </div>
          
          {/* 关闭按钮和倒计时 */}
          <button
            onClick={onClose}
            className="absolute top-6 right-6 w-10 h-10 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full flex items-center justify-center text-white transition-all duration-200"
          >
            ✕
          </button>
          <div className="absolute top-6 left-6 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 text-sm">
            {countdown}s
          </div>
        </div>

        {/* 内容区域 */}
        <div className="p-8">
          {/* 介绍部分 */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center">
              <span className="text-3xl mr-3">🎯</span>
              {t('readingNotice.aboutTitle')}
            </h2>
            <p className="text-gray-600 leading-relaxed text-lg">
              {t('readingNotice.aboutDescription')}
            </p>
          </div>

          {/* 核心优势 */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
              <span className="text-3xl mr-3">⭐</span>
              {t('readingNotice.advantagesTitle')}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <div 
                  key={index}
                  className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:scale-105"
                >
                  <div className="text-4xl mb-3">{feature.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* 自主DIY介绍 */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center">
              <span className="text-3xl mr-3">🛠️</span>
              {t('readingNotice.diyTitle')}
            </h2>
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6">
              <p className="text-gray-700 leading-relaxed text-lg mb-4">
                {t('readingNotice.diyDescription')}
              </p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[
                  { emoji: '🎨', text: t('readingNotice.diyFeatures.colors') },
                  { emoji: '📝', text: t('readingNotice.diyFeatures.text') },
                  { emoji: '🖼️', text: t('readingNotice.diyFeatures.images') },
                  { emoji: '✨', text: t('readingNotice.diyFeatures.effects') }
                ].map((item, index) => (
                  <div key={index} className="text-center">
                    <div className="text-3xl mb-2">{item.emoji}</div>
                    <p className="text-sm text-gray-600 font-medium">{item.text}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 网站风格介绍 */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center">
              <span className="text-3xl mr-3">🎭</span>
              {t('readingNotice.styleTitle')}
            </h2>
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6">
              <p className="text-gray-700 leading-relaxed text-lg">
                {t('readingNotice.styleDescription')}
              </p>
            </div>
          </div>

          {/* 创作号召 */}
          <div className="text-center bg-gradient-to-r from-cyan-500 via-purple-500 to-pink-500 rounded-3xl p-8 text-white">
            <div className="text-6xl mb-4">🚀</div>
            <h2 className="text-3xl font-bold mb-4">
              {t('readingNotice.callToAction.title')}
            </h2>
            <p className="text-xl text-white/90 mb-6">
              {t('readingNotice.callToAction.subtitle')}
            </p>
            <button
              onClick={onClose}
              className="bg-white text-purple-600 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 hover:scale-105 shadow-lg"
            >
              {t('readingNotice.callToAction.button')} ✨
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 