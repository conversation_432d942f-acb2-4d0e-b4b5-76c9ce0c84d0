'use client';

import { useState, useEffect } from 'react';
import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { 
  Locale, 
  languageNames, 
  setStoredLocale, 
  getStoredLocale,
  getCurrentLocale 
} from '@/lib/locale';

interface LanguageSwitcherProps {
  className?: string;
  onLocaleChange?: (locale: Locale) => void;
  darkMode?: boolean; // 添加深色模式支持
}

export default function LanguageSwitcher({ className = '', onLocaleChange, darkMode = false }: LanguageSwitcherProps) {
  const [currentLocale, setCurrentLocale] = useState<Locale>('zh');
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 初始化语言设置
  useEffect(() => {
    let isMounted = true;
    
    const initLocale = async () => {
      try {
        const locale = await getCurrentLocale();
        if (isMounted) {
          setCurrentLocale(locale);
        }
      } catch (error) {
        console.error('Failed to initialize locale:', error);
        if (isMounted) {
          setCurrentLocale('zh');
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    initLocale();
    
    return () => {
      isMounted = false;
    };
  }, []);

  // 处理语言切换
  const handleLocaleChange = (locale: Locale) => {
    setCurrentLocale(locale);
    setStoredLocale(locale);
    setIsOpen(false);
    
    // 触发回调
    if (onLocaleChange) {
      onLocaleChange(locale);
    }

    // 刷新页面以应用新语言
    window.location.reload();
  };

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.language-switcher')) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [isOpen]);

  if (isLoading) {
    return (
      <div className={`language-switcher ${className}`}>
        <div className="px-3 py-2 text-sm">
          <div className={`w-16 h-4 rounded animate-pulse ${
            darkMode ? 'bg-gray-700' : 'bg-gray-200'
          }`}></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`language-switcher relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center space-x-2 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
          darkMode 
            ? 'text-gray-300 hover:text-white hover:bg-gray-700/50' 
            : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
        }`}
        aria-label="切换语言"
      >
        {/* 语言图标 */}
        <svg
          className="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
          />
        </svg>
        
        {/* 当前语言 */}
        <span>{languageNames[currentLocale]}</span>
        
        {/* 下拉箭头 */}
        <ChevronDownIcon 
          className={`w-4 h-4 transition-transform duration-200 ${
            isOpen ? 'transform rotate-180' : ''
          }`} 
        />
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className={`absolute right-0 mt-2 w-40 rounded-lg shadow-lg py-1 z-50 ${
          darkMode 
            ? 'bg-gray-800 border border-gray-700' 
            : 'bg-white border border-gray-200'
        }`}>
          {Object.entries(languageNames).map(([locale, name]) => (
            <button
              key={locale}
              onClick={() => handleLocaleChange(locale as Locale)}
              className={`w-full text-left px-4 py-2 text-sm transition-colors duration-200 ${
                darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
              } ${
                currentLocale === locale 
                  ? darkMode 
                    ? 'text-blue-400 bg-blue-900/50 font-medium' 
                    : 'text-blue-600 bg-blue-50 font-medium'
                  : darkMode 
                    ? 'text-gray-300' 
                    : 'text-gray-700'
              }`}
            >
              <div className="flex items-center justify-between">
                <span>{name}</span>
                {currentLocale === locale && (
                  <svg
                    className={`w-4 h-4 ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
} 