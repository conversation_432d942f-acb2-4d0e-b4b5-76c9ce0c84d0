'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api';

  // 辅助函数：生成完整的图片URL
function getFullImageUrl(path: string): string {
  // 如果路径为空，返回默认图片
  if (!path) return '/public/images/placeholders/image-placeholder.png';
  
  // 如果路径已经是完整URL，直接返回
  if (path.startsWith('http')) return path;

  // 去除开头多余的斜杠
  let cleanPath = path;
  while (cleanPath.startsWith('/')) {
    cleanPath = cleanPath.substring(1);
  }

  // 尝试多种可能的路径格式
  // 1. 如果路径包含uploads，则直接使用后端基地址
  if (cleanPath.includes('uploads/')) {
    const baseUrl = 'http://localhost:8080';
    console.log(`处理图片路径(uploads): ${path} -> ${baseUrl}/${cleanPath}`);
    return `${baseUrl}/${cleanPath}`;
  }
  
  // 2. 如果路径是api/uploads格式
  if (cleanPath.includes('api/uploads/')) {
    const baseUrl = 'http://localhost:8080';
    console.log(`处理图片路径(api/uploads): ${path} -> ${baseUrl}/${cleanPath}`);
    return `${baseUrl}/${cleanPath}`;
  }
  
  // 3. 如果路径是products/格式(直接指向产品目录)
  if (cleanPath.includes('products/')) {
    const baseUrl = 'http://localhost:8080/uploads';
    console.log(`处理图片路径(products): ${path} -> ${baseUrl}/${cleanPath}`);
    return `${baseUrl}/${cleanPath}`;
  }
  
  // 4. 如果是JSON字符串，尝试解析
  if (path.startsWith('[') && path.endsWith(']')) {
    try {
      const images = JSON.parse(path);
      if (Array.isArray(images) && images.length > 0) {
        return getFullImageUrl(images[0]);
      }
    } catch (e) {
      console.error('解析JSON图片路径失败:', e);
    }
  }

  // 默认情况，尝试作为相对路径处理
  const fullUrl = `http://localhost:8080/uploads/products/${cleanPath}`;
  console.log(`处理默认图片路径: ${path} -> ${fullUrl}`);
  return fullUrl;
}

interface Product {
  id: string;
  productId: string;
  name: string;
  description: string;
  price: number;
  stock: number;
  category: string;
  images: string[];
  videoUrl?: string;
  status: 'ACTIVE' | 'INACTIVE' | 'OUT_OF_STOCK';
  createTime: string;
  updateTime: string;
}

interface ProductStats {
  total: number;
  active: number;
  inactive: number;
  outOfStock: number;
}

export default function ShopProducts() {
  const router = useRouter();
  const { user } = useAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [stats, setStats] = useState<ProductStats>({ total: 0, active: 0, inactive: 0, outOfStock: 0 });
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'all' | 'active' | 'inactive' | 'outOfStock'>('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [newProduct, setNewProduct] = useState({
    name: '',
    description: '',
    price: 0,
    stock: 0,
    category: '',
    tags: [] as string[],
    specifications: {
      material: '',
      profile: '',
      keyCount: '',
      layout: '',
      compatibility: ''
    },
    images: [] as string[],
    videoUrl: ''
  });
  const [uploadedImages, setUploadedImages] = useState<{file: File, preview: string}[]>([]);
  const [uploadedVideo, setUploadedVideo] = useState<{file: File, preview: string} | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // 状态映射函数：将后端状态映射到前端状态
  const mapBackendStatusToFrontend = (backendStatus: string): Product['status'] => {
    switch (backendStatus) {
      case 'ON_SALE':
        return 'ACTIVE';
      case 'OFF_SALE':
        return 'INACTIVE';
      case 'OUT_OF_STOCK':
        return 'OUT_OF_STOCK';
      default:
        return 'INACTIVE';
    }
  };

  // 状态映射函数：将前端状态映射到后端状态
  const mapFrontendStatusToBackend = (frontendStatus: Product['status']): string => {
    switch (frontendStatus) {
      case 'ACTIVE':
        return 'ON_SALE';
      case 'INACTIVE':
        return 'OFF_SALE';
      case 'OUT_OF_STOCK':
        return 'OUT_OF_STOCK';
      default:
        return 'OFF_SALE';
    }
  };

  // 获取商品列表
  const fetchProducts = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        router.push('/login');
        return;
      }

      console.log('获取商品列表 - 发送请求...');
      const response = await fetch(`${API_BASE_URL}/shop/products`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        console.log('获取商品列表 - API响应:', result);
        
        let productData = null;
        
        // 兼容多种API响应格式
        if (result.success && result.data) {
          // 格式1: {success: true, data: [...]}
          productData = result.data;
        } else if (result.code === 200 && result.data) {
          // 格式2: {code: 200, message: "操作成功", data: [...]}
          productData = result.data;
        } else if (Array.isArray(result)) {
          // 格式3: 直接返回数组
          productData = result;
        }
        
        if (productData && Array.isArray(productData)) {
          console.log('获取商品列表 - 解析到商品数据:', productData);
          
          // 映射后端状态到前端状态
          const mappedProducts = productData.map(product => {
            // 处理图片数据
            let images = [];
            if (Array.isArray(product.images)) {
              images = product.images;
            } else if (typeof product.images === 'string') {
              try {
                // 尝试解析JSON字符串
                const parsedImages = JSON.parse(product.images);
                images = Array.isArray(parsedImages) ? parsedImages : [];
                console.log('成功解析商品图片JSON:', product.id, images);
              } catch (e) {
                console.error('解析商品图片JSON失败:', product.id, e);
                images = [];
              }
            }
            
            return {
              ...product,
              images: images,
              status: mapBackendStatusToFrontend(product.status)
            };
          });
          
          console.log('获取商品列表 - 状态映射后:', mappedProducts);
          setProducts(mappedProducts);
          setStats({
            total: mappedProducts.length,
            active: mappedProducts.filter(p => p.status === 'ACTIVE').length,
            inactive: mappedProducts.filter(p => p.status === 'INACTIVE').length,
            outOfStock: mappedProducts.filter(p => p.status === 'OUT_OF_STOCK').length
          });
        } else {
          console.warn('获取商品列表 - 未找到有效数据:', result);
        }
      } else {
        console.error('获取商品列表 - 请求失败:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('获取商品列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理文件选择
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    // 检查文件数量限制（包括现有图片）
    const currentImageCount = (newProduct.images?.length || 0) + uploadedImages.length;
    if (currentImageCount + files.length > 5) {
      alert(`最多只能上传5张图片，当前已有${currentImageCount}张`);
      return;
    }

    // 验证文件类型和大小
    const validFiles = files.filter(file => {
      if (!file.type.startsWith('image/')) {
        alert(`${file.name} 不是图片文件`);
        return false;
      }
      if (file.size > 10 * 1024 * 1024) {
        alert(`${file.name} 文件过大，请选择小于10MB的图片`);
        return false;
      }
      return true;
    });

    // 创建预览
    const newImages = validFiles.map(file => ({
      file,
      preview: URL.createObjectURL(file)
    }));

    setUploadedImages(prev => [...prev, ...newImages]);
  };

  // 删除图片
  const removeImage = (index: number) => {
    URL.revokeObjectURL(uploadedImages[index].preview);
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  // 上传图片到服务器
  const uploadImages = async (images: {file: File, preview: string}[]): Promise<string[]> => {
    const token = localStorage.getItem('token');
    if (!token) throw new Error('用户未登录');

    const uploadedPaths: string[] = [];
    
    for (let i = 0; i < images.length; i++) {
      const { file } = images[i];
      try {
        console.log(`开始上传图片: ${file.name}`);
        
        const formData = new FormData();
        formData.append('file', file);
        formData.append('uploadType', 'PRODUCT');
        
        const response = await fetch(`${API_BASE_URL}/upload`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          },
          body: formData
        });
        
        if (response.ok) {
          const result = await response.json();
          console.log(`图片 ${file.name} 上传响应:`, result);
          
          if (result.code === 200 && result.data) {
            console.log("处理图片上传响应:", result);
            let imagePath = '';
            if (typeof result.data === 'object') {
              // 尝试从返回对象中获取路径
              if (result.data.filePath) {
                imagePath = result.data.filePath;
              } else if (result.data.fileUrl) {
                imagePath = result.data.fileUrl;
              } else if (result.data.fileName) {
                // 如果只有文件名，构造路径
                const subDir = 'products';
                imagePath = `/uploads/${subDir}/${result.data.fileName}`;
              }
            } else if (typeof result.data === 'string') {
              // 如果直接返回字符串路径
              imagePath = result.data;
            }
            
            // 构造完整URL
            const fullPath = imagePath ? getFullImageUrl(imagePath) : '';

            if (imagePath) {
              // 保存原始路径，用于API通信
              uploadedPaths.push(imagePath);
              console.log(`图片 ${file.name} 上传成功，路径: ${imagePath}`);
              console.log(`图片 ${file.name} 完整URL: ${fullPath}`);
              // 更新进度
              setUploadProgress((i + 1) / images.length * 100);
            } else {
              throw new Error('服务器未返回图片路径');
            }
          } else {
            throw new Error(result.message || '图片上传失败');
          }
        } else {
          const errorResult = await response.json();
          throw new Error(errorResult.message || `图片上传失败，状态码：${response.status}`);
        }
      } catch (error) {
        console.error(`上传图片 ${file.name} 失败:`, error);
        throw new Error(`上传图片 ${file.name} 失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }
    
    console.log('所有图片上传完成，路径列表:', uploadedPaths);
    return uploadedPaths;
  };

  // 处理视频选择
  const handleVideoSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    // 只取第一个文件
    const file = files[0];

    // 验证文件类型和大小
    if (!file.type.startsWith('video/')) {
      alert(`${file.name} 不是视频文件`);
      return;
    }
    
    if (file.size > 20 * 1024 * 1024) {
      alert(`${file.name} 文件过大，请选择小于20MB的视频`);
      return;
    }

    // 清除之前的视频（如果有）
    if (uploadedVideo) {
      URL.revokeObjectURL(uploadedVideo.preview);
    }

    // 创建预览
    setUploadedVideo({
      file,
      preview: URL.createObjectURL(file)
    });
  };

  // 删除视频
  const removeVideo = () => {
    if (uploadedVideo) {
      URL.revokeObjectURL(uploadedVideo.preview);
      setUploadedVideo(null);
    }
  };

  // 上传视频到服务器
  const uploadVideo = async (videoData: {file: File, preview: string}): Promise<string> => {
    const token = localStorage.getItem('token');
    if (!token) throw new Error('用户未登录');

    try {
      console.log(`开始上传视频: ${videoData.file.name}`);
      
      const formData = new FormData();
      formData.append('file', videoData.file);
      formData.append('uploadType', 'PRODUCT'); // 指定上传类型为PRODUCT，确保和图片使用相同处理逻辑
      
      const response = await fetch(`${API_BASE_URL}/upload`, { // 使用与图片上传相同的端点
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log(`视频上传响应:`, result);
        
        if (result.code === 200 && result.data) {
          // 处理响应格式(与图片上传使用相同的格式)
          let videoPath = '';
          if (typeof result.data === 'object') {
            // 标准格式：返回对象包含filePath属性
            if (result.data.filePath) {
              videoPath = result.data.filePath;
            } else if (result.data.fileUrl) {
              videoPath = result.data.fileUrl;
            } else if (result.data.fileName) {
              // 如果只有fileName，构造路径
              const subDir = 'products'; // 产品目录
              videoPath = `/uploads/${subDir}/${result.data.fileName}`;
            }
          } else if (typeof result.data === 'string') {
            // 如果直接返回字符串路径
            videoPath = result.data;
          }
          
          // 构造完整URL
          const fullVideoUrl = videoPath ? getFullImageUrl(videoPath) : '';
          
          if (!videoPath) {
            console.error('无法从响应中解析视频路径:', result);
            throw new Error('无法解析视频路径');
          }
          
          console.log(`视频完整URL: ${fullVideoUrl}`);
          
          console.log(`视频上传成功，路径: ${videoPath}`);
          return videoPath;
        } else {
          throw new Error(result.message || '视频上传失败');
        }
      } else {
        const errorResult = await response.json();
        throw new Error(errorResult.message || `视频上传失败，状态码：${response.status}`);
      }
    } catch (error) {
      console.error(`上传视频失败:`, error);
      throw new Error(`上传视频失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 删除商品
  const handleDeleteProduct = async (product: Product) => {
    try {
      setProductToDelete(null);
      setShowDeleteModal(false);
      
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      console.log('删除商品 - 开始删除');
      console.log('删除商品 - 产品ID:', product.productId);
      console.log('删除商品 - 产品名称:', product.name);
      console.log('删除商品 - API URL:', `${API_BASE_URL}/shop/products/${product.productId}`);
      
      const response = await fetch(`${API_BASE_URL}/shop/products/${product.productId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('删除商品 - 响应状态:', response.status);
      console.log('删除商品 - 响应headers:', response.headers);
      
      const responseText = await response.text();
      console.log('删除商品 - 响应原始内容:', responseText);
      
      if (response.ok) {
        // 尝试解析JSON响应
        let result;
        try {
          result = JSON.parse(responseText);
          console.log('删除商品 - 解析后的响应:', result);
        } catch (e) {
          console.log('删除商品 - 响应不是JSON格式');
        }
        
        // 从本地列表中移除商品
        setProducts(prev => prev.filter(p => p.productId !== product.productId));
        alert('商品删除成功！');
        
        // 重新获取数据以确保状态同步
        setTimeout(() => {
          fetchProducts();
        }, 500);
      } else {
        // 尝试解析错误信息
        let errorMessage = '删除失败';
        try {
          const errorResult = JSON.parse(responseText);
          console.error('删除商品失败 - 错误响应:', errorResult);
          errorMessage = errorResult.message || errorResult.error || '服务器错误';
        } catch (e) {
          console.error('删除商品失败 - 无法解析错误响应');
          errorMessage = `服务器返回错误: ${response.status} ${response.statusText}`;
        }
        alert('删除失败：' + errorMessage);
      }
    } catch (error) {
      console.error('删除商品失败 - 异常:', error);
      alert('删除失败：' + (error instanceof Error ? error.message : '网络错误，请稍后重试'));
    }
  };

  // 编辑商品 - 打开编辑模态框
  const handleEditProduct = (product: Product) => {
    console.log('编辑商品:', product);
    
    // 解析商品的规格参数（如果有的话）
    let specifications = {
      material: '',
      profile: '',
      keyCount: '',
      layout: '',
      compatibility: ''
    };
    
    // 如果产品有specifications字段，尝试解析它
    if ((product as any).specifications) {
      try {
        if (typeof (product as any).specifications === 'string') {
          specifications = JSON.parse((product as any).specifications);
        } else {
          specifications = (product as any).specifications;
        }
      } catch (e) {
        console.log('解析规格参数失败:', e);
      }
    }
    
    // 填充编辑表单
    setNewProduct({
      name: product.name,
      description: product.description || '',
      price: product.price,
      stock: product.stock,
      category: product.category,
      tags: [],
      specifications: specifications,
      images: product.images || [],
      videoUrl: product.videoUrl || ''
    });
    
    setSelectedProduct(product);
    setIsEditing(true);
    setShowAddModal(true);
    
    // 清空之前的上传图片和视频
    uploadedImages.forEach(img => URL.revokeObjectURL(img.preview));
    setUploadedImages([]);
    if (uploadedVideo) {
      URL.revokeObjectURL(uploadedVideo.preview);
      setUploadedVideo(null);
    }
  };

  // 更新商品
  const handleUpdateProduct = async () => {
    try {
      if (!selectedProduct) {
        alert('未选择要更新的商品');
        return;
      }
      
      if (!newProduct.name || !newProduct.category) {
        alert('请填写商品名称和分类');
        return;
      }

      setIsUploading(true);
      setUploadProgress(0);

      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        setIsUploading(false);
        return;
      }

      let imagePaths: string[] = [...newProduct.images]; // 保留原有图片
      let videoPath: string = newProduct.videoUrl || '';
      
      // 如果有新图片需要上传
      if (uploadedImages.length > 0) {
        try {
          const newImagePaths = await uploadImages(uploadedImages);
          imagePaths = [...imagePaths, ...newImagePaths]; // 添加新图片
          console.log('新图片上传成功:', newImagePaths);
        } catch (error) {
          console.error('图片上传失败:', error);
          alert('图片上传失败，请重试');
          setIsUploading(false);
          return;
        }
      }
      
      // 如果有新视频需要上传
      if (uploadedVideo) {
        try {
          videoPath = await uploadVideo(uploadedVideo);
          console.log('视频上传成功:', videoPath);
        } catch (error) {
          console.error('视频上传失败:', error);
          alert('视频上传失败，请重试');
          setIsUploading(false);
          return;
        }
      }

      // 创建更新数据
      const updateData = {
        name: newProduct.name.trim(),
        description: newProduct.description.trim(),
        price: Number(newProduct.price),
        stock: Number(newProduct.stock),
        category: newProduct.category,
        images: imagePaths,
        videoUrl: videoPath,
        specifications: {
          material: newProduct.specifications.material || '',
          profile: newProduct.specifications.profile || '',
          keyCount: newProduct.specifications.keyCount || '',
          layout: newProduct.specifications.layout || '',
          compatibility: newProduct.specifications.compatibility || ''
        }
      };

      console.log('更新商品 - 开始更新');
      console.log('更新商品 - 产品ID:', selectedProduct.productId);
      console.log('更新商品 - 产品名称:', selectedProduct.name);
      console.log('更新商品 - 更新数据:', JSON.stringify(updateData, null, 2));
      console.log('更新商品 - API URL:', `${API_BASE_URL}/shop/products/${selectedProduct.productId}`);

      const response = await fetch(`${API_BASE_URL}/shop/products/${selectedProduct.productId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      console.log('更新商品 - 响应状态:', response.status);
      console.log('更新商品 - 响应headers:', response.headers);
      
      const responseText = await response.text();
      console.log('更新商品 - 响应原始内容:', responseText);

      if (response.ok) {
        let result;
        try {
          result = JSON.parse(responseText);
          console.log('更新商品 - 解析后的响应:', result);
        } catch (e) {
          console.log('更新商品 - 响应不是JSON格式');
        }
        
        alert('商品更新成功');
        
        // 清理状态
        setShowAddModal(false);
        setIsEditing(false);
        setSelectedProduct(null);
        
        // 清理预览URL
        uploadedImages.forEach(img => URL.revokeObjectURL(img.preview));
        if (uploadedVideo) {
          URL.revokeObjectURL(uploadedVideo.preview);
        }
        
        setUploadedImages([]);
        setUploadedVideo(null);
        setNewProduct({ 
          name: '', 
          description: '', 
          price: 0, 
          stock: 0, 
          category: '', 
          tags: [],
          specifications: {
            material: '',
            profile: '',
            keyCount: '',
            layout: '',
            compatibility: ''
          },
          images: [],
          videoUrl: ''
        });
        
        // 重新获取数据
        setTimeout(() => {
          fetchProducts();
        }, 500);
      } else {
        // 尝试解析错误信息
        let errorMessage = '更新失败';
        try {
          const errorResult = JSON.parse(responseText);
          console.error('更新商品失败 - 错误响应:', errorResult);
          errorMessage = errorResult.message || errorResult.error || '服务器错误';
        } catch (e) {
          console.error('更新商品失败 - 无法解析错误响应');
          errorMessage = `服务器返回错误: ${response.status} ${response.statusText}`;
        }
        alert('更新失败：' + errorMessage);
      }
    } catch (error) {
      console.error('更新商品失败:', error);
      alert('更新商品失败，请稍后重试');
    } finally {
      setIsUploading(false);
    }
  };

  // 添加商品
  const handleAddProduct = async () => {
    try {
      if (!newProduct.name || !newProduct.category) {
        alert('请填写商品名称和分类');
        return;
      }
      
      // 检查是否至少有一个媒体（图片或视频）
      if (uploadedImages.length === 0 && !uploadedVideo) {
        alert('请至少上传一张图片或一个视频');
        return;
      }

      setIsUploading(true);
      setUploadProgress(0);

      const token = localStorage.getItem('token');
      if (!token) return;

      let imagePaths: string[] = [];
      let videoPath: string = '';
      
      // 如果有图片需要上传
      if (uploadedImages.length > 0) {
        try {
          imagePaths = await uploadImages(uploadedImages);
        } catch (error) {
          console.error('图片上传失败:', error);
          alert('图片上传失败，请重试');
          setIsUploading(false);
          return;
        }
      }
      
      // 如果有视频需要上传
      if (uploadedVideo) {
        try {
          videoPath = await uploadVideo(uploadedVideo);
        } catch (error) {
          console.error('视频上传失败:', error);
          alert('视频上传失败，请重试');
          setIsUploading(false);
          return;
        }
      }

      // 创建商品数据 - 只发送后端需要的字段
      const productData = {
        name: newProduct.name,
        description: newProduct.description,
        price: newProduct.price,
        stock: newProduct.stock,
        category: newProduct.category,
        images: imagePaths, // 直接使用原始路径，不要格式化
        videoUrl: videoPath || ''
      };
      
      console.log('格式化后的路径:', {
        images: productData.images,
        videoUrl: productData.videoUrl
      });

      console.log('添加商品 - 发送数据:', productData);
      const response = await fetch(`${API_BASE_URL}/shop/products`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(productData)
      });

      const result = await response.json();
      console.log('添加商品 - API响应:', result);

      if (response.ok && (result.success || result.code === 200)) {
        // 清理预览URL
        uploadedImages.forEach(img => URL.revokeObjectURL(img.preview));
        if (uploadedVideo) {
          URL.revokeObjectURL(uploadedVideo.preview);
        }
        
        setShowAddModal(false);
        setNewProduct({ 
          name: '', 
          description: '', 
          price: 0, 
          stock: 0, 
          category: '', 
          tags: [],
          specifications: {
            material: '',
            profile: '',
            keyCount: '',
            layout: '',
            compatibility: ''
          },
          images: [],
          videoUrl: ''
        });
        setUploadedImages([]);
        setUploadedVideo(null);
        setUploadProgress(0);
        fetchProducts();
        alert('商品添加成功！');
      } else {
        console.error('添加商品失败 - 响应:', result);
        alert('添加商品失败：' + (result.message || '未知错误'));
      }
    } catch (error) {
      console.error('添加商品失败:', error);
      alert('添加商品失败，请重试');
    } finally {
      setIsUploading(false);
    }
  };

  // 更新商品状态
  const handleUpdateStatus = async (productId: string, newStatus: Product['status']) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      // 将前端状态映射到后端状态
      const backendStatus = mapFrontendStatusToBackend(newStatus);
      console.log('更新商品状态 - 开始更新');
      console.log('更新商品状态 - 商品ID:', productId);
      console.log('更新商品状态 - 前端状态:', newStatus, '后端状态:', backendStatus);
      console.log('更新商品状态 - API URL:', `${API_BASE_URL}/shop/products/${productId}/status`);

      const response = await fetch(`${API_BASE_URL}/shop/products/${productId}/status`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: backendStatus })
      });

      console.log('状态更新 - 响应状态:', response.status);
      
      const responseText = await response.text();
      console.log('状态更新 - 响应原始内容:', responseText);

      if (response.ok) {
        let result;
        try {
          result = JSON.parse(responseText);
          console.log('状态更新成功:', result);
        } catch (e) {
          console.log('状态更新 - 响应不是JSON格式');
        }
        
        // 立即更新本地状态
        setProducts(prev => prev.map(p => 
          p.productId === productId 
            ? { ...p, status: newStatus }
            : p
        ));
        
        alert(`商品${newStatus === 'ACTIVE' ? '上架' : newStatus === 'INACTIVE' ? '下架' : '状态更新'}成功！`);
        
        // 延迟重新获取数据以确保同步
        setTimeout(() => {
          fetchProducts();
        }, 1000);
      } else {
        // 尝试解析错误信息
        let errorMessage = '状态更新失败';
        try {
          const errorResult = JSON.parse(responseText);
          console.error('更新商品状态失败 - 错误响应:', errorResult);
          errorMessage = errorResult.message || errorResult.error || '服务器错误';
        } catch (e) {
          console.error('更新商品状态失败 - 无法解析错误响应');
          errorMessage = `服务器返回错误: ${response.status} ${response.statusText}`;
        }
        alert('状态更新失败：' + errorMessage);
      }
    } catch (error) {
      console.error('更新商品状态失败 - 异常:', error);
      alert('状态更新失败：' + (error instanceof Error ? error.message : '网络错误，请稍后重试'));
    }
  };

  // 筛选商品
  const filteredProducts = products.filter(product => {
    if (activeTab === 'all') return true;
    if (activeTab === 'active') return product.status === 'ACTIVE';
    if (activeTab === 'inactive') return product.status === 'INACTIVE';
    if (activeTab === 'outOfStock') return product.status === 'OUT_OF_STOCK';
    return true;
  });

  const getStatusColor = (status: Product['status']) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'INACTIVE':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'OUT_OF_STOCK':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: Product['status']) => {
    switch (status) {
      case 'ACTIVE':
        return '在售';
      case 'INACTIVE':
        return '下架';
      case 'OUT_OF_STOCK':
        return '缺货';
      default:
        return '未知';
    }
  };

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }
    fetchProducts();
  }, [user, router]);

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* 顶部导航 */}
      <nav className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-sm mr-3">
                K
              </Link>
              <h1 className="text-xl font-bold text-white">商品管理</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/notifications"
                className="text-gray-300 hover:text-white transition-colors"
              >
                <span className="text-xl">🔔</span>
              </Link>
              <span className="text-gray-300">店主：{user.nickname || user.username}</span>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* 侧边栏 */}
        <aside className="w-64 bg-gray-800 min-h-screen pt-4">
          <nav className="px-4 space-y-2">
            <Link href="/dashboard" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🏠</span>
              返回首页
            </Link>
            <Link href="/shop/my-store" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🏪</span>
              我的店铺
            </Link>
            <Link href="/shop/products" className="flex items-center px-4 py-2 text-white bg-blue-600 rounded-lg">
              <span className="mr-3">📦</span>
              商品管理
            </Link>
            <Link href="/shop/orders" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📋</span>
              订单管理
            </Link>
            <Link href="/shop/settings" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">⚙️</span>
              店铺设置
            </Link>
          </nav>
        </aside>

        {/* 主内容区 */}
        <main className="flex-1 p-6">
          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm font-medium">商品总数</p>
                  <p className="text-3xl font-bold">{stats.total}</p>
                </div>
                <div className="text-4xl">📦</div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm font-medium">在售商品</p>
                  <p className="text-3xl font-bold">{stats.active}</p>
                </div>
                <div className="text-4xl">✅</div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-gray-500 to-slate-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-100 text-sm font-medium">已下架</p>
                  <p className="text-3xl font-bold">{stats.inactive}</p>
                </div>
                <div className="text-4xl">⏸️</div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-red-500 to-pink-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-100 text-sm font-medium">缺货商品</p>
                  <p className="text-3xl font-bold">{stats.outOfStock}</p>
                </div>
                <div className="text-4xl">⚠️</div>
              </div>
            </div>
          </div>

          {/* 筛选和操作栏 */}
          <div className="bg-gray-800 rounded-2xl p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-white">商品列表</h2>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowAddModal(true)}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  ➕ 添加商品
                </button>
              </div>
            </div>
            
            <div className="flex space-x-2">
              {[
                { key: 'all', label: '全部', count: stats.total },
                { key: 'active', label: '在售', count: stats.active },
                { key: 'inactive', label: '下架', count: stats.inactive },
                { key: 'outOfStock', label: '缺货', count: stats.outOfStock }
              ].map(tab => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    activeTab === tab.key
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  {tab.label} ({tab.count})
                </button>
              ))}
            </div>
          </div>

          {/* 商品列表 */}
          <div className="bg-gray-800 rounded-2xl overflow-hidden">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className="ml-3 text-gray-300">加载中...</span>
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📦</div>
                <h3 className="text-xl font-bold text-white mb-2">暂无商品</h3>
                <p className="text-gray-400">开始添加您的第一个商品吧！</p>
                <button
                  onClick={() => setShowAddModal(true)}
                  className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors"
                >
                  添加商品
                </button>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-700">
                    <tr>
                      <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">商品信息</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">价格</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">库存</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">状态</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">更新时间</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">操作</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700">
                    {filteredProducts.map((product) => (
                      <tr key={product.id} className="hover:bg-gray-700/50 transition-colors">
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <div className="w-12 h-12 bg-gray-600 rounded-lg mr-4 flex items-center justify-center overflow-hidden">
                              {product.images && product.images.length > 0 ? (
                                <img 
                                  key={product.images[0]} // 添加key确保更新
                                  src={getFullImageUrl(product.images[0])} 
                                  alt={product.name}
                                  className="w-full h-full object-cover"
                                  onError={(e) => {
                                    console.error('商品图片加载失败:', product.images[0], '尝试使用完整URL:', getFullImageUrl(product.images[0]));
                                    const target = e.target as HTMLImageElement;
                                    const originalSrc = target.src;
                                    
                                    // 尝试多种可能的URL格式
                                    const possibleFormats = [
                                      // 1. 尝试直接使用文件名
                                      () => {
                                        const fileName = product.images[0].split('/').pop();
                                        return `http://localhost:8080/uploads/products/${fileName}`;
                                      },
                                      // 2. 尝试使用相对路径
                                      () => {
                                        return `http://localhost:8080${product.images[0]}`;
                                      },
                                      // 3. 尝试使用不同的基础路径
                                      () => {
                                        return `http://localhost:8080/api${product.images[0]}`;
                                      }
                                    ];
                                    
                                    // 如果当前URL已经失败，尝试下一个格式
                                    for (const formatFn of possibleFormats) {
                                      const newUrl = formatFn();
                                      if (newUrl !== originalSrc) {
                                        console.log('尝试替代URL:', newUrl);
                                        target.src = newUrl;
                                        return; // 先尝试这个新URL
                                      }
                                    }
                                    
                                    // 如果所有尝试都失败，显示占位符
                                    target.style.display = 'none';
                                    const nextSibling = target.nextElementSibling as HTMLElement;
                                    if (nextSibling) {
                                      nextSibling.style.display = 'flex';
                                    }
                                  }}
                                />
                              ) : null}
                              <div className="w-6 h-6 bg-gray-700 rounded flex items-center justify-center text-xs text-white">
                                {product.category?.[0]?.toUpperCase() || 'P'}
                              </div>
                            </div>
                            <div>
                              <div className="text-sm font-medium text-white">{product.name}</div>
                              <div className="text-sm text-gray-200 mt-1">ID: {product.productId}</div>
                              {product.images && product.images.length > 0 && (
                                <div className="flex mt-2 space-x-1">
                                  {product.images.slice(0, 3).map((img, idx) => (
                                    <div key={idx} className="w-6 h-6 bg-gray-700 rounded overflow-hidden">
                                      <img 
                                        src={getFullImageUrl(img)} 
                                        alt={`缩略图 ${idx+1}`}
                                        className="w-full h-full object-cover"
                                      />
                                    </div>
                                  ))}
                                  {product.images.length > 3 && (
                                    <div className="w-6 h-6 bg-gray-700 rounded flex items-center justify-center text-xs text-gray-400">
                                      +{product.images.length - 3}
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-lg font-semibold text-white">¥{product.price.toFixed(2)}</span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`text-sm font-medium ${
                            product.stock > 10 ? 'text-green-400' : 
                            product.stock > 0 ? 'text-yellow-400' : 'text-red-400'
                          }`}>
                            {product.stock} 件
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(product.status)}`}>
                            {getStatusText(product.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                          {new Date(product.updateTime).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleEditProduct(product)}
                              className="text-blue-400 hover:text-blue-300 transition-colors"
                            >
                              编辑
                            </button>
                            {product.status === 'ACTIVE' ? (
                              <button
                                onClick={() => handleUpdateStatus(product.productId, 'INACTIVE')}
                                className="text-red-400 hover:text-red-300 transition-colors"
                              >
                                下架
                              </button>
                            ) : (
                              <button
                                onClick={() => handleUpdateStatus(product.productId, 'ACTIVE')}
                                className="text-green-400 hover:text-green-300 transition-colors"
                              >
                                上架
                              </button>
                            )}
                            <button
                              onClick={() => {
                                setProductToDelete(product);
                                setShowDeleteModal(true);
                              }}
                              className="text-red-400 hover:text-red-300 transition-colors"
                            >
                              删除
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </main>
      </div>

      {/* 添加商品模态框 */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-2xl p-8 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-white">{isEditing ? '编辑商品' : '添加新商品'}</h3>
              <button
                onClick={() => {
                  setShowAddModal(false);
                  setIsEditing(false);
                  setSelectedProduct(null);
                  // 清理预览URL
                  uploadedImages.forEach(img => URL.revokeObjectURL(img.preview));
                  setUploadedImages([]);
                  if (uploadedVideo) {
                    URL.revokeObjectURL(uploadedVideo.preview);
                    setUploadedVideo(null);
                  }
                  setNewProduct({ 
                    name: '', 
                    description: '', 
                    price: 0, 
                    stock: 0, 
                    category: '', 
                    tags: [],
                    specifications: {
                      material: '',
                      profile: '',
                      keyCount: '',
                      layout: '',
                      compatibility: ''
                    },
                    images: [],
                    videoUrl: ''
                  });
                }}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-8">
              {/* 基本信息 */}
              <div className="bg-gray-750 rounded-lg p-6">
                <h4 className="text-lg font-medium text-white mb-4 flex items-center">
                  <span className="mr-2">📝</span>
                  基本信息
                </h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">商品名称 *</label>
                    <input
                      type="text"
                      value={newProduct.name}
                      onChange={(e) => setNewProduct(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      style={{ color: 'white', WebkitTextFillColor: 'white', opacity: 1 }}
                      placeholder="请输入商品名称"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-2">商品分类 *</label>
                    <select
                      value={newProduct.category}
                      onChange={(e) => setNewProduct(prev => ({ ...prev, category: e.target.value }))}
                      className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      style={{ color: 'white', WebkitTextFillColor: 'white', opacity: 1 }}
                    >
                      <option value="" className="bg-gray-700 text-gray-300">请选择分类</option>
                      <option value="GMK" className="bg-gray-700 text-white">GMK 键帽</option>
                      <option value="SA" className="bg-gray-700 text-white">SA 键帽</option>
                      <option value="Jellykey" className="bg-gray-700 text-white">Jellykey 艺术键帽</option>
                      <option value="PBT" className="bg-gray-700 text-white">PBT 键帽</option>
                      <option value="主题键帽" className="bg-gray-700 text-white">主题键帽</option>
                      <option value="定制键帽" className="bg-gray-700 text-white">定制键帽</option>
                    </select>
                  </div>

                  <div className="lg:col-span-2">
                    <label className="block text-sm font-medium text-white mb-2">商品描述</label>
                    <textarea
                      value={newProduct.description}
                      onChange={(e) => setNewProduct(prev => ({ ...prev, description: e.target.value }))}
                      rows={4}
                      className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      style={{ color: 'white', WebkitTextFillColor: 'white', opacity: 1 }}
                      placeholder="请详细描述商品特点、材质、工艺等信息"
                    />
                  </div>
                </div>
              </div>

              {/* 规格参数 */}
              <div className="bg-gray-750 rounded-lg p-6">
                <h4 className="text-lg font-medium text-white mb-4 flex items-center">
                  <span className="mr-2">⚙️</span>
                  规格参数
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">材质</label>
                    <select
                      value={newProduct.specifications.material}
                      onChange={(e) => setNewProduct(prev => ({ 
                        ...prev, 
                        specifications: { ...prev.specifications, material: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      style={{ color: 'white', WebkitTextFillColor: 'white', opacity: 1 }}
                    >
                      <option value="" className="bg-gray-700 text-gray-300">请选择材质</option>
                      <option value="PBT" className="bg-gray-700 text-white">PBT</option>
                      <option value="ABS" className="bg-gray-700 text-white">ABS</option>
                      <option value="POM" className="bg-gray-700 text-white">POM</option>
                      <option value="树脂" className="bg-gray-700 text-white">树脂</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-2">键帽高度</label>
                    <select
                      value={newProduct.specifications.profile}
                      onChange={(e) => setNewProduct(prev => ({ 
                        ...prev, 
                        specifications: { ...prev.specifications, profile: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      style={{ color: 'white', WebkitTextFillColor: 'white', opacity: 1 }}
                    >
                      <option value="" className="bg-gray-700 text-gray-300">请选择高度</option>
                      <option value="Cherry" className="bg-gray-700 text-white">Cherry</option>
                      <option value="OEM" className="bg-gray-700 text-white">OEM</option>
                      <option value="SA" className="bg-gray-700 text-white">SA</option>
                      <option value="XDA" className="bg-gray-700 text-white">XDA</option>
                      <option value="DSA" className="bg-gray-700 text-white">DSA</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-2">键数</label>
                    <select
                      value={newProduct.specifications.keyCount}
                      onChange={(e) => setNewProduct(prev => ({ 
                        ...prev, 
                        specifications: { ...prev.specifications, keyCount: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      style={{ color: 'white', WebkitTextFillColor: 'white', opacity: 1 }}
                    >
                      <option value="" className="bg-gray-700 text-gray-300">请选择键数</option>
                      <option value="104键" className="bg-gray-700 text-white">104键 (全尺寸)</option>
                      <option value="87键" className="bg-gray-700 text-white">87键 (无数字区)</option>
                      <option value="68键" className="bg-gray-700 text-white">68键 (65%)</option>
                      <option value="61键" className="bg-gray-700 text-white">61键 (60%)</option>
                      <option value="自定义" className="bg-gray-700 text-white">自定义</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-2">布局</label>
                    <select
                      value={newProduct.specifications.layout}
                      onChange={(e) => setNewProduct(prev => ({ 
                        ...prev, 
                        specifications: { ...prev.specifications, layout: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      style={{ color: 'white', WebkitTextFillColor: 'white', opacity: 1 }}
                    >
                      <option value="" className="bg-gray-700 text-gray-300">请选择布局</option>
                      <option value="ANSI" className="bg-gray-700 text-white">ANSI (美式)</option>
                      <option value="ISO" className="bg-gray-700 text-white">ISO (欧式)</option>
                      <option value="JIS" className="bg-gray-700 text-white">JIS (日式)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-2">兼容性</label>
                    <input
                      type="text"
                      value={newProduct.specifications.compatibility}
                      onChange={(e) => setNewProduct(prev => ({ 
                        ...prev, 
                        specifications: { ...prev.specifications, compatibility: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      style={{ color: 'white', WebkitTextFillColor: 'white', opacity: 1 }}
                      placeholder="如：Cherry MX, 3-pin/5-pin"
                    />
                  </div>
                </div>
              </div>

              {/* 价格库存 */}
              <div className="bg-gray-750 rounded-lg p-6">
                <h4 className="text-lg font-medium text-white mb-4 flex items-center">
                  <span className="mr-2">💰</span>
                  价格与库存
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">售价 (¥) *</label>
                    <input
                      type="number"
                      step="0.01"
                      value={newProduct.price}
                      onChange={(e) => setNewProduct(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                      className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      style={{ color: 'white', WebkitTextFillColor: 'white', opacity: 1 }}
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-2">库存数量 *</label>
                    <input
                      type="number"
                      value={newProduct.stock}
                      onChange={(e) => setNewProduct(prev => ({ ...prev, stock: parseInt(e.target.value) || 0 }))}
                      className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      style={{ color: 'white', WebkitTextFillColor: 'white', opacity: 1 }}
                      placeholder="0"
                    />
                  </div>
                </div>
              </div>

              {/* 右侧：图片上传 */}
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    商品图片 <span className="text-gray-200">(至少上传图片或视频其中之一，最多5张图片，每张不超过10MB)</span>
                  </label>
                  
                  {/* 图片上传区域 */}
                  <div className="bg-gray-750 rounded-lg p-6">
                    <h4 className="text-lg font-medium text-white mb-4 flex items-center">
                      <span className="mr-2">📷</span>
                      图片上传
                    </h4>
                    
                    {/* 图片上传和预览 */}
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-white mb-2">商品图片（最多5张）</label>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                        {/* 显示现有图片（编辑模式） */}
                        {isEditing && newProduct.images.map((imagePath, index) => (
                          <div key={`existing-${index}`} className="relative aspect-square rounded-lg overflow-hidden bg-gray-700 border border-gray-600">
                            <img 
                              src={getFullImageUrl(imagePath)}
                              alt={`现有图片 ${index + 1}`}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                console.log(`现有图片加载错误: ${index}`, e);
                                const imgElement = e.currentTarget as HTMLImageElement;
                                imgElement.onerror = null;
                                imgElement.src = '/images/placeholders/image-placeholder.png';
                              }}
                            />
                            <button
                              onClick={() => {
                                // 从现有图片列表中移除
                                setNewProduct(prev => ({
                                  ...prev,
                                  images: prev.images.filter((_, i) => i !== index)
                                }));
                              }}
                              className="absolute top-2 right-2 bg-red-600 rounded-full w-6 h-6 flex items-center justify-center text-white text-xs"
                            >
                              ✕
                            </button>
                            <div className="absolute bottom-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                              现有
                            </div>
                          </div>
                        ))}
                        
                        {/* 显示新上传的图片 */}
                        {uploadedImages.map((image, index) => (
                          <div key={`new-${index}`} className="relative aspect-square rounded-lg overflow-hidden bg-gray-700 border border-gray-600">
                            <img 
                              src={image.preview}
                              alt={`新上传图片 ${index + 1}`}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                console.log(`新图片加载错误: ${index}`, e);
                                const imgElement = e.currentTarget as HTMLImageElement;
                                imgElement.onerror = null;
                                imgElement.src = '/images/placeholders/image-placeholder.png';
                              }}
                            />
                            <button
                              onClick={() => removeImage(index)}
                              className="absolute top-2 right-2 bg-red-600 rounded-full w-6 h-6 flex items-center justify-center text-white text-xs"
                            >
                              ✕
                            </button>
                            <div className="absolute bottom-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded">
                              新增
                            </div>
                          </div>
                        ))}
                        
                        {/* 上传按钮 */}
                        {(newProduct.images.length + uploadedImages.length) < 5 && (
                          <div className="aspect-square rounded-lg bg-gray-700 border border-gray-600 flex items-center justify-center cursor-pointer">
                            <label className="flex flex-col items-center justify-center w-full h-full cursor-pointer">
                              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                              </svg>
                              <span className="mt-2 text-xs text-white">添加图片</span>
                              <input type="file" className="hidden" onChange={handleFileSelect} accept="image/*" multiple />
                            </label>
                          </div>
                        )}
                      </div>
                      <p className="text-xs text-gray-200 mt-2">
                        支持JPG、PNG、GIF格式，每张图片不超过10MB
                      </p>
                    </div>
                    
                    {/* 视频上传和预览 */}
                    <div>
                      <label className="block text-sm font-medium text-white mb-2">商品视频（至少上传图片或视频其中之一）</label>
                      <div className="flex flex-col space-y-4">
                        {/* 视频预览 */}
                        {uploadedVideo ? (
                          <div className="relative rounded-lg overflow-hidden bg-gray-700 border border-gray-600 aspect-video">
                            <video 
                              src={uploadedVideo.preview}
                              className="w-full h-full object-contain"
                              controls
                              onError={(e) => {
                                console.log("视频加载错误，尝试替代方案", e);
                                const videoElement = e.currentTarget as HTMLVideoElement;
                                videoElement.onerror = null; // 防止无限循环
                                
                                // 创建一个提示信息
                                const parent = videoElement.parentElement;
                                if (parent) {
                                  const errorDiv = document.createElement('div');
                                  errorDiv.className = "absolute inset-0 flex items-center justify-center text-gray-400";
                                  errorDiv.innerHTML = '<div class="text-center"><svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg><div>视频预览不可用</div></div>';
                                  parent.appendChild(errorDiv);
                                }
                              }}
                            />
                            <button
                              onClick={removeVideo}
                              className="absolute top-2 right-2 bg-red-600 rounded-full w-6 h-6 flex items-center justify-center text-white text-xs"
                            >
                              ✕
                            </button>
                          </div>
                        ) : (
                          // 上传按钮
                          <div className="aspect-video rounded-lg bg-gray-700 border border-gray-600 flex items-center justify-center cursor-pointer">
                            <label className="flex flex-col items-center justify-center w-full h-full cursor-pointer">
                              <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                              </svg>
                              <span className="mt-2 text-sm text-white">添加视频</span>
                              <input type="file" className="hidden" onChange={handleVideoSelect} accept="video/*" />
                            </label>
                          </div>
                        )}
                        <p className="text-xs text-gray-200">
                          支持MP4、WEBM格式，视频不超过20MB，推荐尺寸：1280×720像素
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4 mt-8">
              <button
                onClick={() => {
                  setShowAddModal(false);
                  setIsEditing(false);
                  setSelectedProduct(null);
                  // 清理预览URL
                  uploadedImages.forEach(img => URL.revokeObjectURL(img.preview));
                  setUploadedImages([]);
                  if (uploadedVideo) {
                    URL.revokeObjectURL(uploadedVideo.preview);
                    setUploadedVideo(null);
                  }
                  setNewProduct({ 
                    name: '', 
                    description: '', 
                    price: 0, 
                    stock: 0, 
                    category: '', 
                    tags: [],
                    specifications: {
                      material: '',
                      profile: '',
                      keyCount: '',
                      layout: '',
                      compatibility: ''
                    },
                    images: [],
                    videoUrl: ''
                  });
                }}
                className="px-6 py-3 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors"
                disabled={isUploading}
              >
                取消
              </button>
              <button
                onClick={isEditing ? handleUpdateProduct : handleAddProduct}
                disabled={!newProduct.name || !newProduct.category || isUploading}
                className="px-6 py-3 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                {isUploading ? (isEditing ? '更新中...' : '添加中...') : (isEditing ? '更新商品' : '添加商品')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 删除确认模态框 */}
      {showDeleteModal && productToDelete && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-2xl p-8 w-full max-w-md">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-white">确认删除</h3>
              <button
                onClick={() => {
                  setShowDeleteModal(false);
                  setProductToDelete(null);
                }}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="mb-6">
              <p className="text-white mb-4">
                确定要删除商品 "<span className="text-white font-medium">{productToDelete.name}</span>" 吗？
              </p>
              <p className="text-red-400 text-sm">
                ⚠️ 此操作不可恢复，删除后商品将从商城中消失。
              </p>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                onClick={() => {
                  setShowDeleteModal(false);
                  setProductToDelete(null);
                }}
                className="px-6 py-3 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors"
              >
                取消
              </button>
              <button
                onClick={() => handleDeleteProduct(productToDelete)}
                className="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                确认删除
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 