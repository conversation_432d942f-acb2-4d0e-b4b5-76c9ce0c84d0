'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import AddressPicker from '@/components/ui/AddressPicker';

// API基础URL
const API_BASE_URL = 'http://localhost:8080/api';

// 商品信息接口
interface Product {
  id: number;
  productName: string;
  price: number;
  originalPrice: number;
  mainImage?: string;
  stock: number;
  shopName: string;
}

// 用户地址接口
interface UserAddress {
  id: number;
  receiverName: string;
  phone: string;
  province: string;
  city: string;
  district: string;
  detailAddress: string;
  isDefault: boolean;
}

export default function BuyNowPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // 从URL参数获取商品信息
  const productId = searchParams.get('productId');
  const quantity = parseInt(searchParams.get('quantity') || '1');
  
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  
  // 用户地址相关
  const [addresses, setAddresses] = useState<UserAddress[]>([]);
  const [selectedAddressId, setSelectedAddressId] = useState<number | null>(null);
  const [editingAddressId, setEditingAddressId] = useState<number | null>(null);
  
  // 订单信息
  const [orderQuantity, setOrderQuantity] = useState(quantity);
  const [buyerNote, setBuyerNote] = useState('');
  
  // 新地址表单
  const [newAddress, setNewAddress] = useState({
    receiverName: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    detailAddress: '',
    saveAddress: false
  });

  // 编辑地址表单
  const [editAddress, setEditAddress] = useState({
    receiverName: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    detailAddress: ''
  });

  // 获取token
  const getToken = () => {
    return localStorage.getItem('token');
  };

  // 获取商品详情
  const fetchProduct = async () => {
    if (!productId) {
      alert('商品参数错误！');
      router.push('/shop');
      return;
    }

    try {
      const token = getToken();
      if (!token) {
        alert('请先登录！');
        router.push('/login');
        return;
      }

      const response = await fetch(`${API_BASE_URL}/products/${productId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200 && result.data) {
          setProduct(result.data);
        } else {
          throw new Error('商品信息获取失败');
        }
      } else {
        throw new Error('商品不存在或已下架');
      }
    } catch (error) {
      console.error('获取商品信息失败:', error);
      alert('商品信息获取失败！');
      router.push('/shop');
    }
  };

  // 获取用户地址列表
  const fetchAddresses = async () => {
    try {
      const token = getToken();
      if (!token) {
        // 如果没有token，选择新地址选项
        setSelectedAddressId(null);
        return;
      }

      const response = await fetch(`${API_BASE_URL}/user/addresses`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log('地址获取API响应:', result); // 调试日志
        
        if (result.code === 200 && result.data) {
          setAddresses(result.data);
          console.log('获取到的地址列表:', result.data); // 调试日志
          
          if (result.data.length > 0) {
            // 如果有地址，默认选择常用地址
            const defaultAddress = result.data.find((addr: UserAddress) => addr.isDefault);
            if (defaultAddress) {
              setSelectedAddressId(defaultAddress.id);
              console.log('自动选择默认地址:', defaultAddress); // 调试日志
            } else {
              // 没有默认地址，选择第一个
              setSelectedAddressId(result.data[0].id);
              console.log('选择第一个地址:', result.data[0]); // 调试日志
            }
          } else {
            // 如果没有保存的地址，选择新地址选项
            setSelectedAddressId(null);
            console.log('没有保存的地址，显示新地址表单'); // 调试日志
          }
        } else {
          // API返回错误，选择新地址选项
          console.log('API返回错误，显示新地址表单:', result); // 调试日志
          setSelectedAddressId(null);
        }
      } else {
        // 请求失败，选择新地址选项
        console.error('获取地址列表失败，显示新地址表单');
        setSelectedAddressId(null);
      }
    } catch (error) {
      console.error('获取地址列表失败:', error);
      // 出现异常，选择新地址选项
      setSelectedAddressId(null);
    }
  };

  // 创建订单
  const createOrder = async () => {
    if (!product) return;

    // 验证表单
    let receiverInfo;
    if (selectedAddressId) {
      const selectedAddress = addresses.find(addr => addr.id === selectedAddressId);
      if (!selectedAddress) {
        alert('请选择收货地址！');
        return;
      }
      receiverInfo = {
        receiverName: selectedAddress.receiverName,
        receiverPhone: selectedAddress.phone,
        receiverAddress: `${selectedAddress.province}${selectedAddress.city}${selectedAddress.district}${selectedAddress.detailAddress}`
      };
    } else {
      // 使用新地址
      if (!newAddress.receiverName) {
        alert('请输入收货人姓名！');
        return;
      }
      if (!newAddress.phone) {
        alert('请输入联系电话！');
        return;
      }
      if (!newAddress.province || !newAddress.city || !newAddress.district) {
        alert('请选择完整的省市区信息！');
        return;
      }
      if (!newAddress.detailAddress) {
        alert('请输入详细地址！');
        return;
      }
      receiverInfo = {
        receiverName: newAddress.receiverName,
        receiverPhone: newAddress.phone,
        receiverAddress: `${newAddress.province}${newAddress.city}${newAddress.district}${newAddress.detailAddress}`
      };
    }

    if (orderQuantity < 1 || orderQuantity > product.stock) {
      alert(`购买数量必须在1-${product.stock}之间！`);
      return;
    }

    try {
      setSubmitting(true);
      const token = getToken();
      
      if (!token) {
        alert('请先登录！');
        router.push('/login');
        return;
      }
      
      let orderData;
      
      if (selectedAddressId) {
        // 使用已保存的地址
        orderData = {
          orderType: 'DIRECT_BUY',
          items: [{
            productId: product.id,
            quantity: orderQuantity,
            unitPrice: Number(product.price)
          }],
          addressId: selectedAddressId,
          ...receiverInfo,
          buyerNote: buyerNote
        };
      } else {
        // 使用新地址
        orderData = {
          orderType: 'DIRECT_BUY',
          items: [{
            productId: product.id,
            quantity: orderQuantity,
            unitPrice: Number(product.price)
          }],
          ...receiverInfo,
          buyerNote: buyerNote,
          saveAddress: newAddress.saveAddress,
          // 发送详细的地址信息给后端用于保存
          province: newAddress.province,
          city: newAddress.city,
          district: newAddress.district,
          detailAddress: newAddress.detailAddress
        };
      }

      const response = await fetch(`${API_BASE_URL}/orders`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200 && result.data) {
          // 如果保存了新地址，重新获取地址列表
          if (!selectedAddressId && newAddress.saveAddress) {
            console.log('订单创建成功，重新获取地址列表');
            await fetchAddresses();
          }
          // 订单创建成功，跳转到支付页面
          router.push(`/payment?orderId=${result.data.orderId}`);
        } else {
          throw new Error(result.message || '订单创建失败');
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || '订单创建失败');
      }
          } catch (error) {
        console.error('创建订单失败:', error);
        alert(error instanceof Error ? error.message : '订单创建失败，请重试！');
      } finally {
      setSubmitting(false);
    }
  };

  // 开始编辑地址
  const startEditAddress = (address: UserAddress) => {
    setEditingAddressId(address.id);
    setEditAddress({
      receiverName: address.receiverName,
      phone: address.phone,
      province: address.province,
      city: address.city,
      district: address.district,
      detailAddress: address.detailAddress
    });
  };

  // 保存编辑的地址
  const saveEditAddress = async () => {
    if (!editingAddressId) return;

    // 验证表单
    if (!editAddress.receiverName) {
      alert('请输入收货人姓名！');
      return;
    }
    if (!editAddress.phone) {
      alert('请输入联系电话！');
      return;
    }
    if (!editAddress.province || !editAddress.city || !editAddress.district) {
      alert('请选择完整的省市区信息！');
      return;
    }
    if (!editAddress.detailAddress) {
      alert('请输入详细地址！');
      return;
    }

    try {
      const token = getToken();
      if (!token) {
        alert('请先登录！');
        router.push('/login');
        return;
      }

      const response = await fetch(`${API_BASE_URL}/user/addresses/${editingAddressId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          receiverName: editAddress.receiverName,
          phone: editAddress.phone,
          province: editAddress.province,
          city: editAddress.city,
          district: editAddress.district,
          detailAddress: editAddress.detailAddress,
          isDefault: false // 编辑时不改变默认状态
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          alert('地址更新成功！');
          setEditingAddressId(null);
          // 重新获取地址列表
          await fetchAddresses();
        } else {
          throw new Error(result.message || '地址更新失败');
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || '地址更新失败');
      }
    } catch (error) {
      console.error('更新地址失败:', error);
      alert(error instanceof Error ? error.message : '地址更新失败，请重试！');
    }
  };

  // 取消编辑地址
  const cancelEditAddress = () => {
    setEditingAddressId(null);
    setEditAddress({
      receiverName: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detailAddress: ''
    });
  };

  // 计算总价
  const calculateTotal = () => {
    if (!product) return 0;
    return product.price * orderQuantity;
  };

  // 页面加载时获取数据
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchProduct(), fetchAddresses()]);
      setLoading(false);
    };
    loadData();
  }, [productId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😔</div>
          <h3 className="text-xl font-medium text-gray-900 mb-2">商品不存在</h3>
          <Link href="/shop" className="text-purple-600 hover:text-purple-700">
            返回商城
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 cart-checkout-page" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif' }}>
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-8">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                  K
                </div>
                <span className="text-xl font-bold text-gray-900">Keycap</span>
              </Link>
              <div className="hidden md:flex items-center space-x-6">
                <Link href="/shop" className="text-gray-600 hover:text-gray-900 transition-colors">
                  商城
                </Link>
                <span className="text-purple-600 font-medium">立即购买</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="bg-gradient-to-r from-purple-600 to-cyan-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-shadow">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">确认订单</h1>
          <Link href="/shop" className="text-purple-600 hover:text-purple-700 transition-colors">
            ← 返回商城
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要内容区域 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 商品信息 */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-5 h-5 bg-cyan-100 rounded-full flex items-center justify-center">
                  <span className="text-cyan-600 text-xs">🛍️</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900">商品信息</h3>
              </div>
              <div className="flex items-start space-x-4">
                <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center overflow-hidden flex-shrink-0">
                  {product.mainImage ? (
                    <img 
                      src={product.mainImage} 
                      alt={product.productName}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="text-2xl">🎯</div>
                  )}
                </div>
                <div className="flex-1">
                  <h4 className="text-lg font-medium text-gray-900 mb-1">{product.productName}</h4>
                  <p className="text-sm text-gray-500 mb-2">店铺：{product.shopName}</p>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg font-bold text-red-600">¥{product.price}</span>
                      {product.originalPrice && product.originalPrice > product.price && (
                        <span className="text-sm text-gray-400 line-through">¥{product.originalPrice}</span>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">数量：</span>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setOrderQuantity(Math.max(1, orderQuantity - 1))}
                          className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 text-gray-700"
                        >
                          -
                        </button>
                        <span className="w-12 text-center font-medium text-gray-900">{orderQuantity}</span>
                        <button
                          onClick={() => setOrderQuantity(Math.min(product.stock, orderQuantity + 1))}
                          className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 text-gray-700"
                        >
                          +
                        </button>
                      </div>
                      <span className="text-xs text-gray-400">库存：{product.stock}件</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 收货地址 */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">📍</span>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900">收货地址</h3>
                </div>
                <div className="flex items-center space-x-2">
                  {addresses.length > 0 && (
                    <button 
                      onClick={() => {
                        const options = [
                          '重新获取地址',
                          '查看地址详情',
                          '取消'
                        ];
                        const choice = prompt(`请选择操作：
1. 重新获取地址
2. 查看地址详情

请输入选项序号（1-2）：`);
                        
                        if (choice === '1') {
                          fetchAddresses();
                          alert('地址列表已刷新！');
                        } else if (choice === '2') {
                          const addressInfo = addresses.map((addr, index) => 
                            `${index + 1}. ${addr.receiverName} ${addr.phone}\n   ${addr.province}${addr.city}${addr.district}${addr.detailAddress}${addr.isDefault ? ' [默认]' : ''}`
                          ).join('\n\n');
                          alert(`您的常用地址：\n\n${addressInfo}`);
                        }
                      }}
                      className="text-purple-600 hover:text-purple-700 text-sm"
                    >
                      管理地址
                    </button>
                  )}
                  <button 
                    onClick={() => {
                      console.log('重新获取地址...');
                      fetchAddresses();
                    }}
                    className="text-blue-600 hover:text-blue-700 text-sm"
                  >
                    刷新地址
                  </button>
                  <button 
                    onClick={() => {
                      const token = getToken();
                      alert(`调试信息：
用户Token: ${token ? '已登录' : '未登录'}
地址数量: ${addresses.length}
当前选择: ${selectedAddressId ? `地址ID ${selectedAddressId}` : '新地址'}
请查看浏览器控制台获取详细日志`);
                    }}
                    className="text-gray-600 hover:text-gray-700 text-sm"
                  >
                    调试信息
                  </button>
                </div>
              </div>

              {/* 地址选择选项 */}
              <div className="space-y-4">
                {/* 常用地址选项 */}
                <div>
                  <div className="flex items-center space-x-2 mb-3">
                    <input
                      type="radio"
                      name="addressType"
                      id="savedAddress"
                      checked={selectedAddressId !== null}
                      disabled={addresses.length === 0}
                      onChange={() => {
                        if (addresses.length > 0) {
                          const defaultAddr = addresses.find(addr => addr.isDefault) || addresses[0];
                          setSelectedAddressId(defaultAddr.id);
                        }
                      }}
                      className="w-4 h-4 text-purple-600 disabled:text-gray-400"
                    />
                    <label 
                      htmlFor="savedAddress" 
                      className={`font-medium cursor-pointer ${
                        addresses.length === 0 ? 'text-gray-400' : 'text-gray-900'
                      }`}
                    >
                      使用常用地址
                    </label>
                    {addresses.length > 0 ? (
                      <span className="bg-purple-100 text-purple-600 px-2 py-1 rounded-full text-xs">
                        {addresses.length}个地址
                      </span>
                    ) : (
                      <span className="bg-gray-100 text-gray-500 px-2 py-1 rounded-full text-xs">
                        暂无常用地址
                      </span>
                    )}
                  </div>
                  
                  {/* 常用地址列表 */}
                  {addresses.length > 0 && selectedAddressId !== null && (
                    <div className="ml-6 space-y-3 bg-gray-50 p-4 rounded-lg">
                      {addresses.map((address) => (
                        <div key={address.id}>
                          {editingAddressId === address.id ? (
                            /* 编辑地址表单 */
                            <div className="p-4 border border-purple-300 rounded-lg bg-white">
                              <div className="flex items-center justify-between mb-4">
                                <h4 className="font-medium text-gray-900">编辑地址</h4>
                                <div className="flex space-x-2">
                                  <button
                                    onClick={saveEditAddress}
                                    className="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700"
                                  >
                                    保存
                                  </button>
                                  <button
                                    onClick={cancelEditAddress}
                                    className="px-3 py-1 bg-gray-300 text-gray-700 text-sm rounded hover:bg-gray-400"
                                  >
                                    取消
                                  </button>
                                </div>
                              </div>
                              
                              <div className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                      收货人姓名 <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                      type="text"
                                      value={editAddress.receiverName}
                                      onChange={(e) => setEditAddress({...editAddress, receiverName: e.target.value})}
                                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                      placeholder="请输入收货人姓名"
                                    />
                                  </div>
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                      联系电话 <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                      type="tel"
                                      value={editAddress.phone}
                                      onChange={(e) => setEditAddress({...editAddress, phone: e.target.value})}
                                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                      placeholder="请输入手机号码"
                                    />
                                  </div>
                                </div>
                                
                                {/* 省市区三级联动选择器 */}
                                <div>
                                  <AddressPicker
                                    value={{
                                      province: editAddress.province,
                                      city: editAddress.city,
                                      district: editAddress.district
                                    }}
                                    onChange={({ province, city, district }: { province: string; city: string; district: string }) => {
                                      setEditAddress({
                                        ...editAddress,
                                        province,
                                        city,
                                        district
                                      });
                                    }}
                                  />
                                </div>
                                
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    详细地址 <span className="text-red-500">*</span>
                                  </label>
                                  <textarea
                                    value={editAddress.detailAddress}
                                    onChange={(e) => setEditAddress({...editAddress, detailAddress: e.target.value})}
                                    rows={3}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                    placeholder="请输入详细地址，如：街道、门牌号、楼层等"
                                  />
                                </div>
                              </div>
                            </div>
                          ) : (
                            /* 普通地址显示 */
                            <label 
                              className={`flex items-start space-x-3 p-3 border rounded-lg cursor-pointer transition-all ${
                                selectedAddressId === address.id 
                                  ? 'border-purple-300 bg-white shadow-sm' 
                                  : 'border-gray-200 bg-white hover:border-gray-300'
                              }`}
                            >
                              <input
                                type="radio"
                                name="savedAddressChoice"
                                value={address.id}
                                checked={selectedAddressId === address.id}
                                onChange={() => setSelectedAddressId(address.id)}
                                className="mt-1 w-4 h-4 text-purple-600"
                              />
                              <div className="flex-1">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <div className="flex items-center space-x-2 mb-1">
                                      <span className="font-medium">{address.receiverName}</span>
                                      <span className="text-gray-600">{address.phone}</span>
                                      {address.isDefault && (
                                        <span className="bg-purple-100 text-purple-600 px-2 py-1 rounded-full text-xs">默认</span>
                                      )}
                                    </div>
                                    <p className="text-gray-600 text-sm">
                                      {address.province}{address.city}{address.district}{address.detailAddress}
                                    </p>
                                  </div>
                                  <button
                                    onClick={(e) => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      startEditAddress(address);
                                    }}
                                    className="ml-4 px-3 py-1 text-purple-600 hover:text-purple-700 text-sm border border-purple-300 rounded hover:bg-purple-50 transition-colors"
                                  >
                                    编辑
                                  </button>
                                </div>
                              </div>
                            </label>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {/* 如果没有常用地址，显示提示 */}
                  {addresses.length === 0 && selectedAddressId !== null && (
                    <div className="ml-6 p-4 bg-gray-50 rounded-lg text-center text-gray-500">
                      <p>暂无保存的常用地址</p>
                      <p className="text-sm mt-1">您可以在下方填写新地址并保存为常用地址</p>
                    </div>
                  )}
                </div>

                {/* 新地址选项 */}
                <div>
                  <div className="flex items-center space-x-2 mb-3">
                    <input
                      type="radio"
                      name="addressType"
                      id="newAddress"
                      checked={selectedAddressId === null}
                      onChange={() => {
                        setSelectedAddressId(null);
                      }}
                      className="w-4 h-4 text-purple-600"
                    />
                    <label htmlFor="newAddress" className="font-medium text-gray-900 cursor-pointer">
                      使用新地址
                    </label>
                    <span className="text-xs text-gray-500">（填写后可保存为常用地址）</span>
                  </div>

                  {/* 新地址表单 */}
                  {selectedAddressId === null && (
                    <div className="ml-6 space-y-4 bg-gray-50 p-4 rounded-lg">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            收货人姓名 <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            value={newAddress.receiverName}
                            onChange={(e) => setNewAddress({...newAddress, receiverName: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                            placeholder="请输入收货人姓名"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            联系电话 <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="tel"
                            value={newAddress.phone}
                            onChange={(e) => setNewAddress({...newAddress, phone: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                            placeholder="请输入手机号码"
                          />
                        </div>
                      </div>
                      
                      {/* 省市区三级联动选择器 */}
                      <div>
                        <AddressPicker
                          value={{
                            province: newAddress.province,
                            city: newAddress.city,
                            district: newAddress.district
                          }}
                          onChange={({ province, city, district }: { province: string; city: string; district: string }) => {
                            setNewAddress({
                              ...newAddress,
                              province,
                              city,
                              district
                            });
                          }}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          详细地址 <span className="text-red-500">*</span>
                        </label>
                        <textarea
                          value={newAddress.detailAddress}
                          onChange={(e) => setNewAddress({...newAddress, detailAddress: e.target.value})}
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                          placeholder="请输入详细地址，如：街道、门牌号、楼层等"
                        />
                      </div>
                      
                      <div>
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={newAddress.saveAddress}
                            onChange={(e) => setNewAddress({...newAddress, saveAddress: e.target.checked})}
                            className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500"
                          />
                          <span className="text-sm text-gray-700">保存为常用地址</span>
                        </label>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 买家备注 */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-5 h-5 bg-yellow-100 rounded-full flex items-center justify-center">
                  <span className="text-yellow-600 text-xs">💬</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900">买家备注</h3>
                <span className="text-sm text-gray-500">（可选）</span>
              </div>
              <textarea
                value={buyerNote}
                onChange={(e) => setBuyerNote(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                placeholder="给卖家留言（可选）"
                maxLength={500}
              />
              <div className="text-xs text-gray-400 mt-1">{buyerNote.length}/500</div>
            </div>
          </div>

          {/* 订单摘要 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 sticky top-24">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 text-xs">💰</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900">订单摘要</h3>
              </div>
              
              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">商品数量</span>
                  <span className="text-gray-900">{orderQuantity} 件</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">商品小计</span>
                  <span className="text-gray-900">¥{calculateTotal().toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">运费</span>
                  <span className="text-green-600">免运费</span>
                </div>
                <div className="border-t pt-3">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-medium text-gray-900">实付金额</span>
                    <span className="text-2xl font-bold text-red-600">¥{calculateTotal().toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <button
                onClick={createOrder}
                disabled={submitting}
                className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-shadow disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {submitting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>创建订单中...</span>
                  </div>
                ) : (
                  '提交订单'
                )}
              </button>

              <div className="mt-4 space-y-2 text-xs text-gray-500">
                <p>• 支持支付宝、微信支付</p>
                <p>• 支持7天无理由退换货</p>
                <p>• 满199元免运费</p>
                <p>• 订单提交后请在10分钟内完成支付</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 