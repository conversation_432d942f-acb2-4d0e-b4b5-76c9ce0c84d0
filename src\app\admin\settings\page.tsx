'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdmin } from '../../../contexts/AdminContext';
import Link from 'next/link';

export default function AdminSettings() {
  const router = useRouter();
  const { admin, isLoading: adminLoading } = useAdmin();

  useEffect(() => {
    if (!adminLoading) {
      if (!admin) {
        router.push('/admin/login');
      }
    }
  }, [admin, adminLoading, router]);

  if (adminLoading) {
    return <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
    </div>;
  }

  if (!admin) {
    return null; // 会被useEffect重定向到登录页
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <nav className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-600 rounded-lg flex items-center justify-center text-white font-bold text-sm mr-3">
                管
              </Link>
              <h1 className="text-xl font-bold text-white">系统设置</h1>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        <aside className="w-64 bg-gray-800 min-h-screen pt-4">
          <nav className="px-4 space-y-2">
            <Link href="/admin/dashboard" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📊</span>
              仪表盘
            </Link>
            <Link href="/admin/users" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">👥</span>
              用户管理
            </Link>

            <Link href="/admin/products" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📦</span>
              商品管理
            </Link>
            <Link href="/admin/orders" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🛒</span>
              订单管理
            </Link>
            <Link href="/admin/customer-service" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">💬</span>
              客服中心
            </Link>
            <Link href="/admin/stores" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🏪</span>
              店铺管理
            </Link>
            <Link href="/admin/logistics" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🚚</span>
              物流管理
            </Link>
            <Link href="/admin/fund-management" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">💰</span>
              资金管理
            </Link>
            <Link href="/admin/settings" className="flex items-center px-4 py-2 text-white bg-red-600 rounded-lg">
              <span className="mr-3">⚙️</span>
              系统设置
            </Link>
          </nav>
        </aside>

        <main className="flex-1 p-6">
          <div className="bg-gray-800 rounded-lg p-8 border border-gray-700">
            <div className="text-center mb-8">
              <div className="text-6xl mb-4">⚙️</div>
              <h2 className="text-2xl font-bold text-white mb-4">系统设置</h2>
              <p className="text-gray-400">配置系统参数、管理权限和维护设置</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-gray-700 rounded-lg p-6">
                <div className="text-blue-400 text-3xl mb-3">🌐</div>
                <h3 className="text-white font-bold mb-2">网站配置</h3>
                <p className="text-gray-400 text-sm mb-4">网站标题、Logo、SEO设置</p>
                <Link href="/admin/settings/website-config" className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors inline-block">
                  配置
                </Link>
              </div>
              
              <div className="bg-gray-700 rounded-lg p-6">
                <div className="text-green-400 text-3xl mb-3">💳</div>
                <h3 className="text-white font-bold mb-2">支付设置</h3>
                <p className="text-gray-400 text-sm mb-4">支付宝、微信支付配置</p>
                <Link href="/admin/settings/payment-config" className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded transition-colors inline-block">
                  配置
                </Link>
              </div>
              
              <div className="bg-gray-700 rounded-lg p-6">
                <div className="text-yellow-400 text-3xl mb-3">📧</div>
                <h3 className="text-white font-bold mb-2">邮件设置</h3>
                <p className="text-gray-400 text-sm mb-4">SMTP配置、邮件模板</p>
                <button className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded transition-colors">
                  配置
                </button>
              </div>
              
              <div className="bg-gray-700 rounded-lg p-6">
                <div className="text-purple-400 text-3xl mb-3">🔐</div>
                <h3 className="text-white font-bold mb-2">安全设置</h3>
                <p className="text-gray-400 text-sm mb-4">密码策略、登录限制</p>
                <button className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded transition-colors">
                  配置
                </button>
              </div>
              
              <div className="bg-gray-700 rounded-lg p-6">
                <div className="text-red-400 text-3xl mb-3">🗄️</div>
                <h3 className="text-white font-bold mb-2">数据备份</h3>
                <p className="text-gray-400 text-sm mb-4">数据库备份与恢复</p>
                <button className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded transition-colors">
                  管理
                </button>
              </div>
              
              <div className="bg-gray-700 rounded-lg p-6">
                <div className="text-cyan-400 text-3xl mb-3">📊</div>
                <h3 className="text-white font-bold mb-2">系统监控</h3>
                <p className="text-gray-400 text-sm mb-4">性能监控、日志管理</p>
                <button className="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded transition-colors">
                  查看
                </button>
              </div>
            </div>

            <div className="mt-8 bg-gray-700 rounded-lg p-6">
              <h3 className="text-white font-bold mb-4">系统信息</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">系统版本:</span>
                    <span className="text-white">v2.1.0</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">数据库:</span>
                    <span className="text-green-400">MySQL 8.0</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">服务器:</span>
                    <span className="text-green-400">运行正常</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">最后备份:</span>
                    <span className="text-white">2024-06-20 03:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">存储空间:</span>
                    <span className="text-yellow-400">65% (125GB/192GB)</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">在线用户:</span>
                    <span className="text-blue-400">127 人</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
} 
    </div>
  );
} 