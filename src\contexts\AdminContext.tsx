'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface Admin {
  id: string;
  username: string;
  role: string;
  permissions: string[];
}

interface AdminContextType {
  admin: Admin | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
}

const AdminContext = createContext<AdminContextType | undefined>(undefined);

export function AdminProvider({ children }: { children: ReactNode }) {
  const [admin, setAdmin] = useState<Admin | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 检查本地存储的管理员登录状态
  useEffect(() => {
    const checkStoredAdmin = async () => {
      try {
        const savedAdmin = localStorage.getItem('admin');
        const savedToken = localStorage.getItem('admin_token');
        
        console.log('检查存储的管理员数据:', { savedAdmin: !!savedAdmin, savedToken: !!savedToken });
        
        if (savedAdmin && savedToken) {
          try {
            const adminData = JSON.parse(savedAdmin);
            console.log('解析到的管理员数据:', adminData);
            
            // 对于本地token，直接使用不验证
            if (savedToken.startsWith('local_admin_token_')) {
              console.log('使用本地管理员token');
              setAdmin(adminData);
              localStorage.setItem('user_type', 'admin');
              setIsLoading(false);
              return;
            }
            
            // 尝试验证远程token（如果后端可用）
            try {
              console.log('验证远程token...');
              const response = await fetch('http://localhost:8080/api/admin/verify', {
                headers: {
                  'Authorization': `Bearer ${savedToken}`,
                  'Content-Type': 'application/json'
                }
              });

              if (response.ok) {
                console.log('远程token验证成功');
                setAdmin(adminData);
                localStorage.setItem('user_type', 'admin');
              } else {
                console.log('远程token验证失败，清除数据');
                // token已过期，清除本地数据
                localStorage.removeItem('admin');
                localStorage.removeItem('admin_token');
                localStorage.removeItem('user_type');
              }
            } catch (error) {
              console.warn('后端服务不可用，使用本地管理员数据:', error);
              // 如果后端不可用，仍然使用本地数据
              setAdmin(adminData);
              localStorage.setItem('user_type', 'admin');
            }
          } catch (error) {
            console.error('解析管理员数据失败:', error);
            // 清除错误数据
            localStorage.removeItem('admin');
            localStorage.removeItem('admin_token');
            localStorage.removeItem('user_type');
          }
        } else {
          console.log('未找到存储的管理员数据');
        }
      } catch (error) {
        console.error('检查存储的管理员数据时出错:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkStoredAdmin();
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      console.log('尝试管理员登录:', { username });
      
      // 调用后端API登录
      const response = await fetch('http://localhost:8080/api/admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          password
        })
      });

      console.log('登录API响应状态:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('登录API响应数据:', result);
        
        // 处理不同的响应格式
        let adminData, token;
        if (result.success || result.code === 200) {
          // 成功响应
          const data = result.data || result;
          token = data.token;
          adminData = {
            id: data.adminId || 'admin_' + Date.now(),
            username: data.username || username,
            role: data.role || 'admin',
            permissions: data.permissions || [
              'user_management',
              'content_management', 
              'product_management',
              'order_management',
              'customer_service',
              'system_settings',
              'store_approval',
              'logistics_management'
            ]
          };
        } else {
          throw new Error(result.message || '登录失败');
        }

        // 保存登录信息
        setAdmin(adminData);
        localStorage.setItem('admin', JSON.stringify(adminData));
        localStorage.setItem('admin_token', token);
        localStorage.setItem('user_type', 'admin');
        
        console.log('管理员登录成功:', adminData);
        return true;
      } else {
        const errorText = await response.text();
        console.error('管理员登录失败:', errorText);
        
        // 如果是开发环境且为测试账号，使用本地验证
        if (username === 'admin' && password === 'admin123') {
          console.log('使用本地验证作为备用方案');
          const adminData: Admin = {
            id: 'admin_local',
            username: 'admin',
            role: 'super_admin',
            permissions: [
              'user_management',
              'content_management',
              'product_management',
              'order_management',
              'customer_service',
              'system_settings',
              'store_approval',
              'logistics_management'
            ]
          };
          
          const localToken = 'local_admin_token_' + Date.now();
          setAdmin(adminData);
          localStorage.setItem('admin', JSON.stringify(adminData));
          localStorage.setItem('admin_token', localToken);
          localStorage.setItem('user_type', 'admin');
          
          return true;
        }
      }
      
      return false;
    } catch (error) {
      console.error('管理员登录请求失败:', error);
      
      // 网络错误时的备用验证
      if (username === 'admin' && password === 'admin123') {
        console.log('网络错误，使用本地验证');
        const adminData: Admin = {
          id: 'admin_local',
          username: 'admin',
          role: 'super_admin',
          permissions: [
            'user_management',
            'content_management',
            'product_management', 
            'order_management',
            'customer_service',
            'system_settings',
            'store_approval',
            'logistics_management'
          ]
        };
        
        const localToken = 'local_admin_token_' + Date.now();
        setAdmin(adminData);
        localStorage.setItem('admin', JSON.stringify(adminData));
        localStorage.setItem('admin_token', localToken);
        localStorage.setItem('user_type', 'admin');
        
        return true;
      }
      
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    console.log('管理员退出登录');
    setAdmin(null);
    localStorage.removeItem('admin');
    localStorage.removeItem('admin_token');
    localStorage.removeItem('user_type');
  };

  return (
    <AdminContext.Provider value={{ admin, login, logout, isLoading }}>
      {children}
    </AdminContext.Provider>
  );
}

export function useAdmin() {
  const context = useContext(AdminContext);
  if (context === undefined) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
} 