'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdmin } from '../../../contexts/AdminContext';
import Link from 'next/link';
import ImageWithFallback from '@/components/ui/ImageWithFallback';

// API配置
const API_BASE_URL = 'http://localhost:8080/api';

interface ShopApplication {
  shopId: string;
  userId: string;
  username: string;
  email: string;
  shopName: string;
  shopDescription?: string;
  contactName?: string;
  contactPhone?: string;
  contactEmail?: string;
  businessAddress?: string;
  shopStyle: string;
  hasDesigner: boolean;
  designFiles: string[];
  productionFiles: string[];
  worksPath: string[]; // 兼容旧版本
  status: string;
  applyTime: string;
  reviewTime?: string;
  reviewComment?: string;
}

interface ShopStats {
  pending: number;
  approved: number;
  rejected: number;
  total: number;
}

export default function AdminStores() {
  const router = useRouter();
  const { admin, isLoading: adminLoading } = useAdmin();
  const [loading, setLoading] = useState(true);
  const [applications, setApplications] = useState<ShopApplication[]>([]);
  const [stats, setStats] = useState<ShopStats>({ pending: 0, approved: 0, rejected: 0, total: 0 });
  const [activeTab, setActiveTab] = useState<'pending' | 'approved' | 'rejected' | 'all'>('pending');
  const [selectedApp, setSelectedApp] = useState<ShopApplication | null>(null);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [reviewForm, setReviewForm] = useState({
    action: 'APPROVE' as 'APPROVE' | 'REJECT',
    comment: ''
  });
  const [processing, setProcessing] = useState(false);

  // 获取店铺统计数据
  const fetchStats = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      if (!token) {
        console.error('未找到管理员token');
        return;
      }

      console.log('正在获取店铺统计数据...');
      const response = await fetch(`${API_BASE_URL}/admin/shop/applications/stats`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('统计API响应状态:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('统计API响应数据:', result);
        
        // 兼容多种API响应格式
        const isSuccess = result.success || (result.code === 200);
        const statsData = result.data || {};
        
        if (isSuccess && statsData) {
          setStats(statsData);
        } else {
          console.error('统计数据格式错误:', result);
          // 设置默认值
          setStats({ pending: 0, approved: 0, rejected: 0, total: 0 });
        }
      } else {
        const errorText = await response.text();
        console.error('获取统计数据失败:', errorText);
        // 设置默认值，避免页面崩溃
        setStats({ pending: 0, approved: 0, rejected: 0, total: 0 });
      }
    } catch (error) {
      console.error('获取统计数据网络错误:', error);
      // 设置默认值，避免页面崩溃
      setStats({ pending: 0, approved: 0, rejected: 0, total: 0 });
    }
  };

  // 获取店铺申请列表
  const fetchApplications = async (status?: string) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin_token');
      if (!token) {
        console.error('未找到管理员token');
        setApplications([]);
        return;
      }

      let url = `${API_BASE_URL}/admin/shop/applications`;
      if (status && status !== 'all') {
        url += `?status=${status.toUpperCase()}`;
      }

      console.log('正在获取店铺申请列表:', url);
      console.log('使用token:', token.substring(0, 30) + '...');
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('申请列表API响应状态:', response.status, response.statusText);

      if (response.ok) {
        const text = await response.text();
        console.log('申请列表API原始响应:', text);
        
        let result;
        try {
          result = JSON.parse(text);
        } catch (parseError) {
          console.error('JSON解析失败:', parseError);
          throw new Error('服务器响应格式错误');
        }
        
        console.log('申请列表API解析后数据:', result);
        
        // 兼容多种API响应格式
        const isSuccess = result.success || (result.code === 200);
        const responseData = result.data || [];
        
        if (isSuccess && responseData) {
          console.log('原始数据数组长度:', responseData.length);
          
          // 简化数据处理，减少转换错误
          const formattedApplications = (responseData || []).map((app: any, index: number) => {
            console.log(`处理第${index + 1}条记录:`, app);
            
                          const formatted = {
              shopId: app.shopId || app.shop_id || `unknown_${index}`,
              userId: app.userId || app.user_id || `unknown_user_${index}`,
              username: app.username || '未知用户',
              email: app.email || '未知邮箱',
              shopName: app.shopName || app.shop_name || '未知店铺',
              shopDescription: app.shopDescription || app.shop_description,
              contactName: app.contactName || app.contact_name,
              contactPhone: app.contactPhone || app.contact_phone,
              contactEmail: app.contactEmail || app.contact_email,
              businessAddress: app.businessAddress || app.business_address,
              shopStyle: app.shopStyle || app.shop_style || '未知风格',
              hasDesigner: app.hasDesigner !== undefined ? app.hasDesigner : (app.has_designer || false),
              designFiles: (() => {
                try {
                  if (Array.isArray(app.designFiles)) return app.designFiles;
                  if (Array.isArray(app.design_files)) return app.design_files;
                  if (typeof app.designFiles === 'string' && app.designFiles) {
                    return app.designFiles.split(',').filter(Boolean);
                  }
                  if (typeof app.design_files === 'string' && app.design_files) {
                    return app.design_files.split(',').filter(Boolean);
                  }
                  return [];
                } catch (e) {
                  console.warn('处理设计稿文件路径失败:', e);
                  return [];
                }
              })(),
              productionFiles: (() => {
                try {
                  if (Array.isArray(app.productionFiles)) return app.productionFiles;
                  if (Array.isArray(app.production_files)) return app.production_files;
                  if (typeof app.productionFiles === 'string' && app.productionFiles) {
                    return app.productionFiles.split(',').filter(Boolean);
                  }
                  if (typeof app.production_files === 'string' && app.production_files) {
                    return app.production_files.split(',').filter(Boolean);
                  }
                  return [];
                } catch (e) {
                  console.warn('处理生产证明文件路径失败:', e);
                  return [];
                }
              })(),
              worksPath: (() => {
                try {
                  if (Array.isArray(app.worksPath)) return app.worksPath;
                  if (Array.isArray(app.works_path)) return app.works_path;
                  if (typeof app.worksPath === 'string' && app.worksPath) {
                    return app.worksPath.split(',').filter(Boolean);
                  }
                  if (typeof app.works_path === 'string' && app.works_path) {
                    return app.works_path.split(',').filter(Boolean);
                  }
                  return [];
                } catch (e) {
                  console.warn('处理作品路径失败:', e);
                  return [];
                }
              })(),
              status: app.status || 'UNKNOWN',
              applyTime: app.applyTime || app.apply_time || new Date().toISOString(),
              reviewTime: app.reviewTime || app.review_time,
              reviewComment: app.reviewComment || app.review_comment
            };
            
            console.log(`格式化第${index + 1}条记录:`, formatted);
            return formatted;
          });
          
          console.log('最终格式化数据:', formattedApplications);
          setApplications(formattedApplications);
        } else {
          console.error('API返回数据格式错误:', result);
          setApplications([]);
        }
      } else {
        const errorText = await response.text();
        console.error('API调用失败:', response.status, errorText);
        setApplications([]);
      }
    } catch (error) {
      console.error('获取申请列表网络错误:', error);
      setApplications([]);
    } finally {
      setLoading(false);
    }
  };

  // 审核店铺申请
  const handleReview = async () => {
    if (!selectedApp) return;

    try {
      setProcessing(true);
      const token = localStorage.getItem('admin_token');
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/admin/shop/applications/${selectedApp.shopId}/review`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: reviewForm.action,
          comment: reviewForm.comment
        })
      });

      if (!response.ok) {
        const errorResult = await response.json();
        throw new Error(errorResult.message || `审核失败，状态码：${response.status}`);
      }

      const result = await response.json();

      // 兼容多种API响应格式
      const isSuccess = result.success || (result.code === 200);

      if (isSuccess) {
        alert(`店铺审核${reviewForm.action === 'APPROVE' ? '通过' : '拒绝'}成功！`);
        setShowReviewModal(false);
        setSelectedApp(null);
        setReviewForm({ action: 'APPROVE', comment: '' });
        
        // 重新获取数据以保持数据一致性
        await Promise.all([
          fetchApplications(activeTab === 'all' ? undefined : activeTab),
          fetchStats()
        ]);
      } else {
        throw new Error(result.message || '审核失败');
      }
    } catch (error) {
      console.error('审核失败:', error);
      alert(`审核失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setProcessing(false);
    }
  };

  useEffect(() => {
    if (!adminLoading) {
      if (admin) {
        fetchStats();
        fetchApplications(activeTab === 'all' ? undefined : activeTab);
      } else {
      router.push('/admin/login');
      }
    }
  }, [admin, adminLoading, router, activeTab]);

  const filteredApplications = applications;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'APPROVED':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'REJECTED':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING':
        return '待审核';
      case 'APPROVED':
        return '已通过';
      case 'REJECTED':
        return '已拒绝';
      default:
        return '未知';
    }
  };

  // 添加展示用户上传图片的组件
  const ImagePreview = ({ imagePath, title }: { imagePath: string, title: string }) => {
    if (!imagePath) return null;
    
    return (
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">{title}</h4>
        <div className="relative bg-gray-100 rounded-lg p-2 w-full h-40">
          <ImageWithFallback
            src={imagePath}
            alt={title}
            className="w-full h-full object-contain rounded-lg"
          />
        </div>
      </div>
    );
  };

  if (adminLoading) {
    return <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
    </div>;
  }

  if (!admin) {
    return null; // 会被useEffect重定向到登录页
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* 顶部导航 */}
      <nav className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-600 rounded-lg flex items-center justify-center text-white font-bold text-sm mr-3">
                管
              </Link>
              <h1 className="text-xl font-bold text-white">店铺管理</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/admin/notifications"
                className="text-gray-300 hover:text-white transition-colors"
              >
                <span className="text-xl">🔔</span>
              </Link>
              <span className="text-gray-300">管理员：{admin.username}</span>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* 侧边栏 */}
        <aside className="w-64 bg-gray-800 min-h-screen pt-4">
          <nav className="px-4 space-y-2">
            <Link href="/admin/dashboard" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📊</span>
              仪表盘
            </Link>
            <Link href="/admin/users" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">👥</span>
              用户管理
            </Link>

            <Link href="/admin/products" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">📦</span>
              商品管理
            </Link>
            <Link href="/admin/orders" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🛒</span>
              订单管理
            </Link>
            <Link href="/admin/customer-service" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">💬</span>
              客服中心
            </Link>
            <Link href="/admin/stores" className="flex items-center px-4 py-2 text-white bg-red-600 rounded-lg">
              <span className="mr-3">🏪</span>
              店铺管理
            </Link>
            <Link href="/admin/logistics" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">🚚</span>
              物流管理
            </Link>
            <Link href="/admin/settings" className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <span className="mr-3">⚙️</span>
              系统设置
            </Link>
          </nav>
        </aside>

        {/* 主内容区 */}
        <main className="flex-1 p-6">
          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-100 text-sm font-medium">待审核</p>
                  <p className="text-3xl font-bold">{stats.pending}</p>
                </div>
                <div className="text-4xl opacity-80">⏳</div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm font-medium">已通过</p>
                  <p className="text-3xl font-bold">{stats.approved}</p>
                </div>
                <div className="text-4xl opacity-80">✅</div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-red-500 to-pink-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-100 text-sm font-medium">已拒绝</p>
                  <p className="text-3xl font-bold">{stats.rejected}</p>
                </div>
                <div className="text-4xl opacity-80">❌</div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm font-medium">总申请</p>
                  <p className="text-3xl font-bold">{stats.total}</p>
                </div>
                <div className="text-4xl opacity-80">🏪</div>
              </div>
            </div>
          </div>

          {/* 筛选标签 */}
          <div className="bg-gray-800 rounded-2xl p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-white">店铺申请管理</h2>
              <button
                onClick={() => fetchApplications(activeTab === 'all' ? undefined : activeTab)}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                🔄 刷新
              </button>
            </div>
            
            <div className="flex space-x-2">
              {[
                { key: 'pending', label: '待审核', count: stats.pending },
                { key: 'approved', label: '已通过', count: stats.approved },
                { key: 'rejected', label: '已拒绝', count: stats.rejected },
                { key: 'all', label: '全部', count: stats.total }
              ].map(tab => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    activeTab === tab.key
                      ? 'bg-red-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  {tab.label} ({tab.count})
                </button>
              ))}
            </div>
          </div>

          {/* 申请列表 */}
          <div className="bg-gray-800 rounded-2xl overflow-hidden">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
                <span className="ml-3 text-gray-300">加载中...</span>
              </div>
            ) : filteredApplications.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📭</div>
                <h3 className="text-xl font-bold text-white mb-2">暂无申请</h3>
                <p className="text-gray-400">当前没有{activeTab === 'all' ? '' : getStatusText(activeTab.toUpperCase())}的店铺申请</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-700">
                    <tr>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">申请人</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">店铺信息</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">申请时间</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">状态</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">操作</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700">
                    {filteredApplications.map((app) => (
                      <tr key={app.shopId} className="hover:bg-gray-700/50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-white">{app.username}</div>
                            <div className="text-sm text-gray-400">{app.email}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-white">{app.shopName}</div>
                            <div className="text-sm text-gray-400">{app.shopStyle}</div>
                            <div className="text-xs text-gray-500 mt-1">
                              {app.hasDesigner ? '有设计师' : '无设计师'} • 
                              {app.designFiles && app.designFiles.length > 0 ? (
                                <span> {app.designFiles.length} 个设计稿</span>
                              ) : (
                                <span> {app.worksPath?.length || 0} 个作品</span>
                              )}
                              {app.productionFiles && app.productionFiles.length > 0 && (
                                <span> • {app.productionFiles.length} 个证明</span>
                              )}
                            </div>
                            {app.contactName && (
                              <div className="text-xs text-gray-500 mt-1">
                                联系人：{app.contactName}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {new Date(app.applyTime).toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(app.status)}`}>
                            {getStatusText(app.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                          <button
                            onClick={() => {
                              setSelectedApp(app);
                              setShowReviewModal(true);
                              setReviewForm({ action: 'APPROVE', comment: '' });
                            }}
                            className="text-blue-400 hover:text-blue-300 transition-colors"
                          >
                            查看详情
                          </button>
                          {app.status === 'PENDING' && (
                            <>
                              <button
                                onClick={() => {
                                  setSelectedApp(app);
                                  setReviewForm({ action: 'APPROVE', comment: '' });
                                  setShowReviewModal(true);
                                }}
                                className="text-green-400 hover:text-green-300 transition-colors"
                              >
                                批准
                              </button>
                              <button
                                onClick={() => {
                                  setSelectedApp(app);
                                  setReviewForm({ action: 'REJECT', comment: '' });
                                  setShowReviewModal(true);
                                }}
                                className="text-red-400 hover:text-red-300 transition-colors"
                              >
                                拒绝
                              </button>
                            </>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </main>
      </div>

      {/* 审核模态框 */}
      {showReviewModal && selectedApp && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-2xl p-8 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-white">
                {reviewForm.action === 'APPROVE' ? '审核通过' : '拒绝申请'} - {selectedApp.shopName}
              </h3>
              <button
                onClick={() => {
                  setShowReviewModal(false);
                  setSelectedApp(null);
                }}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 申请信息 */}
              <div className="space-y-6">
                <div className="bg-gray-700 rounded-xl p-6">
                  <h4 className="text-lg font-semibold text-white mb-4">申请人信息</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm text-gray-400">用户名</label>
                      <p className="text-white font-medium">{selectedApp.username}</p>
                    </div>
                    <div>
                      <label className="text-sm text-gray-400">邮箱</label>
                      <p className="text-white font-medium">{selectedApp.email}</p>
                    </div>
                    <div>
                      <label className="text-sm text-gray-400">申请时间</label>
                      <p className="text-white font-medium">{new Date(selectedApp.applyTime).toLocaleString()}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-700 rounded-xl p-6">
                  <h4 className="text-lg font-semibold text-white mb-4">店铺信息</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm text-gray-400">店铺名称</label>
                      <p className="text-white font-medium">{selectedApp.shopName}</p>
                    </div>
                    {selectedApp.shopDescription && (
                      <div>
                        <label className="text-sm text-gray-400">店铺描述</label>
                        <p className="text-white font-medium">{selectedApp.shopDescription}</p>
                      </div>
                    )}
                    <div>
                      <label className="text-sm text-gray-400">店铺风格</label>
                      <p className="text-white font-medium">{selectedApp.shopStyle}</p>
                    </div>
                    <div>
                      <label className="text-sm text-gray-400">入驻设计师</label>
                      <p className="text-white font-medium">{selectedApp.hasDesigner ? '是' : '否'}</p>
                    </div>
                  </div>
                </div>

                {/* 联系信息 */}
                {(selectedApp.contactName || selectedApp.contactPhone || selectedApp.contactEmail || selectedApp.businessAddress) && (
                  <div className="bg-gray-700 rounded-xl p-6">
                    <h4 className="text-lg font-semibold text-white mb-4">联系信息</h4>
                    <div className="space-y-3">
                      {selectedApp.contactName && (
                        <div>
                          <label className="text-sm text-gray-400">联系人</label>
                          <p className="text-white font-medium">{selectedApp.contactName}</p>
                        </div>
                      )}
                      {selectedApp.contactPhone && (
                        <div>
                          <label className="text-sm text-gray-400">联系电话</label>
                          <p className="text-white font-medium">{selectedApp.contactPhone}</p>
                        </div>
                      )}
                      {selectedApp.contactEmail && (
                        <div>
                          <label className="text-sm text-gray-400">联系邮箱</label>
                          <p className="text-white font-medium">{selectedApp.contactEmail}</p>
                        </div>
                      )}
                      {selectedApp.businessAddress && (
                        <div>
                          <label className="text-sm text-gray-400">经营地址</label>
                          <p className="text-white font-medium">{selectedApp.businessAddress}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* 设计稿文件 */}
                {selectedApp.designFiles && selectedApp.designFiles.length > 0 && (
                  <div className="bg-gray-700 rounded-xl p-6">
                    <h4 className="text-lg font-semibold text-white mb-4">设计稿文件 ({selectedApp.designFiles.length})</h4>
                    <div className="grid grid-cols-2 gap-3">
                      {selectedApp.designFiles.map((file, index) => (
                        <div key={`design-${index}`} className="bg-gray-600 rounded-lg p-3">
                          <div className="aspect-square bg-gray-800/50 rounded-lg flex items-center justify-center mb-2 overflow-hidden">
                            <ImageWithFallback
                              src={`http://localhost:8080/api/materials/image/file/${file.split('/').pop()}`}
                              alt={`设计稿 ${index + 1}`}
                              className="w-full h-full object-contain rounded-lg"
                              onClick={() => {
                                window.open(`http://localhost:8080/api/materials/image/file/${file.split('/').pop()}`, '_blank');
                              }}
                            />
                          </div>
                          <p className="text-xs text-gray-300 truncate text-center" title={file}>{file.split('/').pop()}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 生产证明文件 */}
                {selectedApp.productionFiles && selectedApp.productionFiles.length > 0 && (
                  <div className="bg-gray-700 rounded-xl p-6">
                    <h4 className="text-lg font-semibold text-white mb-4">生产证明文件 ({selectedApp.productionFiles.length})</h4>
                    <div className="grid grid-cols-2 gap-3">
                      {selectedApp.productionFiles.map((file, index) => (
                        <div key={`production-${index}`} className="bg-gray-600 rounded-lg p-3">
                          {file.toLowerCase().includes('.pdf') ? (
                            <div className="aspect-square bg-red-900/30 rounded-lg flex items-center justify-center mb-2">
                              <svg className="w-12 h-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            </div>
                          ) : (
                            <div className="aspect-square bg-gray-800/50 rounded-lg flex items-center justify-center mb-2 overflow-hidden">
                              <ImageWithFallback
                                src={`http://localhost:8080/api/materials/image/file/${file.split('/').pop()}`}
                                alt={`生产证明 ${index + 1}`}
                                className="w-full h-full object-contain rounded-lg"
                                onClick={() => {
                                  window.open(`http://localhost:8080/api/materials/image/file/${file.split('/').pop()}`, '_blank');
                                }}
                              />
                            </div>
                          )}
                          <p className="text-xs text-gray-300 truncate text-center" title={file}>{file.split('/').pop()}</p>
                          <p className="text-xs text-gray-500 mt-1 text-center">
                            {file.toLowerCase().includes('.pdf') ? 'PDF文档' : '图片文件'}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 兼容旧版本的申请作品 */}
                {selectedApp.worksPath && selectedApp.worksPath.length > 0 && (!selectedApp.designFiles || selectedApp.designFiles.length === 0) && (
                <div className="bg-gray-700 rounded-xl p-6">
                  <h4 className="text-lg font-semibold text-white mb-4">申请作品 ({selectedApp.worksPath.length})</h4>
                  <div className="grid grid-cols-2 gap-3">
                    {selectedApp.worksPath.map((work, index) => (
                      <div key={`work-${index}`} className="bg-gray-600 rounded-lg p-3">
                        <div className="aspect-square bg-gray-800/50 rounded-lg flex items-center justify-center mb-2 overflow-hidden">
                          <ImageWithFallback
                            src={`http://localhost:8080/api/materials/image/file/${work.split('/').pop()}`}
                            alt={`旧版本作品 ${index + 1}`}
                            className="w-full h-full object-contain rounded-lg"
                            onClick={() => {
                              window.open(`http://localhost:8080/api/materials/image/file/${work.split('/').pop()}`, '_blank');
                            }}
                          />
                        </div>
                        <p className="text-xs text-gray-300 truncate text-center" title={work}>{work.split('/').pop()}</p>
                      </div>
                    ))}
                  </div>
                </div>
                )}
              </div>

              {/* 审核操作 */}
              <div className="space-y-6">
                <div className="bg-gray-700 rounded-xl p-6">
                  <h4 className="text-lg font-semibold text-white mb-4">审核操作</h4>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">审核结果</label>
                      <div className="space-y-2">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            value="APPROVE"
                            checked={reviewForm.action === 'APPROVE'}
                            onChange={(e) => setReviewForm({...reviewForm, action: e.target.value as any})}
                            className="mr-3 text-green-500"
                          />
                          <span className="text-green-400 font-medium">✅ 通过审核</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            value="REJECT"
                            checked={reviewForm.action === 'REJECT'}
                            onChange={(e) => setReviewForm({...reviewForm, action: e.target.value as any})}
                            className="mr-3 text-red-500"
                          />
                          <span className="text-red-400 font-medium">❌ 拒绝申请</span>
                        </label>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        审核意见 {reviewForm.action === 'REJECT' && <span className="text-red-400">*</span>}
                      </label>
                      <textarea
                        value={reviewForm.comment}
                        onChange={(e) => setReviewForm({...reviewForm, comment: e.target.value})}
                        rows={4}
                        className="w-full px-4 py-3 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                        placeholder={
                          reviewForm.action === 'APPROVE' 
                            ? '恭喜通过审核！您可以添加一些鼓励的话...' 
                            : '请说明拒绝的原因，以便申请人改进...'
                        }
                        required={reviewForm.action === 'REJECT'}
                      />
                    </div>

                    {reviewForm.action === 'APPROVE' && (
                      <div className="bg-green-900/30 border border-green-600 rounded-lg p-4">
                        <h5 className="text-green-400 font-semibold mb-2">通过后的权限</h5>
                        <ul className="text-green-300 text-sm space-y-1">
                          <li>• 可以上传和销售商品</li>
                          <li>• 可以管理店铺设置</li>
                          <li>• 可以查看订单和收益</li>
                          <li>• 可以参与平台推广活动</li>
                        </ul>
                      </div>
                    )}

                    {reviewForm.action === 'REJECT' && (
                      <div className="bg-red-900/30 border border-red-600 rounded-lg p-4">
                        <h5 className="text-red-400 font-semibold mb-2">拒绝原因示例</h5>
                        <ul className="text-red-300 text-sm space-y-1">
                          <li>• 上传作品质量不符合要求</li>
                          <li>• 店铺信息不完整或不真实</li>
                          <li>• 作品涉嫌抄袭或版权问题</li>
                          <li>• 不符合平台经营规范</li>
                        </ul>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex space-x-4">
                  <button
                    onClick={handleReview}
                    disabled={processing || (reviewForm.action === 'REJECT' && !reviewForm.comment.trim())}
                    className={`flex-1 font-semibold py-3 px-6 rounded-xl transition-all duration-200 ${
                      reviewForm.action === 'APPROVE'
                        ? 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white'
                        : 'bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white'
                    } disabled:opacity-50 disabled:cursor-not-allowed`}
                  >
                    {processing ? (
                      <div className="flex items-center justify-center">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        处理中...
                      </div>
                    ) : (
                      `确认${reviewForm.action === 'APPROVE' ? '通过' : '拒绝'}`
                    )}
                  </button>
                  <button
                    onClick={() => {
                      setShowReviewModal(false);
                      setSelectedApp(null);
                    }}
                    disabled={processing}
                    className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200"
                  >
                    取消
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 