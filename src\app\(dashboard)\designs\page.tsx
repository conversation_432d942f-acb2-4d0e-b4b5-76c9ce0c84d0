'use client';

import React, { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';

// 设计数据接口
interface Design {
  designId: string;
  designName: string;
  description: string;
  designData: string;
  layoutId: number;
  status: string;
  isPublished: boolean;
  createTime: string;
  updateTime: string;
  likeCount: number;
  viewCount: number;
}

// 键盘预览组件Props类型
interface KeyboardPreviewProps {
  designData: string;
  layoutId: string;
  width?: number;
  height?: number;
  className?: string;
}

const KeyboardPreview = ({ designData, layoutId, width = 400, height = 116, className = '' }: KeyboardPreviewProps) => {
  // 解析设计数据
  const parsedData = useMemo(() => {
    try {
      return JSON.parse(designData || '{}');
    } catch (error) {
      return { elements: [] };
    }
  }, [designData]);

  // 3D键帽渲染函数（与设计器完全一致）
  const render3DKeycap = (x: number, y: number, width: number, height: number, text: string, keyId: string, fontSize: number = 11) => {
    return (
      <g key={keyId} transform={`translate(${x},${y})`}>
        {/* 外轮廓 - 白底黑边 */}
        <rect
          x={0} y={0} width={width} height={height} rx={3}
          fill="#ffffff"
          stroke="#000000"
          strokeWidth={1}
        />
        {/* 内部斜边线条 - 四个角的立体效果 */}
        <polyline points={`7,1 1,1 1,7`} stroke="#000000" strokeWidth="1" fill="none"/>
        <polyline points={`${width-1-7},1 ${width-1},1 ${width-1},7`} stroke="#000000" strokeWidth="1" fill="none"/>
        <polyline points={`1,${height-1-7} 1,${height-1} 7,${height-1}`} stroke="#000000" strokeWidth="1" fill="none"/>
        <polyline points={`${width-1},${height-1-7} ${width-1},${height-1} ${width-1-7},${height-1}`} stroke="#000000" strokeWidth="1" fill="none"/>
        {/* 文字内容 */}
        <text
          x={width/2} y={height/2}
          textAnchor="middle"
          fontFamily="'Orbitron', 'Arial Black', 'Arial Narrow', Arial, sans-serif"
          fontWeight="bold"
          fontSize={fontSize}
          fill="#000000"
          dominantBaseline="middle"
        >{text}</text>
      </g>
    );
  };

  // 与设计器完全一致的键盘布局渲染
  const renderKeyboardLayout = (layout: string) => {
    // 直接用设计器的布局渲染，不做缩放/偏移
    switch (layout) {
      case '108':
      case '104':
        return (
          <g>
            {/* 第一行 - ESC和功能键 */}
            {render3DKeycap(10, 10, 48, 36, 'ESC', 'preview-esc', 9)}
            {['F1', 'F2', 'F3', 'F4'].map((key, i) => render3DKeycap(70 + i * 52, 10, 48, 36, key, `preview-${key.toLowerCase()}`, 9))}
            {['F5', 'F6', 'F7', 'F8'].map((key, i) => render3DKeycap(290 + i * 52, 10, 48, 36, key, `preview-${key.toLowerCase()}`, 9))}
            {['F9', 'F10', 'F11', 'F12'].map((key, i) => render3DKeycap(510 + i * 52, 10, 48, 36, key, `preview-${key.toLowerCase()}`, 9))}
            {render3DKeycap(730, 10, 48, 36, 'PRT', 'key-prt', 8)}
            {render3DKeycap(785, 10, 48, 36, 'SCR', 'key-scr', 8)}
            {render3DKeycap(840, 10, 48, 36, 'PAU', 'key-pau', 8)}
            {/* 第二行 - 数字键 */}
            {render3DKeycap(10, 60, 48, 36, '~', 'preview-tilde', 10)}
            {['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='].map((key, i) => render3DKeycap(65 + i * 52, 60, 48, 36, key, `preview-${key}`, 10))}
            {render3DKeycap(689, 60, 72, 36, 'BACK', 'preview-backspace', 9)}
            {render3DKeycap(785, 60, 48, 36, 'INS', 'key-ins', 8)}
            {render3DKeycap(840, 60, 48, 36, 'HM', 'key-home', 8)}
            {render3DKeycap(895, 60, 48, 36, 'UP', 'key-pageup', 8)}
            {/* 第三行 - QWERTY */}
            {render3DKeycap(10, 105, 72, 36, 'TAB', 'preview-tab', 9)}
            {['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '[', ']', '\\'].map((key, i) => render3DKeycap(87 + i * 48, 105, 48, 36, key, `preview-${key.toLowerCase()}`, 10))}
            {render3DKeycap(785, 105, 48, 36, 'DEL', 'key-del', 8)}
            {render3DKeycap(840, 105, 48, 36, 'END', 'key-end', 8)}
            {render3DKeycap(895, 105, 48, 36, 'DN', 'key-pagedown', 8)}
            {/* 第四行 - ASDF */}
            {render3DKeycap(10, 150, 84, 36, 'CAPS', 'preview-caps', 8)}
            {['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', ';', "'"].map((key, i) => render3DKeycap(99 + i * 48, 150, 48, 36, key, `preview-${key.toLowerCase()}`, 10))}
            {render3DKeycap(627, 150, 84, 36, 'ENTER', 'preview-enter', 9)}
            {/* 第五行 - ZXCV */}
            {render3DKeycap(10, 195, 108, 36, 'SHIFT', 'preview-lshift', 8)}
            {['Z', 'X', 'C', 'V', 'B', 'N', 'M', ',', '.', '/'].map((key, i) => render3DKeycap(123 + i * 48, 195, 48, 36, key, `preview-${key.toLowerCase()}`, 10))}
            {render3DKeycap(603, 195, 108, 36, 'SHIFT', 'preview-rshift', 8)}
            {render3DKeycap(840, 195, 48, 36, '↑', 'key-up', 12)}
            {/* 第六行 - 底部控制键 */}
            {render3DKeycap(10, 240, 60, 36, 'CTRL', 'preview-lctrl', 8)}
            {render3DKeycap(75, 240, 60, 36, 'WIN', 'preview-lwin', 8)}
            {render3DKeycap(140, 240, 60, 36, 'ALT', 'preview-lalt', 8)}
            {render3DKeycap(205, 240, 264, 36, 'SPACE', 'preview-space', 10)}
            {render3DKeycap(474, 240, 60, 36, 'ALT', 'preview-ralt', 8)}
            {render3DKeycap(539, 240, 60, 36, 'FN', 'preview-fn', 8)}
            {render3DKeycap(604, 240, 60, 36, 'MENU', 'preview-menu', 8)}
            {render3DKeycap(669, 240, 60, 36, 'CTRL', 'preview-rctrl', 8)}
            {render3DKeycap(785, 240, 48, 36, '←', 'key-left', 12)}
            {render3DKeycap(840, 240, 48, 36, '↓', 'key-down', 12)}
            {render3DKeycap(895, 240, 48, 36, '→', 'key-right', 12)}
            {/* 数字小键盘 */}
            {render3DKeycap(970, 60, 48, 36, 'NUM', 'key-numlock', 8)}
            {render3DKeycap(1025, 60, 48, 36, '/', 'key-numdiv', 10)}
            {render3DKeycap(1080, 60, 48, 36, '*', 'key-nummul', 10)}
            {render3DKeycap(1135, 60, 48, 36, '-', 'key-numsub', 10)}
            {render3DKeycap(970, 105, 48, 36, '7', 'key-num7', 10)}
            {render3DKeycap(1025, 105, 48, 36, '8', 'key-num8', 10)}
            {render3DKeycap(1080, 105, 48, 81, '+', 'key-numadd', 14)}
            {render3DKeycap(970, 150, 48, 36, '4', 'key-num4', 10)}
            {render3DKeycap(1025, 150, 48, 36, '5', 'key-num5', 10)}
            {render3DKeycap(1080, 150, 48, 36, '6', 'key-num6', 10)}
            {render3DKeycap(970, 195, 48, 36, '1', 'key-num1', 10)}
            {render3DKeycap(1025, 195, 48, 36, '2', 'key-num2', 10)}
            {render3DKeycap(1080, 195, 48, 36, '3', 'key-num3', 10)}
            {render3DKeycap(1135, 195, 48, 81, 'ENT', 'key-numenter', 9)}
            {render3DKeycap(970, 240, 103, 36, '0', 'key-num0', 10)}
            {render3DKeycap(1080, 240, 48, 36, '.', 'key-numdot', 14)}
          </g>
        );
      default:
        return null;
    }
  };

  return (
    <div className={`relative ${className}`} style={{ width, height, overflow: 'hidden' }}>
      <svg
        viewBox="0 0 1200 320"
        width="100%"
        height="100%"
        style={{ display: 'block' }}
        preserveAspectRatio="xMidYMid meet"
      >
        {/* 键盘布局 - 添加与设计器相同的30px偏移 */}
        <g transform="translate(0, 30)">
          {renderKeyboardLayout(layoutId || '108')}
        </g>
        
        {/* 设计元素 - 直接使用原始坐标，不做任何变换 */}
        {parsedData.elements?.map((element: any) => (
          <image
            key={element.id}
            x={element.x}
            y={element.y}
            width={element.width}
            height={element.height}
            href={element.imageUrl || element.originalUrl}
            opacity={element.opacity || 1}
            style={{
              borderRadius: element.borderRadius || 0,
              transform: `rotate(${element.rotation || 0}deg)`,
              transformOrigin: 'center'
            }}
            onError={e => { e.currentTarget.style.display = 'none'; }}
          />
        ))}
        
        {/* 无设计内容提示 */}
        {(!parsedData.elements || parsedData.elements.length === 0) && (
          <text x="550" y="160" textAnchor="middle" fontSize="24" fill="#9ca3af" fontWeight="500">
            暂无设计内容
          </text>
        )}
      </svg>
    </div>
  );
};

// 3D键帽组件
const Keycap3D = ({ 
  x, 
  y, 
  width, 
  height, 
  text, 
  keyId, 
  fontSize = 11,
  designElements = [] 
}: {
  x: number;
  y: number;
  width: number;
  height: number;
  text: string;
  keyId: string;
  fontSize?: number;
  designElements?: any[];
}) => {
  const keycapDepth = 16; // 更深的3D深度
  
  // 查找应用到这个键帽的设计元素
  const keyElement = designElements.find(el => {
    const keycapCenterX = x + width / 2;
    const keycapCenterY = y + height / 2;
    
    return el.x <= keycapCenterX && 
           keycapCenterX <= el.x + el.width &&
           el.y <= keycapCenterY && 
           keycapCenterY <= el.y + el.height;
  });

  return (
    <div
      className="absolute"
      style={{
        left: `${x}px`,
        top: `${y}px`,
        width: `${width}px`,
        height: `${height}px`,
        transformStyle: 'preserve-3d'
      }}
    >
      {/* 键帽3D立体结构 */}
      <div
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          transformStyle: 'preserve-3d',
          transform: `translateZ(${keycapDepth / 2}px)`
        }}
      >
        {/* 键帽顶面 - 严格按照技术图尺寸比例 */}
        <div
          style={{
            position: 'absolute',
            width: `${width * (11.78 / 18.2)}px`,  // 精确顶面宽度比例 0.647
            height: `${height * (10.49 / 18.2)}px`, // 精确顶面高度比例 0.576
            left: `${(width - width * (11.78 / 18.2)) / 2}px`,  // 居中偏移
            top: `${height * 0.15}px`, // 顶面Y偏移，符合技术图深度感
            backgroundColor: keyElement ? 'transparent' : '#f8f9fa',
            backgroundImage: keyElement ? `url(${keyElement.imageUrl || keyElement.originalUrl})` : 'linear-gradient(145deg, #ffffff 0%, #f1f3f4 30%, #e8eaed 70%, #dadce0 100%)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            borderRadius: '6px',
            border: '2px solid #e5e7eb',
            boxShadow: `
              0 6px 12px rgba(0,0,0,0.2),
              inset 0 3px 0 rgba(255,255,255,0.9),
              inset 0 -2px 0 rgba(0,0,0,0.1),
              inset 3px 0 0 rgba(255,255,255,0.6),
              inset -3px 0 0 rgba(0,0,0,0.05)
            `,
            transform: `translateZ(${keycapDepth / 2}px)`,
            opacity: keyElement?.opacity || 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: `${fontSize}px`,
            fontWeight: 'bold',
            color: keyElement ? 'transparent' : '#374151',
            textShadow: keyElement ? 'none' : '0 1px 2px rgba(255,255,255,0.8)',
            userSelect: 'none',
            imageRendering: 'crisp-edges' as const
          }}
        >
          {!keyElement && text}
        </div>

        {/* 键帽前面 */}
        <div
          style={{
            position: 'absolute',
            width: '100%',
            height: `${keycapDepth}px`,
            background: keyElement 
              ? `linear-gradient(180deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.4) 100%)`
              : 'linear-gradient(180deg, #d1d5db 0%, #9ca3af 100%)',
            transform: `rotateX(-90deg) translateZ(${height}px)`,
            transformOrigin: 'top',
            borderRadius: '0 0 6px 6px',
            border: '1px solid #9ca3af'
          }}
        />

        {/* 键帽右面 */}
        <div
          style={{
            position: 'absolute',
            width: `${keycapDepth}px`,
            height: '100%',
            background: keyElement 
              ? `linear-gradient(90deg, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0.5) 100%)`
              : 'linear-gradient(90deg, #9ca3af 0%, #6b7280 100%)',
            transform: `rotateY(90deg) translateZ(${width}px)`,
            transformOrigin: 'left',
            borderRadius: '0 6px 6px 0',
            border: '1px solid #6b7280'
          }}
        />

        {/* 键帽底面 */}
        <div
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            backgroundColor: '#374151',
            transform: `translateZ(-${keycapDepth / 2}px)`,
            borderRadius: '6px',
            border: '1px solid #1f2937',
            boxShadow: '0 0 30px rgba(0,0,0,0.7)'
          }}
        />

        {/* 键帽左面 */}
        <div
          style={{
            position: 'absolute',
            width: `${keycapDepth}px`,
            height: '100%',
            background: keyElement 
              ? `linear-gradient(270deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.3) 100%)`
              : 'linear-gradient(270deg, #d1d5db 0%, #adb5bd 100%)',
            transform: `rotateY(-90deg) translateZ(0px)`,
            transformOrigin: 'right',
            borderRadius: '6px 0 0 6px',
            border: '1px solid #adb5bd'
          }}
        />

        {/* 键帽后面 */}
        <div
          style={{
            position: 'absolute',
            width: '100%',
            height: `${keycapDepth}px`,
            background: keyElement 
              ? `linear-gradient(0deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.15) 100%)`
              : 'linear-gradient(0deg, #adb5bd 0%, #ced4da 100%)',
            transform: `rotateX(90deg) translateZ(0px)`,
            transformOrigin: 'bottom',
            borderRadius: '6px 6px 0 0',
            border: '1px solid #ced4da'
          }}
        />
      </div>
    </div>
  );
};

// 3D键盘布局组件
const Keyboard3DLayout = ({ layoutId, designElements }: { layoutId: string, designElements: any[] }) => {
  // 调试信息
  console.log('3D Layout - layoutId:', layoutId);
  console.log('3D Layout - designElements:', designElements);
  
  const renderKeycap3D = (x: number, y: number, width: number, height: number, text: string, keyId: string, fontSize: number = 11) => {
    return (
      <Keycap3D
        key={keyId}
        x={x}
        y={y}
        width={width}
        height={height}
        text={text}
        keyId={keyId}
        fontSize={fontSize}
        designElements={designElements}
      />
    );
  };

  switch (layoutId) {
    case '108':
    case '104':
      return (
        <div className="relative" style={{ width: '1200px', height: '350px' }}>
          {/* 第一行 - ESC和功能键 */}
          {renderKeycap3D(10, 10, 48, 36, 'ESC', '3d-esc', 9)}
          {['F1', 'F2', 'F3', 'F4'].map((key, i) => renderKeycap3D(70 + i * 52, 10, 48, 36, key, `3d-${key.toLowerCase()}`, 9))}
          {['F5', 'F6', 'F7', 'F8'].map((key, i) => renderKeycap3D(290 + i * 52, 10, 48, 36, key, `3d-${key.toLowerCase()}`, 9))}
          {['F9', 'F10', 'F11', 'F12'].map((key, i) => renderKeycap3D(510 + i * 52, 10, 48, 36, key, `3d-${key.toLowerCase()}`, 9))}
          {renderKeycap3D(730, 10, 48, 36, 'PRT', '3d-prt', 8)}
          {renderKeycap3D(785, 10, 48, 36, 'SCR', '3d-scr', 8)}
          {renderKeycap3D(840, 10, 48, 36, 'PAU', '3d-pau', 8)}
          
          {/* 第二行 - 数字键 */}
          {renderKeycap3D(10, 60, 48, 36, '~', '3d-tilde', 10)}
          {['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='].map((key, i) => renderKeycap3D(65 + i * 52, 60, 48, 36, key, `3d-${key}`, 10))}
          {renderKeycap3D(689, 60, 72, 36, 'BACK', '3d-backspace', 9)}
          {renderKeycap3D(785, 60, 48, 36, 'INS', '3d-ins', 8)}
          {renderKeycap3D(840, 60, 48, 36, 'HM', '3d-home', 8)}
          {renderKeycap3D(895, 60, 48, 36, 'UP', '3d-pageup', 8)}
          
          {/* 第三行 - QWERTY */}
          {renderKeycap3D(10, 105, 72, 36, 'TAB', '3d-tab', 9)}
          {['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '[', ']', '\\'].map((key, i) => renderKeycap3D(87 + i * 48, 105, 48, 36, key, `3d-${key.toLowerCase()}`, 10))}
          {renderKeycap3D(785, 105, 48, 36, 'DEL', '3d-del', 8)}
          {renderKeycap3D(840, 105, 48, 36, 'END', '3d-end', 8)}
          {renderKeycap3D(895, 105, 48, 36, 'DN', '3d-pagedown', 8)}
          
          {/* 第四行 - ASDF */}
          {renderKeycap3D(10, 150, 84, 36, 'CAPS', '3d-caps', 8)}
          {['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', ';', "'"].map((key, i) => renderKeycap3D(99 + i * 48, 150, 48, 36, key, `3d-${key.toLowerCase()}`, 10))}
          {renderKeycap3D(627, 150, 84, 36, 'ENTER', '3d-enter', 9)}
          
          {/* 第五行 - ZXCV */}
          {renderKeycap3D(10, 195, 108, 36, 'SHIFT', '3d-lshift', 8)}
          {['Z', 'X', 'C', 'V', 'B', 'N', 'M', ',', '.', '/'].map((key, i) => renderKeycap3D(123 + i * 48, 195, 48, 36, key, `3d-${key.toLowerCase()}`, 10))}
          {renderKeycap3D(603, 195, 108, 36, 'SHIFT', '3d-rshift', 8)}
          {renderKeycap3D(840, 195, 48, 36, '↑', '3d-up', 12)}
          
          {/* 第六行 - 底部控制键 */}
          {renderKeycap3D(10, 240, 60, 36, 'CTRL', '3d-lctrl', 8)}
          {renderKeycap3D(75, 240, 60, 36, 'WIN', '3d-lwin', 8)}
          {renderKeycap3D(140, 240, 60, 36, 'ALT', '3d-lalt', 8)}
          {renderKeycap3D(205, 240, 264, 36, 'SPACE', '3d-space', 10)}
          {renderKeycap3D(474, 240, 60, 36, 'ALT', '3d-ralt', 8)}
          {renderKeycap3D(539, 240, 60, 36, 'FN', '3d-fn', 8)}
          {renderKeycap3D(604, 240, 60, 36, 'MENU', '3d-menu', 8)}
          {renderKeycap3D(669, 240, 60, 36, 'CTRL', '3d-rctrl', 8)}
          {renderKeycap3D(785, 240, 48, 36, '←', '3d-left', 12)}
          {renderKeycap3D(840, 240, 48, 36, '↓', '3d-down', 12)}
          {renderKeycap3D(895, 240, 48, 36, '→', '3d-right', 12)}
          
          {/* 数字小键盘 */}
          {renderKeycap3D(970, 60, 48, 36, 'NUM', '3d-numlock', 8)}
          {renderKeycap3D(1025, 60, 48, 36, '/', '3d-numdiv', 10)}
          {renderKeycap3D(1080, 60, 48, 36, '*', '3d-nummul', 10)}
          {renderKeycap3D(1135, 60, 48, 36, '-', '3d-numsub', 10)}
          {renderKeycap3D(970, 105, 48, 36, '7', '3d-num7', 10)}
          {renderKeycap3D(1025, 105, 48, 36, '8', '3d-num8', 10)}
          {renderKeycap3D(1080, 105, 48, 81, '+', '3d-numadd', 14)}
          {renderKeycap3D(970, 150, 48, 36, '4', '3d-num4', 10)}
          {renderKeycap3D(1025, 150, 48, 36, '5', '3d-num5', 10)}
          {renderKeycap3D(1080, 150, 48, 36, '6', '3d-num6', 10)}
          {renderKeycap3D(970, 195, 48, 36, '1', '3d-num1', 10)}
          {renderKeycap3D(1025, 195, 48, 36, '2', '3d-num2', 10)}
          {renderKeycap3D(1080, 195, 48, 36, '3', '3d-num3', 10)}
          {renderKeycap3D(1135, 195, 48, 81, 'ENT', '3d-numenter', 9)}
          {renderKeycap3D(970, 240, 103, 36, '0', '3d-num0', 10)}
          {renderKeycap3D(1080, 240, 48, 36, '.', '3d-numdot', 14)}
        </div>
      );
    
    case '87':
    case '80':
      return (
        <div className="relative" style={{ width: '900px', height: '350px' }}>
          {/* 第一行 - ESC和功能键 */}
          {renderKeycap3D(10, 10, 48, 36, 'ESC', '3d-esc', 9)}
          {['F1', 'F2', 'F3', 'F4'].map((key, i) => renderKeycap3D(70 + i * 52, 10, 48, 36, key, `3d-${key.toLowerCase()}`, 9))}
          {['F5', 'F6', 'F7', 'F8'].map((key, i) => renderKeycap3D(290 + i * 52, 10, 48, 36, key, `3d-${key.toLowerCase()}`, 9))}
          {['F9', 'F10', 'F11', 'F12'].map((key, i) => renderKeycap3D(510 + i * 52, 10, 48, 36, key, `3d-${key.toLowerCase()}`, 9))}
          {renderKeycap3D(730, 10, 48, 36, 'PRT', '3d-prt', 8)}
          {renderKeycap3D(785, 10, 48, 36, 'SCR', '3d-scr', 8)}
          {renderKeycap3D(840, 10, 48, 36, 'PAU', '3d-pau', 8)}
          
          {/* 第二行 - 数字键 */}
          {renderKeycap3D(10, 60, 48, 36, '~', '3d-tilde', 10)}
          {['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='].map((key, i) => renderKeycap3D(65 + i * 52, 60, 48, 36, key, `3d-${key}`, 10))}
          {renderKeycap3D(689, 60, 72, 36, 'BACK', '3d-backspace', 9)}
          {renderKeycap3D(785, 60, 48, 36, 'INS', '3d-ins', 8)}
          {renderKeycap3D(840, 60, 48, 36, 'HM', '3d-home', 8)}
          {renderKeycap3D(895, 60, 48, 36, 'UP', '3d-pageup', 8)}
          
          {/* 第三行 - QWERTY */}
          {renderKeycap3D(10, 105, 72, 36, 'TAB', '3d-tab', 9)}
          {['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '[', ']', '\\'].map((key, i) => renderKeycap3D(87 + i * 48, 105, 48, 36, key, `3d-${key.toLowerCase()}`, 10))}
          {renderKeycap3D(785, 105, 48, 36, 'DEL', '3d-del', 8)}
          {renderKeycap3D(840, 105, 48, 36, 'END', '3d-end', 8)}
          {renderKeycap3D(895, 105, 48, 36, 'DN', '3d-pagedown', 8)}
          
          {/* 第四行 - ASDF */}
          {renderKeycap3D(10, 150, 84, 36, 'CAPS', '3d-caps', 8)}
          {['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', ';', "'"].map((key, i) => renderKeycap3D(99 + i * 48, 150, 48, 36, key, `3d-${key.toLowerCase()}`, 10))}
          {renderKeycap3D(627, 150, 84, 36, 'ENTER', '3d-enter', 9)}
          
          {/* 第五行 - ZXCV */}
          {renderKeycap3D(10, 195, 108, 36, 'SHIFT', '3d-lshift', 8)}
          {['Z', 'X', 'C', 'V', 'B', 'N', 'M', ',', '.', '/'].map((key, i) => renderKeycap3D(123 + i * 48, 195, 48, 36, key, `3d-${key.toLowerCase()}`, 10))}
          {renderKeycap3D(603, 195, 108, 36, 'SHIFT', '3d-rshift', 8)}
          
          {/* 第六行 - 底部控制键 */}
          {renderKeycap3D(10, 240, 60, 36, 'CTRL', '3d-lctrl', 8)}
          {renderKeycap3D(75, 240, 60, 36, 'WIN', '3d-lwin', 8)}
          {renderKeycap3D(140, 240, 60, 36, 'ALT', '3d-lalt', 8)}
          {renderKeycap3D(205, 240, 264, 36, 'SPACE', '3d-space', 10)}
          {renderKeycap3D(474, 240, 60, 36, 'ALT', '3d-ralt', 8)}
          {renderKeycap3D(539, 240, 60, 36, 'FN', '3d-fn', 8)}
          {renderKeycap3D(604, 240, 60, 36, 'MENU', '3d-menu', 8)}
          {renderKeycap3D(669, 240, 60, 36, 'CTRL', '3d-rctrl', 8)}
          {renderKeycap3D(785, 240, 48, 36, '←', '3d-left', 12)}
          {renderKeycap3D(840, 240, 48, 36, '↓', '3d-down', 12)}
          {renderKeycap3D(895, 240, 48, 36, '→', '3d-right', 12)}
        </div>
      );

    case '61':
    case '60':
      return (
        <div className="relative" style={{ width: '750px', height: '250px' }}>
          {/* 第一行 - 数字键 */}
          {renderKeycap3D(10, 10, 48, 36, '~', '3d-tilde', 10)}
          {['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='].map((key, i) => renderKeycap3D(65 + i * 52, 10, 48, 36, key, `3d-${key}`, 10))}
          {renderKeycap3D(689, 10, 72, 36, 'BACK', '3d-backspace', 9)}
          
          {/* 第二行 - QWERTY */}
          {renderKeycap3D(10, 55, 72, 36, 'TAB', '3d-tab', 9)}
          {['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '[', ']', '\\'].map((key, i) => renderKeycap3D(87 + i * 48, 55, 48, 36, key, `3d-${key.toLowerCase()}`, 10))}
          
          {/* 第三行 - ASDF */}
          {renderKeycap3D(10, 100, 84, 36, 'CAPS', '3d-caps', 8)}
          {['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', ';', "'"].map((key, i) => renderKeycap3D(99 + i * 48, 100, 48, 36, key, `3d-${key.toLowerCase()}`, 10))}
          {renderKeycap3D(627, 100, 84, 36, 'ENTER', '3d-enter', 9)}
          
          {/* 第四行 - ZXCV */}
          {renderKeycap3D(10, 145, 108, 36, 'SHIFT', '3d-lshift', 8)}
          {['Z', 'X', 'C', 'V', 'B', 'N', 'M', ',', '.', '/'].map((key, i) => renderKeycap3D(123 + i * 48, 145, 48, 36, key, `3d-${key.toLowerCase()}`, 10))}
          {renderKeycap3D(603, 145, 108, 36, 'SHIFT', '3d-rshift', 8)}
          
          {/* 第五行 - 底部控制键 */}
          {renderKeycap3D(10, 190, 60, 36, 'CTRL', '3d-lctrl', 8)}
          {renderKeycap3D(75, 190, 60, 36, 'WIN', '3d-lwin', 8)}
          {renderKeycap3D(140, 190, 60, 36, 'ALT', '3d-lalt', 8)}
          {renderKeycap3D(205, 190, 264, 36, 'SPACE', '3d-space', 10)}
          {renderKeycap3D(474, 190, 60, 36, 'ALT', '3d-ralt', 8)}
          {renderKeycap3D(539, 190, 60, 36, 'FN', '3d-fn', 8)}
          {renderKeycap3D(604, 190, 60, 36, 'MENU', '3d-menu', 8)}
          {renderKeycap3D(669, 190, 60, 36, 'CTRL', '3d-rctrl', 8)}
        </div>
      );
      
    default:
      return null;
  }
};

export default function MyDesignsPage() {
  const [activeTab, setActiveTab] = useState('drafts');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [showOrderMenu, setShowOrderMenu] = useState(false);
  const [designs, setDesigns] = useState<Design[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDesignForPreview, setSelectedDesignForPreview] = useState<Design | null>(null);
  const [selected3DDesign, setSelected3DDesign] = useState<Design | null>(null);
  const [showShareModal, setShowShareModal] = useState<Design | null>(null);
  const [categories, setCategories] = useState<any[]>([]);
  const [topics, setTopics] = useState<any[]>([]);
  const [shareForm, setShareForm] = useState({
    categoryId: '',
    topicId: '',
    title: '',
    content: ''
  });

  // 侧边栏菜单项 - 统一深色主题风格
  const menu = [
    { 
      name: '仪表盘', 
      href: '/dashboard', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
        </svg>
      ), 
      active: false, 
      gradient: 'from-blue-500/20 to-cyan-500/20',
      borderColor: 'border-blue-500/30'
    },
    { 
      name: '设计器', 
      href: '/designer', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z" />
        </svg>
      ), 
      gradient: 'from-purple-500/20 to-pink-500/20',
      borderColor: 'border-purple-500/30'
    },
    { 
      name: '我的设计', 
      href: '/designs', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ), 
      active: true, 
      gradient: 'from-emerald-500/20 to-teal-500/20',
      borderColor: 'border-emerald-500/30'
    },
    { 
      name: '我的素材', 
      href: '/materials/my', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 5H6a2 2 0 00-2 2v6a2 2 0 002 2h2m0-10h8a2 2 0 012 2v6a2 2 0 01-2 2h-8m0-10v10" />
        </svg>
      ), 
      gradient: 'from-orange-500/20 to-red-500/20',
      borderColor: 'border-orange-500/30'
    },
    { 
      name: '素材库', 
      href: '/materials', 
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ), 
      gradient: 'from-indigo-500/20 to-purple-500/20',
      borderColor: 'border-indigo-500/30'
    },
  ];

  // 加载用户设计
  const loadUserDesigns = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.log('用户未登录，跳过设计加载');
        setLoading(false);
        return;
      }

      console.log('🔍 开始加载用户设计...');

      const response = await fetch('http://localhost:8080/api/designs/my', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.log('❌ 用户认证失败，跳过设计加载');
          setLoading(false);
          return;
        }
        throw new Error(`获取设计失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('📋 获取设计API完整响应:', result);

      if (result.code === 200) {
        setDesigns(result.data);
        console.log('🎉 从数据库加载用户设计成功，数量：', result.data.length);
      } else {
        throw new Error(result.message || '获取设计失败');
      }

    } catch (error) {
      console.error('❌ 加载用户设计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时获取用户设计
  useEffect(() => {
    loadUserDesigns();
    loadCategoriesAndTopics();
  }, []);

  // 加载分区和话题数据
  const loadCategoriesAndTopics = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      // 加载分区
      const categoriesResponse = await fetch('http://localhost:8080/api/community/categories', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (categoriesResponse.ok) {
        const categoriesResult = await categoriesResponse.json();
        if (categoriesResult.code === 200) {
          setCategories(categoriesResult.data);
        }
      }

      // 加载话题
      const topicsResponse = await fetch('http://localhost:8080/api/community/topics', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (topicsResponse.ok) {
        const topicsResult = await topicsResponse.json();
        if (topicsResult.code === 200) {
          setTopics(topicsResult.data);
        }
      }
    } catch (error) {
      console.error('加载分区和话题失败:', error);
    }
  };

  // 分享设计到社区
  const shareDesignToCommunity = async () => {
    if (!showShareModal) return;
    
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      const shareData = {
        categoryId: parseInt(shareForm.categoryId),
        topicId: shareForm.topicId ? parseInt(shareForm.topicId) : null,
        designId: parseInt(showShareModal.designId),
        title: shareForm.title,
        content: shareForm.content,
        postType: 'SHARE_DESIGN'
      };

      const response = await fetch('http://localhost:8080/api/community/posts', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(shareData)
      });

      if (!response.ok) {
        throw new Error('分享失败');
      }

      const result = await response.json();
      if (result.code === 200) {
        alert('分享成功！');
        setShowShareModal(null);
        setShareForm({ categoryId: '', topicId: '', title: '', content: '' });
      } else {
        throw new Error(result.message || '分享失败');
      }
    } catch (error) {
      console.error('分享失败:', error);
      alert('分享失败，请重试');
    }
  };

  // 打开分享模态框时加载数据和自动填充
  const openShareModal = async (design: Design) => {
    console.log('🚀 打开分享模态框，设计信息:', design);
    setShowShareModal(design);
    
    // 自动填充标题
    setShareForm({
      categoryId: '',
      topicId: '',
      title: `分享我的键帽设计：${design.designName}`,
      content: `这是我设计的键帽作品"${design.designName}"，${design.description || '希望大家喜欢！'}`
    });

    // 重新加载分区和话题数据（确保最新）
    try {
      console.log('📂 开始加载分区和话题数据...');
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
        console.log('🔑 使用Token:', token.substring(0, 20) + '...');
      } else {
        console.log('⚠️ 没有找到Token，使用匿名请求');
      }

      // 加载分区
      console.log('📂 正在请求分区数据...');
      const categoriesResponse = await fetch('http://localhost:8080/api/community/categories', {
        headers
      });
      console.log('📂 分区响应状态:', categoriesResponse.status);
      
      if (categoriesResponse.ok) {
        const categoriesResult = await categoriesResponse.json();
        console.log('📂 分区数据结果:', categoriesResult);
        if (categoriesResult.code === 200) {
          setCategories(categoriesResult.data);
          console.log('✅ 分区加载成功，数量:', categoriesResult.data.length);
          console.log('📂 分区详细数据:', categoriesResult.data);
        } else {
          console.error('❌ 分区API返回错误:', categoriesResult);
        }
      } else {
        const errorText = await categoriesResponse.text();
        console.error('❌ 分区请求失败:', categoriesResponse.status, errorText);
      }

      // 加载话题
      console.log('🏷️ 正在请求话题数据...');
      const topicsResponse = await fetch('http://localhost:8080/api/community/topics', {
        headers
      });
      console.log('🏷️ 话题响应状态:', topicsResponse.status);
      
      if (topicsResponse.ok) {
        const topicsResult = await topicsResponse.json();
        console.log('🏷️ 话题数据结果:', topicsResult);
        if (topicsResult.code === 200) {
          setTopics(topicsResult.data);
          console.log('✅ 话题加载成功，数量:', topicsResult.data.length);
          console.log('🏷️ 话题详细数据:', topicsResult.data);
        } else {
          console.error('❌ 话题API返回错误:', topicsResult);
        }
      } else {
        const errorText = await topicsResponse.text();
        console.error('❌ 话题请求失败:', topicsResponse.status, errorText);
      }
    } catch (error) {
      console.error('❌ 加载分区和话题失败:', error);
    }
  };

  // 删除设计
  const deleteDesign = async (designId: string) => {
    if (!confirm('确定要删除这个设计吗？此操作不可撤销。')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('请先登录');
        return;
      }

      const response = await fetch(`http://localhost:8080/api/designs/${designId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`删除失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.code === 200) {
        alert('设计删除成功');
        // 重新加载设计列表
        await loadUserDesigns();
      } else {
        throw new Error(result.message || '删除失败');
      }

    } catch (error) {
      console.error('删除设计失败:', error);
      alert(`删除设计失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 根据状态过滤设计
  const getFilteredDesigns = () => {
    switch (activeTab) {
      case 'drafts':
        return designs.filter(design => design.status === 'DRAFT');
      case 'completed':
        return designs.filter(design => design.status === 'PUBLISHED' && !design.isPublished);
      case 'published':
        return designs.filter(design => design.status === 'PUBLISHED' && design.isPublished);
      default:
        return [];
    }
  };

  const tabs = [
    { id: 'drafts', name: '草稿', icon: '📝', count: designs.filter(d => d.status === 'DRAFT').length },
    { id: 'completed', name: '已完成', icon: '✅', count: designs.filter(d => d.status === 'PUBLISHED' && !d.isPublished).length },
    { id: 'published', name: '已发布', icon: '🚀', count: designs.filter(d => d.status === 'PUBLISHED' && d.isPublished).length }
  ];

  // 处理点击外部区域关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.order-menu-container')) {
        setShowOrderMenu(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setShowOrderMenu(false);
        // 关闭3D预览
        if (selected3DDesign) {
          setSelected3DDesign(null);
        }
        // 关闭设计预览
        if (selectedDesignForPreview) {
          setSelectedDesignForPreview(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [selected3DDesign, selectedDesignForPreview]);

  // 格式化时间
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 解析设计数据获取进度（模拟）
  const getDesignProgress = (designData: string) => {
    try {
      const data = JSON.parse(designData);
      // 根据设计元素数量估算进度
      const elementsCount = data.elements?.length || 0;
      return Math.min(Math.max(elementsCount * 20, 10), 100);
    } catch {
      return 30; // 默认进度
    }
  };

  const renderDesignCard = (design: Design) => {
    const isCompleted = design.status === 'PUBLISHED';
    const isPublished = design.isPublished;
    
    if (activeTab === 'drafts') {
      return (
        <div key={design.designId} className="bg-gray-800/80 backdrop-blur-sm border border-gray-700/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
          <div className="relative h-56 bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center overflow-hidden">
            {/* 键盘设计预览 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <KeyboardPreview 
                designData={design.designData}
                layoutId={design.layoutId.toString()}
                width={480}
                height={280}
                className="drop-shadow-lg"
              />
            </div>
            
            {/* 标题覆盖 */}
            <div className="absolute top-3 left-3 right-3 z-20">
              <div className="text-white font-bold text-lg drop-shadow-md">{design.designName}</div>
            </div>
            
            {/* 进度条 */}
            <div className="absolute bottom-4 left-4 right-4 z-20">
              <div className="bg-white/30 rounded-full h-2">
                <div 
                  className="bg-white rounded-full h-2 transition-all duration-300"
                  style={{ width: `${getDesignProgress(design.designData)}%` }}
                ></div>
              </div>
              <div className="text-white text-xs mt-1">进度: {getDesignProgress(design.designData)}%</div>
            </div>

            <div className="absolute top-3 right-3 bg-black/30 backdrop-blur-sm rounded-full px-3 py-1 z-20">
              <span className="text-white text-xs font-medium">草稿</span>
            </div>
          </div>
          
          <div className="p-6">
            <h3 className="text-lg font-bold text-white mb-2 group-hover:text-green-400 transition-colors">
              {design.designName}
            </h3>
            <p className="text-gray-300 text-sm mb-4">{design.description}</p>
            <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
              <span>📅 {formatDate(design.updateTime)}</span>
            </div>
            <div className="flex space-x-2">
              <Link 
                href={`/designer?id=${design.designId}`} 
                className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors text-center"
              >
                继续编辑
              </Link>
              <button 
                onClick={() => {
                  // 打开设计预览模态框
                  setSelectedDesignForPreview(design);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-1"
                title="预览设计"
              >
                <span>👁️</span>
                <span className="hidden sm:inline">预览</span>
              </button>
              <button 
                onClick={() => {
                  // 打开3D预览
                  setSelected3DDesign(design);
                }}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-1"
                title="3D预览"
              >
                <span>🎮</span>
                <span className="hidden sm:inline">3D</span>
              </button>
              <button 
                onClick={() => deleteDesign(design.designId)}
                className="px-3 py-2 border border-red-400/50 text-red-400 rounded-lg hover:bg-red-500/20 transition-colors"
                title="删除设计"
              >
                <span>🗑️</span>
              </button>
            </div>
          </div>
        </div>
      );
    } else if (activeTab === 'completed') {
      return (
        <div key={design.designId} className="bg-gray-800/80 backdrop-blur-sm border border-gray-700/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
          <div className="relative h-56 bg-gradient-to-br from-blue-400 to-teal-500 flex items-center justify-center overflow-hidden">
            {/* 键盘设计预览 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <KeyboardPreview 
                designData={design.designData}
                layoutId={design.layoutId.toString()}
                width={480}
                height={280}
                className="drop-shadow-lg"
              />
            </div>
            
            {/* 标题覆盖 */}
            <div className="absolute top-3 left-3 right-3 z-20">
              <div className="text-white font-bold text-lg drop-shadow-md">{design.designName}</div>
            </div>

            <div className="absolute top-3 right-3 bg-green-500/80 backdrop-blur-sm rounded-full px-2 py-1 z-20">
              <span className="text-white text-xs font-medium">✅ 完成</span>
            </div>
          </div>
          
          <div className="p-6">
            <h3 className="text-lg font-bold text-white mb-2 group-hover:text-blue-400 transition-colors">
              {design.designName}
            </h3>
            <p className="text-gray-300 text-sm mb-4">{design.description}</p>
            <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
              <span>📅 完成于 {formatDate(design.createTime)}</span>
            </div>
            <div className="flex space-x-2">
              <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                发布到社区
              </button>
              <button 
                onClick={() => {
                  setSelectedDesignForPreview(design);
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-1"
                title="预览设计"
              >
                <span>👁️</span>
              </button>
              <button 
                onClick={() => {
                  setSelected3DDesign(design);
                }}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-1"
                title="3D预览"
              >
                <span>🎮</span>
              </button>
              <Link 
                href={`/designer?id=${design.designId}`}
                className="px-4 py-2 border border-gray-600/50 text-gray-300 rounded-lg hover:bg-gray-700/50 transition-colors"
              >
                编辑
              </Link>
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div key={design.designId} className="bg-gray-800/80 backdrop-blur-sm border border-gray-700/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
          <div className="relative h-56 bg-gradient-to-br from-cyan-400 to-pink-500 flex items-center justify-center overflow-hidden">
            {/* 键盘设计预览 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <KeyboardPreview 
                designData={design.designData}
                layoutId={design.layoutId.toString()}
                width={480}
                height={280}
                className="drop-shadow-lg"
              />
            </div>
            
            {/* 标题覆盖 */}
            <div className="absolute top-3 left-3 right-3 z-20">
              <div className="text-white font-bold text-lg drop-shadow-md">{design.designName}</div>
            </div>

            <div className="absolute top-3 right-3 bg-orange-500/80 backdrop-blur-sm rounded-full px-2 py-1 z-20">
              <span className="text-white text-xs font-medium">🔥 公开</span>
            </div>

            <div className="absolute bottom-3 left-3 right-3 flex items-center justify-between z-20">
              <div className="bg-black/30 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
                <span className="text-white text-xs">❤️</span>
                <span className="text-white text-xs font-medium">{design.likeCount}</span>
              </div>
              <div className="bg-black/30 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
                <span className="text-white text-xs">👁️</span>
                <span className="text-white text-xs font-medium">{design.viewCount}</span>
              </div>
            </div>
          </div>
          
          <div className="p-6">
            <h3 className="text-lg font-bold text-white mb-2 group-hover:text-purple-400 transition-colors">
              {design.designName}
            </h3>
            <p className="text-gray-300 text-sm mb-4">{design.description}</p>
            <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
              <span>📅 发布于 {formatDate(design.createTime)}</span>
              <div className="flex items-center space-x-3">
                <span>❤️ {design.likeCount}</span>
                <span>👁️ {design.viewCount}</span>
              </div>
            </div>
            <div className="flex space-x-2">
              <Link 
                href={`/community/design/${design.designId}`} 
                className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors text-center"
              >
                查看详情
              </Link>
              <button 
                onClick={() => {
                  setSelectedDesignForPreview(design);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-1"
                title="预览设计"
              >
                <span>👁️</span>
              </button>
              <button 
                onClick={() => {
                  setSelected3DDesign(design);
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-1"
                title="3D预览"
              >
                <span>🎮</span>
              </button>
              <button 
                onClick={() => deleteDesign(design.designId)}
                className="px-3 py-2 border border-red-400/50 text-red-400 rounded-lg hover:bg-red-500/20 transition-colors"
                title="删除设计"
              >
                <span>🗑️</span>
              </button>
            </div>
          </div>
        </div>
      );
    }
  };

  // 当前显示的数据
  const currentData = getFilteredDesigns();

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900">
      {/* 现代化侧边栏 - 深色主题 */}
      <aside className={`${sidebarOpen ? 'w-72' : 'w-20'} bg-gray-800/95 backdrop-blur-xl border-r border-gray-700/50 transition-all duration-300 ease-in-out fixed left-0 top-0 h-screen overflow-y-auto scrollbar-thin shadow-xl z-50`}>
        <div className="p-6 flex justify-between items-center">
          {sidebarOpen && (
            <Link href="/" className="flex items-center space-x-3 hover-float">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center text-white font-bold text-xs shadow-md">
                Keycap
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-medium text-white">
                  Keycap
                </span>
                <span className="text-xs text-gray-400 font-normal -mt-1">
                  Keycap Design Platform
                </span>
              </div>
            </Link>
          )}
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 rounded-lg bg-gray-700/30 text-gray-300 hover:text-white hover:bg-gray-600/40 transition-all duration-200"
          >
            {sidebarOpen ? '◀' : '▶'}
          </button>
        </div>
        
        <nav className="mt-8 px-4">
          <ul className="space-y-3">
            {menu.map((item) => (
              <li key={item.name}>
                <Link 
                  href={item.href}
                  className={`group flex items-center ${sidebarOpen ? 'px-4' : 'px-3'} py-3 rounded-xl transition-all duration-300 border backdrop-blur-sm ${
                    item.active 
                      ? `bg-gradient-to-r ${item.gradient} text-white shadow-lg border-white/10 hover:shadow-xl` 
                      : 'text-gray-400 hover:text-white hover:bg-gray-700/20 border-transparent hover:border-gray-600/30'
                  }`}
                >
                  <div className={`flex items-center justify-center w-6 h-6 ${
                    item.active ? 'text-white' : 'text-gray-400 group-hover:text-white'
                  } transition-colors duration-300`}>
                    {item.icon}
                  </div>
                  {sidebarOpen && (
                    <>
                      <span className="ml-3 font-medium tracking-wide">{item.name}</span>
                      {item.active && (
                        <div className="ml-auto">
                          <div className="w-2 h-2 rounded-full bg-white/80 shadow-sm"></div>
                        </div>
                      )}
                    </>
                  )}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </aside>

      {/* 主内容区 */}
      <div className={`flex-1 overflow-auto ${sidebarOpen ? 'ml-72' : 'ml-20'} transition-all duration-300 ease-in-out`}>
        {/* 顶部导航 */}
        <header className="bg-gray-800/80 backdrop-blur-md border-b border-gray-700/50 px-6 py-4 sticky top-0 z-40">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-white">我的设计</h1>
              <p className="text-gray-300 mt-1">管理您的创作作品和设计进度 ✨</p>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="relative order-menu-container">
                <button 
                  onClick={() => setShowOrderMenu(!showOrderMenu)}
                  className="flex items-center space-x-2 px-4 py-2 rounded-full bg-gray-700/50 text-gray-300 hover:text-white hover:bg-gray-600/50 transition-all duration-200"
                >
                  <span>📋</span>
                  <span className="hidden sm:inline">我的订单</span>
                  <span className={`transform transition-transform ${showOrderMenu ? 'rotate-180' : ''}`}>▼</span>
                </button>
                
                {showOrderMenu && (
                  <div className="absolute top-full right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-gray-700 py-2 z-50">
                    <Link href="/orders/paid" className="flex items-center space-x-2 px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
                      <span>✅</span>
                      <span>已支付订单</span>
                    </Link>
                    <Link href="/orders/pending" className="flex items-center space-x-2 px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
                      <span>⏳</span>
                      <span>待支付订单</span>
                    </Link>
                    <Link href="/cart" className="flex items-center space-x-2 px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
                      <span>🛒</span>
                      <span>购物车</span>
                    </Link>
                  </div>
                )}
              </div>
              

              
              <Link 
                href="/community" 
                className="flex items-center space-x-2 px-4 py-2 rounded-full bg-gray-700/50 text-gray-300 hover:text-white hover:bg-gray-600/50 transition-all duration-200"
              >
                <span>👥</span>
                <span className="hidden sm:inline">社区</span>
              </Link>
              
              <Link 
                href="/shop" 
                className="flex items-center space-x-2 px-4 py-2 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 text-white hover:from-cyan-600 hover:to-blue-700 transition-all duration-200"
              >
                <span>🛒</span>
                <span className="hidden sm:inline">商城</span>
              </Link>
              
              <div className="flex items-center space-x-3 px-4 py-2 rounded-full bg-gray-700/50">
                <span className="text-gray-300 text-sm">创作者</span>
                <div className="w-8 h-8 rounded-full overflow-hidden bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center">
                  {(() => {
                    // 获取用户信息
                    const userData = typeof window !== 'undefined' ? localStorage.getItem('user') : null;
                    const user = userData ? JSON.parse(userData) : null;
                    const avatarPath = user?.avatarPath;
                    const avatar = user?.nickname || user?.username || 'U';
                    
                    return avatarPath ? (
                      <img 
                        src={`http://localhost:8080${avatarPath}`} 
                        alt="用户头像" 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const fallback = target.parentElement?.querySelector('.fallback-avatar') as HTMLElement;
                          if (fallback) fallback.style.display = 'flex';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full text-white font-bold text-sm flex items-center justify-center fallback-avatar">
                        {avatar.charAt(0).toUpperCase()}
                      </div>
                    );
                  })()}
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* 主要内容 */}
        <main className="p-6 space-y-8 bg-gray-900/95 min-h-screen">
          {/* 标签页导航 */}
          <div className="bg-gray-800/80 backdrop-blur-sm rounded-xl shadow-lg border border-gray-700/50 p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex space-x-1 bg-gray-700/50 rounded-lg p-1">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all ${
                      activeTab === tab.id
                        ? 'bg-gray-600 text-green-400 shadow-sm'
                        : 'text-gray-400 hover:text-gray-200'
                    }`}
                  >
                    <span>{tab.icon}</span>
                    <span className="font-medium">{tab.name}</span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      activeTab === tab.id ? 'bg-green-400/20 text-green-400' : 'bg-gray-600/50 text-gray-400'
                    }`}>
                      {tab.count}
                    </span>
                  </button>
                ))}
              </div>
              
              <Link 
                href="/designer" 
                className="flex items-center space-x-2 bg-gradient-to-r from-green-500 to-teal-600 text-white px-6 py-3 rounded-lg hover:from-green-600 hover:to-teal-700 transition-all duration-200 shadow-lg"
              >
                <span>✨</span>
                <span>新建设计</span>
              </Link>
            </div>

            {/* 设计列表 */}
            {currentData.length === 0 ? (
              <div className="text-center py-16">
                <div className="text-6xl mb-4">
                  {activeTab === 'drafts' ? '📝' : activeTab === 'completed' ? '✅' : '🚀'}
                </div>
                <h3 className="text-xl font-medium text-white mb-2">
                  {activeTab === 'drafts' ? '暂无草稿' : activeTab === 'completed' ? '暂无已完成设计' : '暂无已发布设计'}
                </h3>
                <p className="text-gray-400 mb-6">
                  {activeTab === 'drafts' ? '开始您的第一个设计吧！' : activeTab === 'completed' ? '完成您的设计并保存' : '发布您的作品到社区'}
                </p>
                <Link 
                  href="/designer" 
                  className="inline-flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors shadow-lg"
                >
                  <span>🎨</span>
                  <span>开始设计</span>
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {currentData.map(renderDesignCard)}
              </div>
            )}
          </div>
        </main>
      </div>

      {/* 设计预览模态框 */}
      {selectedDesignForPreview && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-2xl shadow-2xl border border-gray-700/50 max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-700/50">
              <div>
                <h2 className="text-2xl font-bold text-white">{selectedDesignForPreview.designName}</h2>
                <p className="text-gray-300 mt-1">{selectedDesignForPreview.description}</p>
              </div>
              <button
                onClick={() => setSelectedDesignForPreview(null)}
                className="p-2 rounded-full hover:bg-gray-700 text-gray-300 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="p-6">
              <div className="bg-gray-900/50 rounded-xl p-8 flex items-center justify-center">
                <KeyboardPreview 
                  designData={selectedDesignForPreview.designData}
                  layoutId={selectedDesignForPreview.layoutId.toString()}
                  width={800}
                  height={400}
                  className="drop-shadow-xl"
                />
              </div>
              
              <div className="mt-6 flex items-center justify-between">
                <div className="flex items-center space-x-4 text-sm text-gray-400">
                  <span>📅 创建于 {formatDate(selectedDesignForPreview.createTime)}</span>
                  <span>🔄 更新于 {formatDate(selectedDesignForPreview.updateTime)}</span>
                  <span>❤️ {selectedDesignForPreview.likeCount} 喜欢</span>
                  <span>👁️ {selectedDesignForPreview.viewCount} 查看</span>
                </div>
                
                <div className="flex space-x-3">
                  <button
                    onClick={() => {
                      setSelected3DDesign(selectedDesignForPreview);
                      setSelectedDesignForPreview(null);
                    }}
                    className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
                  >
                    <span>🎮</span>
                    <span>3D预览</span>
                  </button>
                  <button
                    onClick={() => {
                      openShareModal(selectedDesignForPreview);
                      setSelectedDesignForPreview(null);
                    }}
                    className="px-4 py-2 bg-cyan-600 text-white rounded-lg hover:bg-cyan-700 transition-colors flex items-center space-x-2"
                  >
                    <span>🚀</span>
                    <span>分享到社区</span>
                  </button>
                  <Link
                    href={`/designer?id=${selectedDesignForPreview.designId}`}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
                  >
                    <span>✏️</span>
                    <span>编辑设计</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 3D预览模态框 */}
      {selected3DDesign && (
        <div className="fixed inset-0 bg-black flex items-center justify-center z-50">
          <div className="w-full h-full relative">
            {/* 顶部控制栏 */}
            <div className="absolute top-0 left-0 right-0 bg-black/80 backdrop-blur-sm p-4 z-10">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => setSelected3DDesign(null)}
                    className="text-white hover:text-gray-300 transition-colors p-2 rounded-lg hover:bg-white/10"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  <div>
                    <h2 className="text-xl font-semibold text-white">{selected3DDesign.designName}</h2>
                    <p className="text-gray-300 text-sm">3D预览模式 - 拖拽鼠标旋转视角</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="text-white text-sm bg-white/10 rounded-full px-3 py-1 flex items-center space-x-2">
                    <span>🖱️</span>
                    <span>拖拽旋转</span>
                  </div>
                  <div className="text-white text-sm bg-white/10 rounded-full px-3 py-1 flex items-center space-x-2">
                    <span>🎮</span>
                    <span>滚轮缩放</span>
                  </div>
                  <button
                    onClick={() => setSelected3DDesign(null)}
                    className="text-white hover:text-gray-300 transition-colors px-3 py-1 rounded-full bg-white/10 hover:bg-white/20"
                  >
                    ESC 退出
                  </button>
                </div>
              </div>
            </div>

            {/* 3D预览区域 */}
            <div className="w-full h-full bg-gradient-to-br from-gray-900 via-black to-gray-800 flex items-center justify-center overflow-hidden">
              {/* 3D场景容器 */}
              <div 
                className="transform-gpu cursor-grab active:cursor-grabbing"
                style={{
                  transformStyle: 'preserve-3d',
                  transform: `perspective(4000px) rotateX(-35deg) rotateY(25deg) scale(0.7)`,
                  transition: 'transform 0.1s ease-out'
                }}
                onMouseDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  const startX = e.clientX;
                  const startY = e.clientY;
                  let rotationX = -35;
                  let rotationY = 25;
                  let scale = 0.7;

                  const handleMouseMove = (moveEvent: MouseEvent) => {
                    moveEvent.preventDefault();
                    const deltaX = moveEvent.clientX - startX;
                    const deltaY = moveEvent.clientY - startY;
                    
                    rotationX = Math.max(-80, Math.min(10, -35 - deltaY * 0.5));
                    rotationY = 25 + deltaX * 0.7;
                    
                    (e.target as HTMLElement).style.transform = 
                      `perspective(4000px) rotateX(${rotationX}deg) rotateY(${rotationY}deg) scale(${scale})`;
                  };

                  const handleMouseUp = (upEvent: MouseEvent) => {
                    upEvent.preventDefault();
                    document.removeEventListener('mousemove', handleMouseMove);
                    document.removeEventListener('mouseup', handleMouseUp);
                  };

                  document.addEventListener('mousemove', handleMouseMove, { passive: false });
                  document.addEventListener('mouseup', handleMouseUp, { passive: false });
                }}
                onWheel={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  const delta = e.deltaY > 0 ? -0.1 : 0.1;
                  const currentTransform = (e.target as HTMLElement).style.transform;
                  const scaleMatch = currentTransform.match(/scale\(([\d.]+)\)/);
                  const currentScale = scaleMatch ? parseFloat(scaleMatch[1]) : 0.7;
                  const newScale = Math.max(0.3, Math.min(1.8, currentScale + delta));
                  
                  const rotXMatch = currentTransform.match(/rotateX\(([-\d.]+)deg\)/);
                  const rotYMatch = currentTransform.match(/rotateY\(([-\d.]+)deg\)/);
                  const rotX = rotXMatch ? rotXMatch[1] : '-35';
                  const rotY = rotYMatch ? rotYMatch[1] : '25';
                  
                  (e.target as HTMLElement).style.transform = 
                    `perspective(4000px) rotateX(${rotX}deg) rotateY(${rotY}deg) scale(${newScale})`;
                }}
              >
                {/* 键盘底座阴影 */}
                <div 
                  className="absolute bg-black rounded-3xl opacity-20 blur-3xl"
                  style={{
                    width: '1400px',
                    height: '500px',
                    left: '-100px',
                    top: '400px',
                    transform: 'rotateX(90deg) translateZ(-80px)'
                  }}
                />
                
                {/* 键盘3D环境光 */}
                <div 
                  className="absolute"
                  style={{
                    width: '1300px',
                    height: '400px',
                    left: '-50px',
                    top: '-50px',
                    background: 'radial-gradient(ellipse at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%)',
                    transform: 'translateZ(-100px)',
                    borderRadius: '50%'
                  }}
                />
                
                {/* 3D键盘布局 */}
                <div
                  style={{
                    transformStyle: 'preserve-3d',
                    transform: 'translateZ(0px)',
                    filter: 'drop-shadow(0 20px 40px rgba(0,0,0,0.3))'
                  }}
                >
                  <Keyboard3DLayout 
                    layoutId={selected3DDesign.layoutId.toString()}
                    designElements={(() => {
                      try {
                        const parsedData = JSON.parse(selected3DDesign.designData || '{}');
                        return parsedData.elements || [];
                      } catch (error) {
                        console.error('Failed to parse design data:', error);
                        return [];
                      }
                    })()}
                  />
                </div>
              </div>
              
              {/* 旋转指示器 */}
              <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 text-center">
                <div className="bg-black/60 backdrop-blur-sm rounded-lg px-6 py-3 text-white">
                  <div className="flex items-center space-x-4 text-sm">
                    <span>🖱️ 拖拽旋转真实3D键盘</span>
                    <span>🔍 滚轮缩放立体视图</span>
                  </div>
                  <p className="text-xs text-gray-300 mt-1">真实立体键帽模型 | 六面体3D渲染 | ESC退出</p>
                </div>
              </div>
              
              {/* 环境光效 */}
              <div className="absolute inset-0 pointer-events-none overflow-hidden">
                <div className="absolute top-10 left-1/3 w-32 h-32 bg-blue-500/5 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute bottom-20 right-1/4 w-40 h-40 bg-purple-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
                <div className="absolute top-1/2 left-10 w-24 h-24 bg-cyan-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 分享到社区模态框 */}
      {showShareModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-2xl shadow-2xl border border-gray-700/50 max-w-2xl w-full max-h-[90vh] flex flex-col">
            <div className="flex items-center justify-between p-6 border-b border-gray-700/50 flex-shrink-0">
              <div>
                <h2 className="text-2xl font-bold text-white">分享到社区</h2>
                <p className="text-gray-300 mt-1">分享您的键帽设计作品给社区用户</p>
              </div>
              <button
                onClick={() => setShowShareModal(null)}
                className="p-2 rounded-full hover:bg-gray-700 text-gray-300 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="flex-1 overflow-y-auto p-6 space-y-6">
              {/* 设计预览 */}
              <div className="bg-gray-700/50 rounded-xl p-4">
                <h3 className="text-lg font-semibold text-white mb-3">设计预览</h3>
                <div className="bg-gray-900/50 rounded-lg p-4 flex items-center justify-center">
                  <KeyboardPreview 
                    designData={showShareModal.designData}
                    layoutId={showShareModal.layoutId.toString()}
                    width={400}
                    height={200}
                    className="drop-shadow-lg"
                  />
                </div>
                <p className="text-sm text-gray-300 mt-2">{showShareModal.designName}</p>
              </div>

              {/* 分享表单 */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">选择分区 *</label>
                  <select
                    value={shareForm.categoryId}
                    onChange={(e) => setShareForm({...shareForm, categoryId: e.target.value})}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  >
                    <option value="">请选择分区</option>
                    {categories.map((category) => (
                      <option key={category.categoryId} value={category.categoryId}>
                        {category.icon} {category.categoryName}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">选择话题</label>
                  <select
                    value={shareForm.topicId}
                    onChange={(e) => setShareForm({...shareForm, topicId: e.target.value})}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  >
                    <option value="">请选择话题（可选）</option>
                    {topics.map((topic) => (
                      <option key={topic.topicId} value={topic.topicId}>
                        {topic.topicName}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">帖子标题 *</label>
                  <input
                    type="text"
                    value={shareForm.title}
                    onChange={(e) => setShareForm({...shareForm, title: e.target.value})}
                    placeholder="为您的设计起个吸引人的标题"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">设计说明 *</label>
                  <textarea
                    value={shareForm.content}
                    onChange={(e) => setShareForm({...shareForm, content: e.target.value})}
                    placeholder="介绍一下您的设计理念、使用的技巧或者想要表达的内容..."
                    rows={4}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent resize-none"
                  />
                </div>
              </div>
            </div>
            
            {/* 底部按钮区域 - 固定在底部 */}
            <div className="flex items-center justify-between p-6 border-t border-gray-700/50 bg-gray-700/30 rounded-b-2xl flex-shrink-0">
              <p className="text-sm text-gray-400">分享后其他用户可以看到您的设计作品</p>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowShareModal(null)}
                  className="px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={shareDesignToCommunity}
                  disabled={!shareForm.categoryId || !shareForm.title || !shareForm.content}
                  className="px-6 py-2 bg-cyan-600 text-white rounded-lg hover:bg-cyan-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  <span>🚀</span>
                  <span>发布到社区</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 