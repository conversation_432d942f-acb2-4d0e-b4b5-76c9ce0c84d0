/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['localhost'],
    unoptimized: true
  },
  async rewrites() {
    return [
      {
        source: '/uploads/:path*',
        destination: 'http://localhost:8080/api/uploads/:path*',
      },
      {
        source: '/api/uploads/:path*',
        destination: 'http://localhost:8080/api/uploads/:path*',
      }
    ]
  },
}

module.exports = nextConfig 